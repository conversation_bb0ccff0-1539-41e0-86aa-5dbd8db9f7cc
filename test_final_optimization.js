const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار التحسينات النهائية الشاملة
 */
async function testFinalOptimization() {
    console.log('🎯 اختبار التحسينات النهائية الشاملة');
    console.log('=' .repeat(60));
    console.log('🔧 التحسينات النهائية المطبقة:');
    console.log('   ✅ عتبات متوازنة: High 60%, Medium 45%, Low 30%');
    console.log('   ✅ عتبات ديناميكية: 35-65% (كان 20-40%)');
    console.log('   ✅ أوزان متوازنة: Jaccard 40%, Cosine 40%, Levenshtein 15%, Semantic 5%');
    console.log('   ✅ خوارزميات محسنة للعبارات الطويلة');
    console.log('=' .repeat(60));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار المعايرة
    const testFiles = [
        { file: 'test_calibrated_20_percent.txt', expected: 20, description: 'نص مع 20% عبارات مرجعية' },
        { file: 'test_calibrated_60_percent.txt', expected: 60, description: 'نص مع 60% عبارات مرجعية' },
        { file: 'test_calibrated_90_percent.txt', expected: 90, description: 'نص مع 90% عبارات مرجعية' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 85; // معيار عالي للتحسينات النهائية
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   ⚙️ العتبة: ${(result.analysis.adaptiveThreshold * 100).toFixed(1)}%`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            // عرض تفاصيل التشابه
            if (result.analysis && result.analysis.similarities) {
                console.log(`   🔬 تفاصيل التشابه:`);
                console.log(`      Jaccard: ${(result.analysis.similarities.jaccard * 100).toFixed(1)}%`);
                console.log(`      Cosine: ${(result.analysis.similarities.cosine * 100).toFixed(1)}%`);
                console.log(`      Levenshtein: ${(result.analysis.similarities.levenshtein * 100).toFixed(1)}%`);
                console.log(`      Semantic: ${(result.analysis.similarities.semantic * 100).toFixed(1)}%`);
                console.log(`      Exact Match Bonus: ${(result.analysis.similarities.exactMatch * 100).toFixed(1)}%`);
            }
            
            // عرض الأوزان المستخدمة
            if (result.analysis && result.analysis.weights) {
                console.log(`   ⚖️ الأوزان المستخدمة:`);
                console.log(`      Jaccard: ${(result.analysis.weights.jaccard * 100).toFixed(1)}%`);
                console.log(`      Cosine: ${(result.analysis.weights.cosine * 100).toFixed(1)}%`);
                console.log(`      Levenshtein: ${(result.analysis.weights.levenshtein * 100).toFixed(1)}%`);
                console.log(`      Semantic: ${(result.analysis.weights.semantic * 100).toFixed(1)}%`);
            }
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                threshold: result.analysis.adaptiveThreshold,
                suspiciousCount: result.suspiciousSegments.length,
                similarities: result.analysis.similarities,
                weights: result.analysis.weights,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج النهائية
    console.log('\n' + '=' .repeat(60));
    console.log('📊 تحليل النتائج النهائية');
    console.log('=' .repeat(60));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgThreshold = validResults.reduce((sum, r) => sum + r.threshold, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`📈 النتائج النهائية:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (كان 52.0%)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (كان 33.3%)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
        console.log(`   متوسط العتبة: ${(avgThreshold * 100).toFixed(1)}%`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 52.0;
        const successImprovement = successRate - 33.3;
        
        console.log(`\n📊 التحسن الإجمالي:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تحليل متوسط التشابه والأوزان
        if (validResults[0] && validResults[0].similarities) {
            console.log(`\n🔬 متوسط قيم التشابه:`);
            const avgSimilarities = {
                jaccard: validResults.reduce((sum, r) => sum + (r.similarities?.jaccard || 0), 0) / validResults.length,
                cosine: validResults.reduce((sum, r) => sum + (r.similarities?.cosine || 0), 0) / validResults.length,
                levenshtein: validResults.reduce((sum, r) => sum + (r.similarities?.levenshtein || 0), 0) / validResults.length,
                semantic: validResults.reduce((sum, r) => sum + (r.similarities?.semantic || 0), 0) / validResults.length,
                exactMatch: validResults.reduce((sum, r) => sum + (r.similarities?.exactMatch || 0), 0) / validResults.length
            };
            
            console.log(`   Jaccard: ${(avgSimilarities.jaccard * 100).toFixed(1)}%`);
            console.log(`   Cosine: ${(avgSimilarities.cosine * 100).toFixed(1)}%`);
            console.log(`   Levenshtein: ${(avgSimilarities.levenshtein * 100).toFixed(1)}%`);
            console.log(`   Semantic: ${(avgSimilarities.semantic * 100).toFixed(1)}%`);
            console.log(`   Exact Match Bonus: ${(avgSimilarities.exactMatch * 100).toFixed(1)}%`);
        }
        
        if (validResults[0] && validResults[0].weights) {
            console.log(`\n⚖️ متوسط الأوزان المستخدمة:`);
            const avgWeights = {
                jaccard: validResults.reduce((sum, r) => sum + (r.weights?.jaccard || 0), 0) / validResults.length,
                cosine: validResults.reduce((sum, r) => sum + (r.weights?.cosine || 0), 0) / validResults.length,
                levenshtein: validResults.reduce((sum, r) => sum + (r.weights?.levenshtein || 0), 0) / validResults.length,
                semantic: validResults.reduce((sum, r) => sum + (r.weights?.semantic || 0), 0) / validResults.length
            };
            
            console.log(`   Jaccard: ${(avgWeights.jaccard * 100).toFixed(1)}%`);
            console.log(`   Cosine: ${(avgWeights.cosine * 100).toFixed(1)}%`);
            console.log(`   Levenshtein: ${(avgWeights.levenshtein * 100).toFixed(1)}%`);
            console.log(`   Semantic: ${(avgWeights.semantic * 100).toFixed(1)}%`);
        }
        
        // تقييم تحقيق الأهداف النهائية
        console.log(`\n🎯 تقييم تحقيق الأهداف النهائية:`);
        console.log(`   دقة 95%+: ${avgAccuracy >= 95 ? '✅' : '❌'} (${avgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 90%+: ${successRate >= 90 ? '✅' : '❌'} (${successRate.toFixed(1)}%)`);
        console.log(`   أداء < 1000ms: ${avgTime < 1000 ? '✅' : '❌'} (${avgTime.toFixed(0)}ms)`);
        
        const targetsAchieved = [
            avgAccuracy >= 95,
            successRate >= 90,
            avgTime < 1000
        ].filter(Boolean).length;
        
        console.log(`   الأهداف المحققة: ${targetsAchieved}/3`);
        
        // تقييم نهائي شامل
        let finalGrade;
        if (targetsAchieved === 3) {
            finalGrade = 'A+ (ممتاز) - تم تحقيق جميع الأهداف المطلوبة';
        } else if (avgAccuracy >= 90 && successRate >= 80) {
            finalGrade = 'A (جيد جداً) - قريب جداً من الأهداف';
        } else if (avgAccuracy >= 80 && successRate >= 70) {
            finalGrade = 'B+ (جيد) - تحسن كبير ومقبول';
        } else if (avgAccuracy >= 70 && successRate >= 50) {
            finalGrade = 'B (مقبول) - تحسن ملحوظ';
        } else {
            finalGrade = 'C (يحتاج تطوير) - تحسن محدود';
        }
        
        console.log(`\n🏆 التقييم النهائي: ${finalGrade}`);
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج:`);
        validResults.forEach(result => {
            const grade = result.accuracy >= 95 ? '🎯 ممتاز' : 
                         result.accuracy >= 85 ? '✅ جيد جداً' : 
                         result.accuracy >= 70 ? '📈 جيد' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${grade}`);
        });
        
        // حفظ النتائج النهائية
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'final_optimization_test',
            description: 'اختبار التحسينات النهائية الشاملة للوصول لدقة 95%+',
            final_settings: {
                thresholds: { high: 60, medium: 45, low: 30 },
                dynamic_thresholds: '35-65%',
                weights: { jaccard: 40, cosine: 40, levenshtein: 15, semantic: 5 }
            },
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgThreshold: avgThreshold * 100,
                targetsAchieved: targetsAchieved,
                finalGrade: finalGrade,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results
        };
        
        fs.writeFileSync('final_optimization_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير النهائي: final_optimization_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            targetsAchieved,
            finalGrade,
            improvement: accuracyImprovement
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار النهائي
async function main() {
    try {
        const results = await testFinalOptimization();
        
        if (results) {
            console.log('\n🎯 الخلاصة النهائية الشاملة:');
            console.log(`   الدقة النهائية: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن الإجمالي: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(0)}ms`);
            console.log(`   الأهداف المحققة: ${results.targetsAchieved}/3`);
            console.log(`   التقييم النهائي: ${results.finalGrade}`);
            
            if (results.targetsAchieved === 3) {
                console.log('\n🎉 تم تحقيق جميع الأهداف المطلوبة! النظام جاهز للإنتاج');
            } else if (results.avgAccuracy >= 90) {
                console.log('\n🎯 دقة ممتازة! قريب جداً من الهدف النهائي');
            } else if (results.avgAccuracy >= 80) {
                console.log('\n✅ تحسن كبير! النظام يعمل بكفاءة جيدة');
            } else {
                console.log('\n🔧 تحسن ملحوظ لكن يحتاج مزيد من العمل');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار النهائي:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testFinalOptimization };
