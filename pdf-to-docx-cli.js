#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const PDFToDocxConverter = require('./src/modules/pdfToDocxConverter');

/**
 * واجهة سطر الأوامر لمحول PDF إلى DOCX
 * استخدام: node pdf-to-docx-cli.js [options]
 */

// عرض المساعدة
function showHelp() {
    console.log(`
🔄 محول PDF إلى DOCX المتقدم
=====================================

الاستخدام:
  node pdf-to-docx-cli.js [خيارات]

الخيارات:
  -i, --input <file>      ملف PDF المدخل
  -o, --output <file>     ملف DOCX الناتج
  -d, --dir <directory>   مجلد يحتوي على ملفات PDF للتحويل المتعدد
  -od, --output-dir <dir> مجلد الحفظ للتحويل المتعدد
  -t, --title <title>     عنوان المستند
  -h, --help              عرض هذه المساعدة
  -v, --version           عرض معلومات الإصدار
  -info, --support-info   عرض معلومات الدعم

أمثلة:
  # تحويل ملف واحد
  node pdf-to-docx-cli.js -i document.pdf -o document.docx

  # تحويل ملف مع عنوان مخصص
  node pdf-to-docx-cli.js -i research.pdf -o research.docx -t "بحث أكاديمي"

  # تحويل متعدد الملفات
  node pdf-to-docx-cli.js -d ./pdfs -od ./docx-output

الميزات:
  ✅ دعم شامل للعربية والإنجليزية
  ✅ استخدام الحل الجذري لاستخراج النصوص العربية
  ✅ الحفاظ على التنسيق والهيكل
  ✅ تحديد تلقائي للغة واتجاه النص
  ✅ معالجة العناوين والفقرات والقوائم
  ✅ سرعة عالية وجودة ممتازة
`);
}

// عرض معلومات الإصدار
function showVersion() {
    const packageInfo = require('./package.json');
    console.log(`
📦 محول PDF إلى DOCX المتقدم
الإصدار: ${packageInfo.version || '1.0.0'}
المطور: نظام كشف الاستلال المتطور
التاريخ: ${new Date().toLocaleDateString('ar-SA')}
`);
}

// تحليل المعاملات
function parseArguments() {
    const args = process.argv.slice(2);
    const options = {};
    
    for (let i = 0; i < args.length; i++) {
        const arg = args[i];
        
        switch (arg) {
            case '-i':
            case '--input':
                options.input = args[++i];
                break;
            case '-o':
            case '--output':
                options.output = args[++i];
                break;
            case '-d':
            case '--dir':
                options.inputDir = args[++i];
                break;
            case '-od':
            case '--output-dir':
                options.outputDir = args[++i];
                break;
            case '-t':
            case '--title':
                options.title = args[++i];
                break;
            case '-h':
            case '--help':
                options.help = true;
                break;
            case '-v':
            case '--version':
                options.version = true;
                break;
            case '-info':
            case '--support-info':
                options.supportInfo = true;
                break;
            default:
                if (arg.startsWith('-')) {
                    console.error(`❌ خيار غير معروف: ${arg}`);
                    console.log('استخدم --help لعرض المساعدة');
                    process.exit(1);
                }
        }
    }
    
    return options;
}

// التحقق من صحة المعاملات
function validateOptions(options) {
    // تحويل ملف واحد
    if (options.input) {
        if (!fs.existsSync(options.input)) {
            throw new Error(`ملف PDF غير موجود: ${options.input}`);
        }
        
        if (!options.input.toLowerCase().endsWith('.pdf')) {
            throw new Error('يجب أن يكون الملف المدخل من نوع PDF');
        }
        
        if (!options.output) {
            // إنشاء اسم ملف الإخراج تلقائياً
            const baseName = path.basename(options.input, '.pdf');
            options.output = path.join(path.dirname(options.input), `${baseName}.docx`);
        }
        
        if (!options.output.toLowerCase().endsWith('.docx')) {
            options.output += '.docx';
        }
    }
    
    // تحويل متعدد الملفات
    if (options.inputDir) {
        if (!fs.existsSync(options.inputDir)) {
            throw new Error(`مجلد PDF غير موجود: ${options.inputDir}`);
        }
        
        if (!options.outputDir) {
            options.outputDir = path.join(options.inputDir, 'converted-docx');
        }
    }
    
    // يجب تحديد إما ملف واحد أو مجلد
    if (!options.input && !options.inputDir && !options.help && !options.version && !options.supportInfo) {
        throw new Error('يجب تحديد ملف PDF (-i) أو مجلد (-d) للتحويل');
    }
    
    return options;
}

// تحويل ملف واحد
async function convertSingleFile(converter, options) {
    console.log('🔄 بدء تحويل ملف واحد...');
    console.log(`📄 الملف المدخل: ${options.input}`);
    console.log(`📁 الملف الناتج: ${options.output}`);
    
    try {
        const startTime = Date.now();
        const result = await converter.convertPDFToDocx(options.input, options.output, {
            title: options.title
        });
        
        const totalTime = Date.now() - startTime;
        
        console.log('\n✅ تم التحويل بنجاح!');
        console.log('📊 تفاصيل التحويل:');
        console.log(`   📄 الملف: ${path.basename(result.inputFile)}`);
        console.log(`   🌐 اللغة: ${result.languageName} (${result.language})`);
        console.log(`   📝 طول النص: ${result.textLength.toLocaleString()} حرف`);
        console.log(`   📊 الهيكل:`);
        console.log(`      📋 فقرات: ${result.structure.paragraphs}`);
        console.log(`      📑 عناوين: ${result.structure.headings}`);
        console.log(`      📝 قوائم: ${result.structure.lists}`);
        console.log(`      📊 جداول: ${result.structure.tables}`);
        console.log(`   ⏱️ وقت الاستخراج: ${result.extractionTime}ms`);
        console.log(`   ⏱️ الوقت الإجمالي: ${result.processingTime}ms`);
        console.log(`   📁 محفوظ في: ${result.outputFile}`);
        
        return result;
        
    } catch (error) {
        console.error('\n❌ فشل التحويل:', error.message);
        process.exit(1);
    }
}

// تحويل متعدد الملفات
async function convertMultipleFiles(converter, options) {
    console.log('🔄 بدء التحويل المتعدد...');
    console.log(`📂 مجلد PDF: ${options.inputDir}`);
    console.log(`📁 مجلد الإخراج: ${options.outputDir}`);
    
    try {
        const startTime = Date.now();
        const result = await converter.convertMultipleFiles(options.inputDir, options.outputDir, {
            title: options.title
        });
        
        const totalTime = Date.now() - startTime;
        
        console.log('\n📊 ملخص التحويل المتعدد:');
        console.log(`   📄 إجمالي الملفات: ${result.totalFiles}`);
        console.log(`   ✅ نجح: ${result.successCount}`);
        console.log(`   ❌ فشل: ${result.failCount}`);
        console.log(`   📈 معدل النجاح: ${((result.successCount / result.totalFiles) * 100).toFixed(1)}%`);
        console.log(`   ⏱️ الوقت الإجمالي: ${totalTime}ms`);
        console.log(`   ⚡ متوسط الوقت لكل ملف: ${(totalTime / result.totalFiles).toFixed(0)}ms`);
        
        if (result.failCount > 0) {
            console.log('\n❌ الملفات التي فشل تحويلها:');
            result.results.filter(r => !r.success).forEach(r => {
                console.log(`   📄 ${path.basename(r.inputFile)}: ${r.error}`);
            });
        }
        
        console.log(`\n📁 الملفات المحولة محفوظة في: ${options.outputDir}`);
        
        return result;
        
    } catch (error) {
        console.error('\n❌ فشل التحويل المتعدد:', error.message);
        process.exit(1);
    }
}

// عرض معلومات الدعم
function showSupportInfo(converter) {
    const info = converter.getSupportInfo();
    
    console.log(`
📋 معلومات الدعم - محول PDF إلى DOCX المتقدم
================================================

📄 الصيغ المدعومة:
   📥 المدخل: ${info.supportedInputFormats.join(', ')}
   📤 الناتج: ${info.supportedOutputFormats.join(', ')}

🌐 اللغات المدعومة:
   ${info.supportedLanguages.map(lang => 
     lang === 'ar' ? '🇸🇦 العربية' : '🇺🇸 الإنجليزية'
   ).join('\n   ')}

✨ الميزات المتقدمة:
${info.features.map(feature => `   ✅ ${feature}`).join('\n')}

📦 معلومات الإصدار:
   🔢 الإصدار: ${info.version}
   👨‍💻 المطور: ${info.author}
   📅 التاريخ: ${new Date().toLocaleDateString('ar-SA')}

🔧 التقنيات المستخدمة:
   ✅ الحل الجذري لاستخراج النصوص العربية
   ✅ تحليل متقدم للغة والهيكل
   ✅ تحسين ثوري للنصوص
   ✅ حل طارئ ذكي للحالات الصعبة
   ✅ معالجة متقدمة للأخطاء

📞 الدعم الفني:
   📧 للمساعدة: استخدم --help
   🐛 للإبلاغ عن مشاكل: راجع الوثائق
   💡 للاقتراحات: تواصل مع فريق التطوير
`);
}

// الدالة الرئيسية
async function main() {
    try {
        console.log('🚀 محول PDF إلى DOCX المتقدم');
        console.log('=' .repeat(50));
        
        const options = parseArguments();
        
        // عرض المساعدة
        if (options.help) {
            showHelp();
            return;
        }
        
        // عرض معلومات الإصدار
        if (options.version) {
            showVersion();
            return;
        }
        
        // إنشاء المحول
        const converter = new PDFToDocxConverter();
        
        // عرض معلومات الدعم
        if (options.supportInfo) {
            showSupportInfo(converter);
            return;
        }
        
        // التحقق من صحة المعاملات
        validateOptions(options);
        
        // تنفيذ التحويل
        if (options.input) {
            await convertSingleFile(converter, options);
        } else if (options.inputDir) {
            await convertMultipleFiles(converter, options);
        }
        
    } catch (error) {
        console.error(`\n❌ خطأ: ${error.message}`);
        console.log('💡 استخدم --help لعرض المساعدة');
        process.exit(1);
    }
}

// تشغيل البرنامج
if (require.main === module) {
    main().catch(error => {
        console.error('❌ خطأ غير متوقع:', error);
        process.exit(1);
    });
}

module.exports = {
    parseArguments,
    validateOptions,
    convertSingleFile,
    convertMultipleFiles,
    showSupportInfo
};
