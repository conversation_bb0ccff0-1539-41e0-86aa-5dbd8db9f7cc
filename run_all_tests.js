const ComprehensiveTestSuite = require('./comprehensive_test');
const PerformanceTestSuite = require('./performance_test');
const fs = require('fs');
const path = require('path');

/**
 * سكريبت شامل لتشغيل جميع الاختبارات
 */
class MasterTestSuite {
    constructor() {
        this.startTime = null;
        this.endTime = null;
        this.allResults = {};
    }

    /**
     * تشغيل جميع الاختبارات
     */
    async runAllTests() {
        console.log('🚀 بدء الاختبارات الشاملة لتطبيق كشف الاستلال');
        console.log('=' .repeat(70));
        console.log('📋 سيتم تشغيل:');
        console.log('   1️⃣ اختبارات الدقة والوظائف');
        console.log('   2️⃣ اختبارات الأداء والضغط');
        console.log('   3️⃣ تقرير شامل نهائي');
        console.log('=' .repeat(70));

        this.startTime = Date.now();

        try {
            // 1. اختبارات الدقة والوظائف
            console.log('\n🎯 المرحلة 1: اختبارات الدقة والوظائف');
            console.log('-' .repeat(50));
            const comprehensiveTest = new ComprehensiveTestSuite();
            await comprehensiveTest.runAllTests();
            this.allResults.comprehensive = comprehensiveTest.testResults;

            console.log('\n⏳ انتظار 3 ثوانٍ قبل بدء اختبارات الأداء...');
            await this.sleep(3000);

            // 2. اختبارات الأداء
            console.log('\n⚡ المرحلة 2: اختبارات الأداء والضغط');
            console.log('-' .repeat(50));
            const performanceTest = new PerformanceTestSuite();
            await performanceTest.runPerformanceTests();
            this.allResults.performance = {
                performanceResults: performanceTest.performanceResults,
                memoryUsage: performanceTest.memoryUsage,
                stressTestResults: performanceTest.stressTestResults
            };

            this.endTime = Date.now();

            // 3. التقرير الشامل النهائي
            console.log('\n📊 المرحلة 3: التقرير الشامل النهائي');
            console.log('-' .repeat(50));
            await this.generateMasterReport();

        } catch (error) {
            console.error('❌ خطأ في تشغيل الاختبارات:', error);
            process.exit(1);
        }
    }

    /**
     * إنتاج التقرير الشامل النهائي
     */
    async generateMasterReport() {
        const totalTime = this.endTime - this.startTime;
        
        console.log('\n' + '=' .repeat(70));
        console.log('📊 التقرير الشامل النهائي - Plagiarism Checker Pro');
        console.log('=' .repeat(70));

        // معلومات عامة
        console.log(`\n📅 تاريخ الاختبار: ${new Date().toLocaleDateString('ar-SA')}`);
        console.log(`⏱️ إجمالي وقت الاختبار: ${(totalTime / 1000 / 60).toFixed(1)} دقيقة`);
        console.log(`🖥️ نظام التشغيل: ${process.platform}`);
        console.log(`⚙️ إصدار Node.js: ${process.version}`);

        // تحليل نتائج الدقة
        if (this.allResults.comprehensive) {
            console.log('\n🎯 نتائج اختبارات الدقة:');
            const comprehensiveResults = this.allResults.comprehensive;
            const totalTests = comprehensiveResults.length;
            const passedTests = comprehensiveResults.filter(r => r.status === 'passed').length;
            const overallAccuracy = comprehensiveResults.reduce((sum, r) => sum + r.accuracy, 0) / totalTests;

            console.log(`   📊 إجمالي الاختبارات: ${totalTests}`);
            console.log(`   ✅ اختبارات ناجحة: ${passedTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
            console.log(`   🎯 متوسط الدقة: ${overallAccuracy.toFixed(1)}%`);

            // تحليل حسب نوع الاستلال
            const typeAnalysis = this.analyzeByPlagiarismType(comprehensiveResults);
            console.log('\n   📈 الدقة حسب نوع الاستلال:');
            Object.keys(typeAnalysis).forEach(type => {
                console.log(`      ${type}: ${typeAnalysis[type].toFixed(1)}%`);
            });
        }

        // تحليل نتائج الأداء
        if (this.allResults.performance) {
            console.log('\n⚡ نتائج اختبارات الأداء:');
            const perfResults = this.allResults.performance.performanceResults;
            
            if (perfResults && perfResults.length > 0) {
                const avgSpeed = perfResults.reduce((sum, r) => sum + r.wordsPerSecond, 0) / perfResults.length;
                const avgTime = perfResults.reduce((sum, r) => sum + r.processingTime, 0) / perfResults.length;
                
                console.log(`   🚀 متوسط السرعة: ${avgSpeed.toFixed(1)} كلمة/ثانية`);
                console.log(`   ⏱️ متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
            }

            // تحليل الذاكرة
            const memoryUsage = this.allResults.performance.memoryUsage;
            if (memoryUsage && memoryUsage.length > 0) {
                const maxMemory = Math.max(...memoryUsage.map(m => m.heapUsed));
                console.log(`   💾 ذروة استخدام الذاكرة: ${(maxMemory / 1024 / 1024).toFixed(2)} MB`);
            }

            // تحليل اختبار الضغط
            const stressResults = this.allResults.performance.stressTestResults;
            if (stressResults && stressResults.length > 0) {
                const times = stressResults.map(r => r.processingTime);
                const avgStressTime = times.reduce((sum, t) => sum + t, 0) / times.length;
                const variance = times.reduce((sum, t) => sum + Math.pow(t - avgStressTime, 2), 0) / times.length;
                const stability = 100 - (Math.sqrt(variance) / avgStressTime * 100);
                
                console.log(`   🔥 استقرار الأداء: ${stability.toFixed(1)}%`);
            }
        }

        // التقييم العام
        const overallGrade = this.calculateOverallGrade();
        console.log(`\n🏆 التقييم العام للتطبيق: ${overallGrade.grade}`);
        console.log(`📝 الوصف: ${overallGrade.description}`);

        // نقاط القوة والضعف
        const analysis = this.analyzeStrengthsAndWeaknesses();
        console.log('\n💪 نقاط القوة:');
        analysis.strengths.forEach(strength => {
            console.log(`   ✅ ${strength}`);
        });

        if (analysis.weaknesses.length > 0) {
            console.log('\n⚠️ نقاط تحتاج تحسين:');
            analysis.weaknesses.forEach(weakness => {
                console.log(`   🔧 ${weakness}`);
            });
        }

        // التوصيات النهائية
        const recommendations = this.generateFinalRecommendations();
        console.log('\n💡 التوصيات النهائية:');
        recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
        });

        // حالة الجاهزية للإنتاج
        const productionReadiness = this.assessProductionReadiness();
        console.log(`\n🚀 جاهزية الإنتاج: ${productionReadiness.status}`);
        console.log(`📋 التفاصيل: ${productionReadiness.details}`);

        // حفظ التقرير الشامل
        await this.saveMasterReport();

        console.log('\n' + '=' .repeat(70));
        console.log('🎉 تم الانتهاء من جميع الاختبارات بنجاح!');
        console.log('📁 تم حفظ التقارير في الملفات التالية:');
        console.log('   📄 test_report.json - تقرير اختبارات الدقة');
        console.log('   📄 performance_report.json - تقرير اختبارات الأداء');
        console.log('   📄 master_report.json - التقرير الشامل');
        console.log('=' .repeat(70));
    }

    /**
     * تحليل النتائج حسب نوع الاستلال
     */
    analyzeByPlagiarismType(results) {
        const typeAccuracy = {};
        
        results.forEach(result => {
            if (!typeAccuracy[result.plagiarismType]) {
                typeAccuracy[result.plagiarismType] = [];
            }
            typeAccuracy[result.plagiarismType].push(result.accuracy);
        });

        const averages = {};
        Object.keys(typeAccuracy).forEach(type => {
            averages[type] = typeAccuracy[type].reduce((sum, acc) => sum + acc, 0) / typeAccuracy[type].length;
        });

        return averages;
    }

    /**
     * حساب التقييم العام
     */
    calculateOverallGrade() {
        let score = 100;
        let issues = [];

        // تقييم الدقة
        if (this.allResults.comprehensive) {
            const overallAccuracy = this.allResults.comprehensive.reduce((sum, r) => sum + r.accuracy, 0) / this.allResults.comprehensive.length;
            if (overallAccuracy < 70) {
                score -= 30;
                issues.push('دقة الكشف منخفضة');
            } else if (overallAccuracy < 80) {
                score -= 15;
                issues.push('دقة الكشف تحتاج تحسين');
            }
        }

        // تقييم الأداء
        if (this.allResults.performance && this.allResults.performance.performanceResults) {
            const avgSpeed = this.allResults.performance.performanceResults.reduce((sum, r) => sum + r.wordsPerSecond, 0) / this.allResults.performance.performanceResults.length;
            if (avgSpeed < 10) {
                score -= 20;
                issues.push('سرعة المعالجة بطيئة');
            } else if (avgSpeed < 20) {
                score -= 10;
                issues.push('سرعة المعالجة متوسطة');
            }
        }

        // تحديد الدرجة
        let grade, description;
        if (score >= 90) {
            grade = 'ممتاز (A+)';
            description = 'التطبيق جاهز للإنتاج بجودة عالية';
        } else if (score >= 80) {
            grade = 'جيد جداً (A)';
            description = 'التطبيق جاهز للإنتاج مع تحسينات طفيفة';
        } else if (score >= 70) {
            grade = 'جيد (B)';
            description = 'التطبيق يحتاج بعض التحسينات قبل الإنتاج';
        } else if (score >= 60) {
            grade = 'مقبول (C)';
            description = 'التطبيق يحتاج تحسينات كبيرة';
        } else {
            grade = 'يحتاج عمل (D)';
            description = 'التطبيق غير جاهز للإنتاج';
        }

        return { grade, description, score, issues };
    }

    /**
     * تحليل نقاط القوة والضعف
     */
    analyzeStrengthsAndWeaknesses() {
        const strengths = [];
        const weaknesses = [];

        // تحليل الدقة
        if (this.allResults.comprehensive) {
            const passRate = this.allResults.comprehensive.filter(r => r.status === 'passed').length / this.allResults.comprehensive.length * 100;
            if (passRate >= 80) {
                strengths.push('معدل نجاح عالي في اختبارات الدقة');
            } else {
                weaknesses.push('معدل نجاح منخفض في بعض الاختبارات');
            }

            const avgAccuracy = this.allResults.comprehensive.reduce((sum, r) => sum + r.accuracy, 0) / this.allResults.comprehensive.length;
            if (avgAccuracy >= 80) {
                strengths.push('دقة كشف جيدة للاستلال');
            } else {
                weaknesses.push('دقة الكشف تحتاج تحسين');
            }
        }

        // تحليل الأداء
        if (this.allResults.performance) {
            if (this.allResults.performance.performanceResults && this.allResults.performance.performanceResults.length > 0) {
                const avgSpeed = this.allResults.performance.performanceResults.reduce((sum, r) => sum + r.wordsPerSecond, 0) / this.allResults.performance.performanceResults.length;
                if (avgSpeed >= 20) {
                    strengths.push('سرعة معالجة ممتازة');
                } else if (avgSpeed >= 10) {
                    strengths.push('سرعة معالجة مقبولة');
                } else {
                    weaknesses.push('سرعة المعالجة بطيئة');
                }
            }

            if (this.allResults.performance.memoryUsage && this.allResults.performance.memoryUsage.length > 0) {
                const maxMemory = Math.max(...this.allResults.performance.memoryUsage.map(m => m.heapUsed)) / 1024 / 1024;
                if (maxMemory < 100) {
                    strengths.push('استخدام ذاكرة محسن');
                } else if (maxMemory > 500) {
                    weaknesses.push('استخدام ذاكرة عالي');
                }
            }
        }

        // نقاط قوة عامة
        strengths.push('واجهة مستخدم عربية شاملة');
        strengths.push('دعم أنواع ملفات متعددة');
        strengths.push('تقارير مفصلة وشاملة');
        strengths.push('تكامل مع الذكاء الاصطناعي');

        return { strengths, weaknesses };
    }

    /**
     * إنتاج التوصيات النهائية
     */
    generateFinalRecommendations() {
        const recommendations = [];

        // توصيات الدقة
        if (this.allResults.comprehensive) {
            const avgAccuracy = this.allResults.comprehensive.reduce((sum, r) => sum + r.accuracy, 0) / this.allResults.comprehensive.length;
            if (avgAccuracy < 80) {
                recommendations.push('تحسين خوارزميات كشف التشابه لزيادة الدقة');
                recommendations.push('توسيع قاعدة البيانات المرجعية');
            }
        }

        // توصيات الأداء
        if (this.allResults.performance && this.allResults.performance.performanceResults) {
            const avgSpeed = this.allResults.performance.performanceResults.reduce((sum, r) => sum + r.wordsPerSecond, 0) / this.allResults.performance.performanceResults.length;
            if (avgSpeed < 20) {
                recommendations.push('تحسين سرعة معالجة النصوص');
                recommendations.push('تحسين خوارزميات التحليل للملفات الكبيرة');
            }
        }

        // توصيات عامة
        recommendations.push('إضافة مفاتيح API حقيقية للذكاء الاصطناعي');
        recommendations.push('تطوير نظام تحديثات تلقائي');
        recommendations.push('إضافة دعم لغات أخرى');
        recommendations.push('تحسين تصميم التقارير المُنتجة');

        return recommendations;
    }

    /**
     * تقييم جاهزية الإنتاج
     */
    assessProductionReadiness() {
        let readinessScore = 100;
        const issues = [];

        // فحص الدقة
        if (this.allResults.comprehensive) {
            const avgAccuracy = this.allResults.comprehensive.reduce((sum, r) => sum + r.accuracy, 0) / this.allResults.comprehensive.length;
            if (avgAccuracy < 70) {
                readinessScore -= 40;
                issues.push('دقة منخفضة');
            } else if (avgAccuracy < 80) {
                readinessScore -= 20;
                issues.push('دقة متوسطة');
            }
        }

        // فحص الأداء
        if (this.allResults.performance && this.allResults.performance.performanceResults) {
            const avgSpeed = this.allResults.performance.performanceResults.reduce((sum, r) => sum + r.wordsPerSecond, 0) / this.allResults.performance.performanceResults.length;
            if (avgSpeed < 10) {
                readinessScore -= 30;
                issues.push('أداء بطيء');
            } else if (avgSpeed < 20) {
                readinessScore -= 15;
                issues.push('أداء متوسط');
            }
        }

        let status, details;
        if (readinessScore >= 80) {
            status = '✅ جاهز للإنتاج';
            details = 'التطبيق يلبي معايير الجودة المطلوبة';
        } else if (readinessScore >= 60) {
            status = '⚠️ جاهز مع تحفظات';
            details = `يحتاج تحسينات: ${issues.join(', ')}`;
        } else {
            status = '❌ غير جاهز';
            details = `مشاكل كبيرة: ${issues.join(', ')}`;
        }

        return { status, details, score: readinessScore };
    }

    /**
     * حفظ التقرير الشامل
     */
    async saveMasterReport() {
        const reportData = {
            timestamp: new Date().toISOString(),
            testDuration: this.endTime - this.startTime,
            systemInfo: {
                platform: process.platform,
                nodeVersion: process.version,
                arch: process.arch
            },
            results: this.allResults,
            analysis: {
                overallGrade: this.calculateOverallGrade(),
                strengthsAndWeaknesses: this.analyzeStrengthsAndWeaknesses(),
                recommendations: this.generateFinalRecommendations(),
                productionReadiness: this.assessProductionReadiness()
            }
        };

        const reportPath = path.join(__dirname, 'master_report.json');
        fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2), 'utf8');
        console.log(`\n💾 تم حفظ التقرير الشامل: ${reportPath}`);
    }

    /**
     * دالة مساعدة للانتظار
     */
    sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// تشغيل جميع الاختبارات
async function main() {
    try {
        const masterTest = new MasterTestSuite();
        await masterTest.runAllTests();
    } catch (error) {
        console.error('❌ خطأ في تشغيل الاختبارات الشاملة:', error);
        process.exit(1);
    }
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    main();
}

module.exports = MasterTestSuite;
