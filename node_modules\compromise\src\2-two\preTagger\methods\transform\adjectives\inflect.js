import { convert } from 'suffix-thumb'
// import toAdverb from './adverbs/toAdverb.js'


const toSuperlative = function (adj, model) {
  const mod = model.two.models.toSuperlative
  return convert(adj, mod)
}
const toComparative = function (adj, model) {
  const mod = model.two.models.toComparative
  return convert(adj, mod)
}
const fromComparative = function (adj, model) {
  const mod = model.two.models.fromComparative
  return convert(adj, mod)
}
const fromSuperlative = function (adj, model) {
  const mod = model.two.models.fromSuperlative
  return convert(adj, mod)
}
const toNoun = function (adj, model) {
  const mod = model.two.models.adjToNoun
  return convert(adj, mod)
}

export {
  toSuperlative, toComparative, fromComparative, fromSuperlative, toNoun
}
