const fs = require('fs');
const path = require('path');

/**
 * محلل بسيط ودقيق للوصول لدقة 95%+
 * يركز على الأساسيات بدلاً من التعقيد
 */
class SimplePreciseAnalyzer {
    constructor() {
        this.loadReferenceData();
        
        // إعدادات بسيطة وفعالة
        this.config = {
            // عتبات محسنة للدقة العالية
            baseThreshold: 0.40,        // العتبة الأساسية 40%
            highSensitivity: 1.8,       // تضخيم للحساسية العالية
            exactMatchBonus: 0.3,       // مكافأة للتطابق الحرفي
            minPhraseLength: 10,        // الحد الأدنى لطول العبارة
            
            // أوزان بسيطة ومتوازنة
            weights: {
                exactMatch: 0.60,       // 60% للتطابق الحرفي
                fuzzyMatch: 0.30,       // 30% للتطابق الضبابي
                contextMatch: 0.10      // 10% للسياق
            }
        };
        
        console.log('🎯 تم تهيئة المحلل البسيط والدقيق للوصول لدقة 95%+');
    }
    
    /**
     * تحميل البيانات المرجعية
     */
    loadReferenceData() {
        try {
            const dataPath = path.join(__dirname, '../data/reference_phrases.json');
            const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
            this.referenceTexts = data.academic_phrases || [];
            
            // معالجة بسيطة وفعالة
            this.processedReferences = this.referenceTexts.map(text => ({
                original: text,
                processed: this.preprocessText(text),
                words: this.extractWords(text),
                length: text.length
            }));
            
            console.log(`✅ تم تحميل ${this.referenceTexts.length} عبارة مرجعية للتحليل الدقيق`);
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error.message);
            this.referenceTexts = [];
            this.processedReferences = [];
        }
    }
    
    /**
     * التحليل الرئيسي البسيط والدقيق
     */
    async analyzePrecisely(inputText) {
        console.log('🎯 بدء التحليل البسيط والدقيق...');
        
        const processedInput = this.preprocessText(inputText);
        const inputWords = this.extractWords(inputText);
        
        let totalSimilarity = 0;
        let maxSimilarity = 0;
        let exactMatches = 0;
        let suspiciousSegments = [];
        
        // فحص كل نص مرجعي
        for (const ref of this.processedReferences) {
            // 1. البحث عن التطابق الحرفي (الأهم)
            const exactScore = this.calculateExactMatch(processedInput, ref.processed);
            
            // 2. البحث عن التطابق الضبابي
            const fuzzyScore = this.calculateFuzzyMatch(inputWords, ref.words);
            
            // 3. تحليل السياق البسيط
            const contextScore = this.calculateSimpleContext(processedInput, ref.processed);
            
            // حساب النتيجة المركبة
            const combinedScore = (
                exactScore * this.config.weights.exactMatch +
                fuzzyScore * this.config.weights.fuzzyMatch +
                contextScore * this.config.weights.contextMatch
            );
            
            totalSimilarity += combinedScore;
            maxSimilarity = Math.max(maxSimilarity, combinedScore);
            
            // إذا كان التطابق عالي، سجله كجزء مشبوه
            if (combinedScore > this.config.baseThreshold) {
                exactMatches++;
                
                // البحث عن الأجزاء المطابقة
                const segments = this.findMatchingSegments(inputText, ref.original, combinedScore);
                suspiciousSegments.push(...segments);
            }
        }
        
        // حساب النتيجة النهائية
        const avgSimilarity = this.processedReferences.length > 0 ? 
            totalSimilarity / this.processedReferences.length : 0;
        
        // تطبيق التضخيم للحساسية العالية
        let finalScore = maxSimilarity * 0.7 + avgSimilarity * 0.3;
        
        // مكافأة للتطابق الحرفي
        if (exactMatches > 0) {
            finalScore += this.config.exactMatchBonus * Math.min(exactMatches / 5, 1);
        }
        
        // تطبيق التضخيم للحساسية
        if (finalScore > this.config.baseThreshold) {
            finalScore = Math.min(1.0, finalScore * this.config.highSensitivity);
        }
        
        const plagiarismPercentage = Math.round(finalScore * 100);
        
        console.log(`📈 انتهى التحليل الدقيق: ${plagiarismPercentage}% استلال`);
        
        return {
            plagiarismPercentage,
            riskLevel: this.classifyRiskLevel(plagiarismPercentage),
            suspiciousSegments: suspiciousSegments.slice(0, 15),
            analysis: {
                maxSimilarity,
                avgSimilarity,
                exactMatches,
                totalReferences: this.processedReferences.length,
                finalScore,
                simplePrecise: true
            }
        };
    }
    
    /**
     * حساب التطابق الحرفي المحسن
     */
    calculateExactMatch(inputText, refText) {
        let matchScore = 0;
        const inputLength = inputText.length;
        
        // البحث عن عبارات متطابقة بأطوال مختلفة
        for (let length = 50; length >= this.config.minPhraseLength; length--) {
            for (let i = 0; i <= inputText.length - length; i++) {
                const substring = inputText.substring(i, i + length);
                
                if (refText.includes(substring)) {
                    // مكافأة أكبر للعبارات الطويلة
                    const bonus = length > 25 ? 2.0 : 1.0;
                    matchScore += (length / inputLength) * bonus;
                }
            }
        }
        
        return Math.min(1.0, matchScore);
    }
    
    /**
     * حساب التطابق الضبابي المحسن
     */
    calculateFuzzyMatch(inputWords, refWords) {
        if (inputWords.length === 0 || refWords.length === 0) return 0;
        
        const inputSet = new Set(inputWords);
        const refSet = new Set(refWords);
        
        // حساب Jaccard similarity
        const intersection = new Set([...inputSet].filter(x => refSet.has(x)));
        const union = new Set([...inputSet, ...refSet]);
        
        const jaccardSim = union.size > 0 ? intersection.size / union.size : 0;
        
        // مكافأة للكلمات المشتركة الطويلة
        let longWordBonus = 0;
        intersection.forEach(word => {
            if (word.length > 5) {
                longWordBonus += 0.1;
            }
        });
        
        return Math.min(1.0, jaccardSim + longWordBonus);
    }
    
    /**
     * تحليل السياق البسيط
     */
    calculateSimpleContext(inputText, refText) {
        // كلمات أكاديمية مهمة
        const academicWords = ['دراسة', 'بحث', 'تحليل', 'نتائج', 'استنتاج', 'توصيات', 'منهج', 'عينة'];
        
        let contextScore = 0;
        
        academicWords.forEach(word => {
            if (inputText.includes(word) && refText.includes(word)) {
                contextScore += 0.1;
            }
        });
        
        return Math.min(1.0, contextScore);
    }
    
    /**
     * العثور على الأجزاء المطابقة
     */
    findMatchingSegments(inputText, referenceText, similarity) {
        const segments = [];
        const sentences = inputText.split(/[.!?؟]/).filter(s => s.trim().length > 20);
        
        sentences.forEach((sentence, index) => {
            const processedSentence = this.preprocessText(sentence);
            const processedRef = this.preprocessText(referenceText);
            
            // البحث عن تطابق في الجملة
            if (processedRef.includes(processedSentence.substring(0, 20)) ||
                this.calculateFuzzyMatch(
                    this.extractWords(sentence), 
                    this.extractWords(referenceText)
                ) > 0.6) {
                
                segments.push({
                    text: sentence.trim(),
                    similarity: similarity,
                    startIndex: index,
                    type: 'precise_match'
                });
            }
        });
        
        return segments;
    }
    
    /**
     * تصنيف مستوى الخطر
     */
    classifyRiskLevel(percentage) {
        if (percentage >= 80) return { level: 5, label: 'عالي جداً' };
        if (percentage >= 60) return { level: 4, label: 'عالي' };
        if (percentage >= 40) return { level: 3, label: 'متوسط' };
        if (percentage >= 20) return { level: 2, label: 'منخفض' };
        return { level: 1, label: 'ضئيل' };
    }
    
    /**
     * معالجة النص
     */
    preprocessText(text) {
        return text
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim()
            .toLowerCase();
    }
    
    /**
     * استخراج الكلمات
     */
    extractWords(text) {
        return this.preprocessText(text)
            .split(/\s+/)
            .filter(word => word.length > 2);
    }
}

module.exports = SimplePreciseAnalyzer;
