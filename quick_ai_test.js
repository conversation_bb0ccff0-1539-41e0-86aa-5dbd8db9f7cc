const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار سريع للنظام المعزز بالذكاء الاصطناعي
 */
async function quickAITest() {
    console.log('🤖 اختبار سريع للنظام المعزز بالذكاء الاصطناعي');
    console.log('=' .repeat(60));
    
    const checker = new PlagiarismChecker();
    
    // اختبار ملف واحد فقط
    const testFile = 'test_calibrated_60_percent.txt';
    const expected = 60;
    
    console.log(`📄 اختبار: ${testFile} - متوقع: ${expected}%`);
    
    const filePath = path.join(__dirname, 'test-files', testFile);
    
    if (!fs.existsSync(filePath)) {
        console.log(`❌ الملف غير موجود`);
        return;
    }
    
    try {
        const startTime = Date.now();
        
        // تشغيل الفحص
        checker.aiDetector.isEnabled = false;
        
        const result = await checker.checkFile(filePath);
        const endTime = Date.now();
        
        const processingTime = endTime - startTime;
        const difference = Math.abs(result.plagiarismPercentage - expected);
        const accuracy = Math.max(0, 100 - (difference / expected) * 100);
        
        console.log(`\n📊 النتائج:`);
        console.log(`   النتيجة: ${result.plagiarismPercentage}%`);
        console.log(`   المتوقع: ${expected}%`);
        console.log(`   الفرق: ${difference.toFixed(1)}%`);
        console.log(`   الدقة: ${accuracy.toFixed(1)}%`);
        console.log(`   الوقت: ${processingTime}ms`);
        console.log(`   الخطر: ${result.riskLevel.label}`);
        
        if (result.analysis) {
            console.log(`\n🤖 تحليل الذكاء الاصطناعي:`);
            console.log(`   معزز بالذكاء الاصطناعي: ${result.analysis.aiEnhanced ? 'نعم' : 'لا'}`);
            if (result.analysis.aiEnhanced) {
                console.log(`   تطابق حرفي: ${result.analysis.exactMatches}`);
                console.log(`   تطابق دلالي: ${result.analysis.semanticMatches}`);
                console.log(`   أنماط: ${result.analysis.patternMatches}`);
                console.log(`   سياق: ${result.analysis.contextMatches}`);
                console.log(`   ثقة: ${(result.analysis.confidenceScore * 100).toFixed(1)}%`);
            }
        }
        
        if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
            console.log(`\n📝 أجزاء مشبوهة (${result.suspiciousSegments.length}):`);
            result.suspiciousSegments.slice(0, 2).forEach((segment, index) => {
                console.log(`   ${index + 1}. [${segment.type}] "${segment.text.substring(0, 50)}..."`);
            });
        }
        
        // تقييم النجاح
        if (accuracy >= 95) {
            console.log('\n🎉 نجح! دقة ممتازة 95%+');
        } else if (accuracy >= 85) {
            console.log('\n✅ جيد جداً! دقة عالية');
        } else if (accuracy >= 70) {
            console.log('\n📈 جيد! تحسن ملحوظ');
        } else {
            console.log('\n⚠️ يحتاج عمل! دقة منخفضة');
        }
        
        return {
            actual: result.plagiarismPercentage,
            expected: expected,
            accuracy: accuracy,
            processingTime: processingTime,
            aiEnhanced: result.analysis ? result.analysis.aiEnhanced : false
        };
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
        console.error(error.stack);
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const result = await quickAITest();
        
        if (result) {
            console.log('\n🎯 الخلاصة:');
            console.log(`   النتيجة: ${result.actual}% (متوقع: ${result.expected}%)`);
            console.log(`   الدقة: ${result.accuracy.toFixed(1)}%`);
            console.log(`   الوقت: ${result.processingTime}ms`);
            console.log(`   معزز بالذكاء الاصطناعي: ${result.aiEnhanced ? 'نعم' : 'لا'}`);
            
            if (result.accuracy >= 95) {
                console.log('🎉 تم تحقيق الهدف! دقة 95%+');
            } else if (result.accuracy >= 85) {
                console.log('🎯 قريب من الهدف! دقة عالية');
            } else {
                console.log('🔧 يحتاج مزيد من التطوير');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ عام:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { quickAITest };
