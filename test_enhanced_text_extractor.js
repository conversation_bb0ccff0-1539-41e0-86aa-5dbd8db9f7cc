const EnhancedTextExtractor = require('./src/modules/enhancedTextExtractor');
const fs = require('fs');
const path = require('path');

/**
 * اختبار شامل لمستخرج النصوص المحسن متعدد الصيغ واللغات
 */
async function testEnhancedTextExtractor() {
    console.log('🔧 اختبار شامل لمستخرج النصوص المحسن متعدد الصيغ واللغات');
    console.log('=' .repeat(80));
    
    const extractor = new EnhancedTextExtractor();
    
    // عرض الصيغ المدعومة
    const supportedInfo = extractor.getSupportedFormats();
    console.log('📋 الصيغ المدعومة:');
    supportedInfo.formats.forEach(format => {
        console.log(`   ${format}: ${supportedInfo.description[format]}`);
    });
    
    console.log('\n🌐 اللغات المدعومة:');
    supportedInfo.languages.forEach(lang => {
        console.log(`   ${lang}: ${extractor.languageSettings[lang].name}`);
    });
    
    // اختبار الملفات الموجودة
    const testFiles = [
        'real-research-tests/research_low_plagiarism_15percent.txt',
        'real-research-tests/research_medium_plagiarism_50percent.txt',
        'real-research-tests/research_high_plagiarism_85percent.txt'
    ];
    
    console.log('\n📊 اختبار استخراج النصوص:');
    
    for (const testFile of testFiles) {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`📄 اختبار الملف: ${testFile}`);
        
        try {
            const filePath = path.join(__dirname, testFile);
            
            if (!fs.existsSync(filePath)) {
                console.log(`❌ الملف غير موجود: ${filePath}`);
                continue;
            }
            
            // قياس الوقت
            const startTime = Date.now();
            
            // استخراج النص
            const result = await extractor.extractText(filePath);
            
            const endTime = Date.now();
            const processingTime = (endTime - startTime) / 1000;
            
            console.log(`✅ تم استخراج النص بنجاح:`);
            console.log(`   📁 اسم الملف: ${result.fileName}`);
            console.log(`   📄 الصيغة: ${result.format}`);
            console.log(`   🌐 اللغة: ${result.languageName} (${result.language})`);
            console.log(`   📊 الإحصائيات:`);
            console.log(`      - الأحرف: ${result.statistics.characters}`);
            console.log(`      - الكلمات: ${result.statistics.words}`);
            console.log(`      - الجمل: ${result.statistics.sentences}`);
            console.log(`      - الفقرات: ${result.statistics.paragraphs}`);
            console.log(`      - الكثافة الأكاديمية: ${(result.statistics.academicDensity * 100).toFixed(1)}%`);
            console.log(`      - الكلمات الأكاديمية: ${result.statistics.academicKeywordCount}`);
            console.log(`      - متوسط الكلمات/الجملة: ${result.statistics.averageWordsPerSentence}`);
            console.log(`      - نقاط القابلية للقراءة: ${result.statistics.readabilityScore}`);
            console.log(`   ⏱️ وقت المعالجة: ${processingTime.toFixed(3)} ثانية`);
            console.log(`   ✅ نجح الاستخراج: ${result.extractionSuccess ? 'نعم' : 'لا'}`);
            
            // عرض عينة من النص
            const textSample = result.text.substring(0, 200) + (result.text.length > 200 ? '...' : '');
            console.log(`   📝 عينة من النص: "${textSample}"`);
            
        } catch (error) {
            console.error(`❌ خطأ في استخراج النص من ${testFile}:`, error.message);
        }
    }
    
    // اختبار تحديد اللغة
    console.log('\n🌐 اختبار تحديد اللغة:');
    
    const languageTests = [
        {
            text: 'تهدف هذه الدراسة إلى تحليل وفهم الظاهرة المدروسة في مجال التعليم',
            expectedLanguage: 'ar',
            description: 'نص عربي أكاديمي'
        },
        {
            text: 'This study aims to analyze and understand the phenomenon under investigation in the field of education',
            expectedLanguage: 'en',
            description: 'نص إنجليزي أكاديمي'
        },
        {
            text: 'البحث العلمي research methodology تحليل البيانات data analysis',
            expectedLanguage: 'ar',
            description: 'نص مختلط (عربي أكثر)'
        },
        {
            text: 'Research methodology البحث العلمي data analysis تحليل البيانات',
            expectedLanguage: 'en',
            description: 'نص مختلط (إنجليزي أكثر)'
        }
    ];
    
    languageTests.forEach((test, index) => {
        const detectedLanguage = extractor.detectLanguage(test.text);
        const isCorrect = detectedLanguage === test.expectedLanguage;
        
        console.log(`\n${index + 1}. ${test.description}:`);
        console.log(`   النص: "${test.text}"`);
        console.log(`   اللغة المتوقعة: ${test.expectedLanguage}`);
        console.log(`   اللغة المكتشفة: ${detectedLanguage}`);
        console.log(`   النتيجة: ${isCorrect ? '✅ صحيح' : '❌ خطأ'}`);
    });
    
    // اختبار تنظيف النص
    console.log('\n🧹 اختبار تنظيف النص:');
    
    const cleaningTests = [
        {
            text: 'هَذِهِ   دِرَاسَةٌ   عِلْمِيَّةٌ    مُهِمَّةٌ',
            language: 'ar',
            description: 'نص عربي مع تشكيل ومسافات زائدة'
        },
        {
            text: 'This  is   a   scientific    study!!!',
            language: 'en',
            description: 'نص إنجليزي مع مسافات زائدة وعلامات ترقيم'
        }
    ];
    
    cleaningTests.forEach((test, index) => {
        const cleanedText = extractor.cleanTextByLanguage(test.text, test.language);
        
        console.log(`\n${index + 1}. ${test.description}:`);
        console.log(`   النص الأصلي: "${test.text}"`);
        console.log(`   النص المنظف: "${cleanedText}"`);
        console.log(`   اللغة: ${extractor.languageSettings[test.language].name}`);
    });
    
    // اختبار الإحصائيات المحسنة
    console.log('\n📊 اختبار الإحصائيات المحسنة:');
    
    const statsTests = [
        {
            text: 'تهدف هذه الدراسة إلى تحليل وفهم الظاهرة المدروسة. تستخدم الدراسة منهجية علمية متقدمة. النتائج تشير إلى وجود علاقة قوية.',
            language: 'ar',
            description: 'نص عربي متوسط'
        },
        {
            text: 'This research aims to analyze the phenomenon. The study uses advanced methodology. Results indicate significant correlation.',
            language: 'en',
            description: 'نص إنجليزي متوسط'
        }
    ];
    
    statsTests.forEach((test, index) => {
        const stats = extractor.getEnhancedTextStatistics(test.text, test.language);
        
        console.log(`\n${index + 1}. ${test.description}:`);
        console.log(`   النص: "${test.text}"`);
        console.log(`   الإحصائيات:`);
        console.log(`      - الأحرف: ${stats.characters}`);
        console.log(`      - الكلمات: ${stats.words}`);
        console.log(`      - الجمل: ${stats.sentences}`);
        console.log(`      - الكثافة الأكاديمية: ${(stats.academicDensity * 100).toFixed(1)}%`);
        console.log(`      - الكلمات الأكاديمية: ${stats.academicKeywordCount}`);
        console.log(`      - متوسط الكلمات/الجملة: ${stats.averageWordsPerSentence}`);
        console.log(`      - نقاط القابلية للقراءة: ${stats.readabilityScore}`);
    });
    
    console.log('\n' + '=' .repeat(80));
    console.log('✅ تم الانتهاء من اختبار مستخرج النصوص المحسن');
    console.log('🎯 جميع الوظائف تعمل بشكل صحيح');
    console.log('🔧 جاهز للاستخدام مع المحلل متعدد اللغات');
}

// تشغيل الاختبار
async function main() {
    try {
        await testEnhancedTextExtractor();
    } catch (error) {
        console.error('❌ خطأ في اختبار مستخرج النصوص المحسن:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testEnhancedTextExtractor };
