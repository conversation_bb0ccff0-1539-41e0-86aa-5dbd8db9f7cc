const fs = require('fs');
const path = require('path');

/**
 * محلل التشابه المحسن مع تقنيات NLP متقدمة
 */
class EnhancedSimilarityAnalyzer {
    constructor() {
        this.loadReferenceDatabase();
        
        // كلمات الربط والكلمات الشائعة
        this.stopWords = new Set([
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك',
            'التي', 'الذي', 'كان', 'كانت', 'يكون', 'تكون', 'أن', 'إن', 'لكن',
            'بعد', 'قبل', 'أثناء', 'خلال', 'عند', 'لدى', 'حول', 'نحو', 'ضد'
        ]);
        
        // أنماط أكاديمية شائعة موسعة
        this.academicPatterns = [
            /تشير\s+النتائج\s+إلى/g,
            /يوصي\s+الباحث\s+ب/g,
            /من\s+خلال\s+هذا\s+البحث/g,
            /تهدف\s+هذه\s+الدراسة\s+إلى/g,
            /أظهرت\s+النتائج\s+وجود/g,
            /في\s+ضوء\s+ما\s+تقدم/g,
            /بناءً\s+على\s+النتائج/g,
            /منهجية\s+البحث/g,
            /فروق\s+ذات\s+دلالة\s+إحصائية/g,
            /اعتمدت\s+الدراسة\s+على\s+المنهج/g,
            /المنهج\s+الوصفي\s+التحليلي/g,
            /تم\s+استخدام\s+المنهج/g,
            /النتائج\s+المتحصل\s+عليها/g,
            /المزيد\s+من\s+الدراسات/g
        ];

        // نظام تحليل السياق الذكي
        this.contextAnalyzer = {
            // كلمات مفتاحية حسب السياق
            methodologyKeywords: ['منهج', 'منهجية', 'أسلوب', 'طريقة', 'إجراءات', 'عينة', 'أدوات'],
            resultsKeywords: ['نتائج', 'نتيجة', 'توصل', 'أظهر', 'بين', 'كشف', 'وجد'],
            conclusionKeywords: ['خلاصة', 'ختام', 'استنتاج', 'خلص', 'يمكن', 'توصي', 'اقتراح'],
            literatureKeywords: ['دراسة', 'بحث', 'أدبيات', 'مراجع', 'مصادر', 'نظرية'],

            // أوزان السياق
            weights: {
                methodology: 1.5,  // منهجية البحث أكثر أهمية
                results: 1.3,      // النتائج مهمة
                conclusion: 1.2,   // الخاتمة مهمة
                literature: 1.1    // الأدبيات أقل أهمية
            }
        };
        
        console.log('🚀 تم تهيئة المحلل المحسن مع تقنيات NLP متقدمة');
    }
    
    /**
     * تحميل قاعدة البيانات المرجعية
     */
    loadReferenceDatabase() {
        try {
            const dbPath = path.join(__dirname, '..', 'data', 'reference_phrases.json');
            const data = fs.readFileSync(dbPath, 'utf8');
            const referenceData = JSON.parse(data);
            
            this.referenceTexts = [
                ...referenceData.academic_phrases,
                ...referenceData.common_transitions,
                ...referenceData.research_verbs
            ];
            
            console.log(`✅ تم تحميل ${this.referenceTexts.length} عبارة مرجعية للتحليل المحسن`);
        } catch (error) {
            console.warn('⚠️ استخدام قاعدة بيانات احتياطية:', error.message);
            this.referenceTexts = [
                "في الختام، يمكن القول أن هذا البحث قد توصل إلى نتائج مهمة",
                "تهدف هذه الدراسة إلى تحليل وفهم الظاهرة المدروسة",
                "تشير النتائج إلى وجود علاقة قوية بين المتغيرات المدروسة",
                "يوصي الباحث بإجراء المزيد من الدراسات في هذا المجال",
                "أظهرت النتائج وجود فروق ذات دلالة إحصائية",
                "منهجية البحث اعتمدت الدراسة على المنهج الوصفي التحليلي",
                "في ضوء ما تقدم، يمكن استنتاج أن",
                "بناءً على النتائج المتحصل عليها"
            ];
        }
    }
    
    /**
     * التحليل الرئيسي المحسن
     */
    async analyzeText(inputText) {
        console.log(`🚀 تحليل محسن: ${this.getWordCount(inputText)} كلمة`);
        
        const processedInput = this.preprocessText(inputText);
        const inputSentences = this.extractSentences(inputText);
        const inputWords = this.extractWords(processedInput);
        
        let totalSimilarity = 0;
        let maxSimilarity = 0;
        let suspiciousSegments = [];
        let checkedTexts = 0;
        
        // إحصائيات متقدمة
        let exactMatches = 0;
        let semanticMatches = 0;
        let patternMatches = 0;
        let structuralMatches = 0;
        
        // فحص كل نص مرجعي
        for (const refText of this.referenceTexts) {
            const processedRef = this.preprocessText(refText);
            const refWords = this.extractWords(processedRef);
            const refSentences = this.extractSentences(refText);
            
            // 1. التطابق الحرفي المتقدم (40%)
            const exactSim = this.calculateAdvancedExactMatch(processedInput, processedRef);
            if (exactSim > 0.5) exactMatches++;
            
            // 2. التشابه الدلالي (30%)
            const semanticSim = this.calculateSemanticSimilarity(inputWords, refWords);
            if (semanticSim > 0.6) semanticMatches++;
            
            // 3. كشف الأنماط الأكاديمية (15%)
            const patternSim = this.detectAcademicPatterns(inputText, refText);
            if (patternSim > 0.4) patternMatches++;

            // 4. تحليل السياق الذكي (15%)
            const contextSim = this.analyzeSmartContext(inputText, refText);

            // 5. التشابه الهيكلي (10%)
            const structuralSim = this.calculateStructuralSimilarity(inputSentences, refSentences);
            if (structuralSim > 0.3) structuralMatches++;
            
            // حساب التشابه المركب مع السياق الذكي
            const combinedSimilarity = (
                exactSim * 0.35 +        // 35% للتطابق الحرفي
                semanticSim * 0.25 +     // 25% للتشابه الدلالي
                patternSim * 0.15 +      // 15% للأنماط الأكاديمية
                contextSim * 0.15 +      // 15% لتحليل السياق الذكي
                structuralSim * 0.10     // 10% للتشابه الهيكلي
            );
            
            totalSimilarity += combinedSimilarity;
            maxSimilarity = Math.max(maxSimilarity, combinedSimilarity);
            checkedTexts++;
            
            // البحث عن الأجزاء المشبوهة
            if (combinedSimilarity > 0.3) {
                const segments = this.findAdvancedSuspiciousSegments(
                    inputText, refText, combinedSimilarity, exactSim, semanticSim, patternSim
                );
                suspiciousSegments.push(...segments);
            }
        }
        
        console.log(`🔍 فحص محسن: ${checkedTexts} نص`);
        console.log(`🎯 مطابقات: ${exactMatches}حرفي، ${semanticMatches}دلالي، ${patternMatches}نمط، ${structuralMatches}هيكلي`);
        
        // حساب النتيجة النهائية مع تحسينات ذكية
        const avgSimilarity = checkedTexts > 0 ? totalSimilarity / checkedTexts : 0;
        let finalScore = (maxSimilarity * 0.7) + (avgSimilarity * 0.3);
        
        // كشف إعادة الصياغة والترجمة
        const paraphraseScore = this.detectParaphrasing(inputText, this.referenceTexts);
        if (paraphraseScore > 0.4) {
            console.log(`🔄 كشف إعادة صياغة محتملة: ${(paraphraseScore * 100).toFixed(1)}%`);
            finalScore = Math.max(finalScore, paraphraseScore);
        }

        // تطبيق مضاعفات ذكية محسنة
        if (exactMatches >= 3) finalScore *= 1.5;
        if (semanticMatches >= 4) finalScore *= 1.4;
        if (patternMatches >= 3) finalScore *= 1.3;
        if (structuralMatches >= 2) finalScore *= 1.2;
        if (paraphraseScore > 0.5) finalScore *= 1.25;
        
        // تطبيق حدود منطقية
        finalScore = Math.min(1.0, finalScore);
        const plagiarismPercentage = Math.round(finalScore * 100);
        
        console.log(`📈 نتائج محسنة: ${plagiarismPercentage}% استلال، ${suspiciousSegments.length} جزء مشبوه`);
        
        return {
            plagiarismPercentage,
            riskLevel: this.classifyRiskLevel(plagiarismPercentage),
            suspiciousSegments: suspiciousSegments.slice(0, 25),
            analysis: {
                totalTextsChecked: checkedTexts,
                maxSimilarity,
                avgSimilarity,
                finalScore,
                exactMatches,
                semanticMatches,
                patternMatches,
                structuralMatches,
                enhancedNLP: true
            }
        };
    }
    
    /**
     * التطابق الحرفي المتقدم
     */
    calculateAdvancedExactMatch(inputText, refText) {
        let matchScore = 0;
        const inputLength = inputText.length;
        
        // البحث عن عبارات متطابقة بأطوال مختلفة
        for (let length = 60; length >= 15; length -= 5) {
            for (let i = 0; i <= inputText.length - length; i++) {
                const substring = inputText.substring(i, i + length);
                
                if (refText.includes(substring)) {
                    const bonus = length > 40 ? 3.0 : length > 25 ? 2.0 : 1.5;
                    matchScore += (length / inputLength) * bonus;
                }
            }
        }
        
        return Math.min(1.0, matchScore);
    }
    
    /**
     * التشابه الدلالي المحسن
     */
    calculateSemanticSimilarity(inputWords, refWords) {
        if (inputWords.length === 0 || refWords.length === 0) return 0;
        
        // إزالة كلمات الربط
        const filteredInput = inputWords.filter(word => !this.stopWords.has(word) && word.length > 3);
        const filteredRef = refWords.filter(word => !this.stopWords.has(word) && word.length > 3);
        
        const inputSet = new Set(filteredInput);
        const refSet = new Set(filteredRef);
        
        // حساب Jaccard similarity
        const intersection = new Set([...inputSet].filter(x => refSet.has(x)));
        const union = new Set([...inputSet, ...refSet]);
        
        const jaccardSim = union.size > 0 ? intersection.size / union.size : 0;
        
        // مكافأة للكلمات المهمة
        let importanceBonus = 0;
        intersection.forEach(word => {
            if (word.length > 6) importanceBonus += 0.15;
            else if (word.length > 4) importanceBonus += 0.1;
        });
        
        return Math.min(1.0, jaccardSim + importanceBonus);
    }
    
    /**
     * كشف الأنماط الأكاديمية
     */
    detectAcademicPatterns(inputText, refText) {
        let patternScore = 0;
        let foundPatterns = 0;

        for (const pattern of this.academicPatterns) {
            const inputMatches = (inputText.match(pattern) || []).length;
            const refMatches = (refText.match(pattern) || []).length;

            if (inputMatches > 0 && refMatches > 0) {
                patternScore += Math.min(inputMatches, refMatches) * 0.25;
                foundPatterns++;
            }
        }

        return Math.min(1.0, patternScore + (foundPatterns * 0.12));
    }

    /**
     * تحليل السياق الذكي
     */
    analyzeSmartContext(inputText, refText) {
        let contextScore = 0;
        const inputLower = inputText.toLowerCase();
        const refLower = refText.toLowerCase();

        // تحليل السياق حسب أقسام البحث
        const contexts = ['methodology', 'results', 'conclusion', 'literature'];

        for (const context of contexts) {
            const keywords = this.contextAnalyzer[context + 'Keywords'];
            const weight = this.contextAnalyzer.weights[context];

            let inputContextScore = 0;
            let refContextScore = 0;

            // حساب نقاط السياق لكل نص
            for (const keyword of keywords) {
                const inputCount = (inputLower.match(new RegExp(keyword, 'g')) || []).length;
                const refCount = (refLower.match(new RegExp(keyword, 'g')) || []).length;

                inputContextScore += inputCount;
                refContextScore += refCount;
            }

            // إذا كان كلا النصين يحتويان على كلمات من نفس السياق
            if (inputContextScore > 0 && refContextScore > 0) {
                const contextSimilarity = Math.min(inputContextScore, refContextScore) /
                                        Math.max(inputContextScore, refContextScore);
                contextScore += contextSimilarity * weight * 0.2;
            }
        }

        // تحليل التسلسل المنطقي للأفكار
        const sequenceScore = this.analyzeLogicalSequence(inputText, refText);
        contextScore += sequenceScore * 0.3;

        // تحليل الانتقالات بين الفقرات
        const transitionScore = this.analyzeTransitions(inputText, refText);
        contextScore += transitionScore * 0.2;

        return Math.min(1.0, contextScore);
    }

    /**
     * تحليل التسلسل المنطقي للأفكار
     */
    analyzeLogicalSequence(inputText, refText) {
        const logicalMarkers = [
            'أولاً', 'ثانياً', 'ثالثاً', 'أخيراً', 'في البداية', 'في النهاية',
            'بالإضافة إلى', 'علاوة على ذلك', 'من ناحية أخرى', 'في المقابل'
        ];

        let sequenceScore = 0;
        let foundMarkers = 0;

        for (const marker of logicalMarkers) {
            const inputHas = inputText.toLowerCase().includes(marker);
            const refHas = refText.toLowerCase().includes(marker);

            if (inputHas && refHas) {
                sequenceScore += 0.15;
                foundMarkers++;
            }
        }

        return Math.min(1.0, sequenceScore + (foundMarkers * 0.05));
    }

    /**
     * تحليل الانتقالات بين الفقرات
     */
    analyzeTransitions(inputText, refText) {
        const transitionWords = [
            'لذلك', 'وبالتالي', 'نتيجة لذلك', 'من هنا', 'إذن', 'هكذا',
            'كما أن', 'بينما', 'في حين', 'رغم ذلك', 'مع ذلك', 'غير أن'
        ];

        let transitionScore = 0;
        let sharedTransitions = 0;

        for (const transition of transitionWords) {
            const inputCount = (inputText.toLowerCase().match(new RegExp(transition, 'g')) || []).length;
            const refCount = (refText.toLowerCase().match(new RegExp(transition, 'g')) || []).length;

            if (inputCount > 0 && refCount > 0) {
                transitionScore += Math.min(inputCount, refCount) * 0.1;
                sharedTransitions++;
            }
        }

        return Math.min(1.0, transitionScore + (sharedTransitions * 0.05));
    }

    /**
     * كشف إعادة الصياغة والترجمة
     */
    detectParaphrasing(inputText, referenceTexts) {
        let maxParaphraseScore = 0;

        // قاموس المرادفات الأكاديمية
        const synonyms = {
            'دراسة': ['بحث', 'تحليل', 'فحص', 'استقصاء'],
            'نتائج': ['نتيجة', 'مخرجات', 'حصائل', 'ثمار'],
            'تحليل': ['دراسة', 'فحص', 'تقييم', 'استعراض'],
            'منهج': ['منهجية', 'أسلوب', 'طريقة', 'نهج'],
            'يوصي': ['ينصح', 'يقترح', 'يدعو', 'يحث'],
            'أظهر': ['بين', 'كشف', 'وضح', 'أوضح'],
            'توصل': ['وصل', 'خلص', 'انتهى', 'استنتج']
        };

        for (const refText of referenceTexts) {
            let paraphraseScore = 0;

            // 1. كشف استبدال المرادفات
            const synonymScore = this.detectSynonymReplacement(inputText, refText, synonyms);
            paraphraseScore += synonymScore * 0.4;

            // 2. كشف إعادة ترتيب الجمل
            const reorderScore = this.detectSentenceReordering(inputText, refText);
            paraphraseScore += reorderScore * 0.3;

            // 3. كشف تغيير الصيغة النحوية
            const grammarScore = this.detectGrammaticalChanges(inputText, refText);
            paraphraseScore += grammarScore * 0.2;

            // 4. كشف الحذف والإضافة
            const modificationScore = this.detectModifications(inputText, refText);
            paraphraseScore += modificationScore * 0.1;

            maxParaphraseScore = Math.max(maxParaphraseScore, paraphraseScore);
        }

        return Math.min(1.0, maxParaphraseScore);
    }

    /**
     * كشف استبدال المرادفات
     */
    detectSynonymReplacement(inputText, refText, synonyms) {
        let synonymScore = 0;
        let replacements = 0;

        const inputWords = this.extractWords(inputText);
        const refWords = this.extractWords(refText);

        for (const [word, syns] of Object.entries(synonyms)) {
            const inputHasWord = inputWords.includes(word);
            const inputHasSynonym = syns.some(syn => inputWords.includes(syn));
            const refHasWord = refWords.includes(word);
            const refHasSynonym = syns.some(syn => refWords.includes(syn));

            // إذا كان أحد النصوص يحتوي على الكلمة والآخر على مرادفها
            if ((inputHasWord && refHasSynonym) || (inputHasSynonym && refHasWord)) {
                synonymScore += 0.15;
                replacements++;
            }
        }

        return Math.min(1.0, synonymScore + (replacements * 0.05));
    }

    /**
     * كشف إعادة ترتيب الجمل
     */
    detectSentenceReordering(inputText, refText) {
        const inputSentences = this.extractSentences(inputText);
        const refSentences = this.extractSentences(refText);

        let reorderScore = 0;
        let matchedSentences = 0;

        for (const inputSent of inputSentences) {
            for (const refSent of refSentences) {
                const similarity = this.calculateSemanticSimilarity(
                    this.extractWords(inputSent),
                    this.extractWords(refSent)
                );

                if (similarity > 0.7) {
                    reorderScore += similarity * 0.2;
                    matchedSentences++;
                    break;
                }
            }
        }

        return Math.min(1.0, reorderScore + (matchedSentences * 0.1));
    }

    /**
     * كشف تغيير الصيغة النحوية
     */
    detectGrammaticalChanges(inputText, refText) {
        // كشف تحويل من المبني للمعلوم إلى المبني للمجهول وبالعكس
        const passiveMarkers = ['تم', 'يتم', 'تمت', 'يجري', 'أجري'];
        const activeMarkers = ['قام', 'يقوم', 'نفذ', 'ينفذ', 'أجرى'];

        let grammarScore = 0;

        const inputLower = inputText.toLowerCase();
        const refLower = refText.toLowerCase();

        // كشف التحويل بين المبني للمعلوم والمجهول
        let inputPassive = 0, inputActive = 0;
        let refPassive = 0, refActive = 0;

        passiveMarkers.forEach(marker => {
            inputPassive += (inputLower.match(new RegExp(marker, 'g')) || []).length;
            refPassive += (refLower.match(new RegExp(marker, 'g')) || []).length;
        });

        activeMarkers.forEach(marker => {
            inputActive += (inputLower.match(new RegExp(marker, 'g')) || []).length;
            refActive += (refLower.match(new RegExp(marker, 'g')) || []).length;
        });

        // إذا كان أحد النصوص يستخدم المبني للمعلوم والآخر للمجهول
        if ((inputPassive > 0 && refActive > 0) || (inputActive > 0 && refPassive > 0)) {
            grammarScore += 0.3;
        }

        return Math.min(1.0, grammarScore);
    }

    /**
     * كشف الحذف والإضافة
     */
    detectModifications(inputText, refText) {
        const inputWords = this.extractWords(inputText);
        const refWords = this.extractWords(refText);

        const inputSet = new Set(inputWords);
        const refSet = new Set(refWords);

        const intersection = new Set([...inputSet].filter(x => refSet.has(x)));
        const union = new Set([...inputSet, ...refSet]);

        // حساب نسبة الكلمات المشتركة
        const overlapRatio = intersection.size / union.size;

        // إذا كان هناك تداخل كبير مع اختلاف في الطول
        const lengthDiff = Math.abs(inputWords.length - refWords.length) / Math.max(inputWords.length, refWords.length);

        if (overlapRatio > 0.6 && lengthDiff > 0.2) {
            return overlapRatio * 0.5;
        }

        return 0;
    }
    
    /**
     * التشابه الهيكلي
     */
    calculateStructuralSimilarity(inputSentences, refSentences) {
        if (inputSentences.length === 0 || refSentences.length === 0) return 0;
        
        let structuralScore = 0;
        const maxComparisons = Math.min(inputSentences.length, refSentences.length, 10);
        
        for (let i = 0; i < maxComparisons; i++) {
            const inputSent = this.preprocessText(inputSentences[i] || '');
            const refSent = this.preprocessText(refSentences[i] || '');
            
            if (inputSent.length > 20 && refSent.length > 20) {
                const similarity = this.calculateLevenshteinSimilarity(inputSent, refSent);
                structuralScore += similarity;
            }
        }
        
        return maxComparisons > 0 ? structuralScore / maxComparisons : 0;
    }
    
    /**
     * البحث عن الأجزاء المشبوهة المتقدم
     */
    findAdvancedSuspiciousSegments(inputText, refText, combinedSim, exactSim, semanticSim, patternSim) {
        const segments = [];
        const sentences = inputText.split(/[.!?؟]/).filter(s => s.trim().length > 20);
        
        sentences.forEach((sentence, index) => {
            const processedSentence = this.preprocessText(sentence);
            const processedRef = this.preprocessText(refText);
            
            // تحديد نوع التطابق
            let matchType = 'mixed';
            if (exactSim > 0.6) matchType = 'exact';
            else if (semanticSim > 0.7) matchType = 'semantic';
            else if (patternSim > 0.5) matchType = 'pattern';
            
            // البحث عن تطابق في الجملة
            if (processedRef.includes(processedSentence.substring(0, 25)) ||
                this.calculateSemanticSimilarity(
                    this.extractWords(sentence), 
                    this.extractWords(refText)
                ) > 0.6) {
                
                segments.push({
                    text: sentence.trim(),
                    similarity: combinedSim,
                    startIndex: index,
                    type: matchType,
                    exactScore: exactSim,
                    semanticScore: semanticSim,
                    patternScore: patternSim
                });
            }
        });
        
        return segments;
    }
    
    // دوال مساعدة
    preprocessText(text) {
        return text
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim()
            .toLowerCase();
    }
    
    extractWords(text) {
        return this.preprocessText(text)
            .split(/\s+/)
            .filter(word => word.length > 2);
    }
    
    extractSentences(text) {
        return text.split(/[.!?؟]/)
            .map(s => s.trim())
            .filter(s => s.length > 10);
    }
    
    getWordCount(text) {
        return text.split(/\s+/).filter(word => word.length > 0).length;
    }
    
    calculateLevenshteinSimilarity(str1, str2) {
        const maxLength = Math.max(str1.length, str2.length);
        if (maxLength === 0) return 1;
        
        const distance = this.levenshteinDistance(str1, str2);
        return 1 - (distance / maxLength);
    }
    
    levenshteinDistance(str1, str2) {
        const matrix = [];
        
        for (let i = 0; i <= str2.length; i++) {
            matrix[i] = [i];
        }
        
        for (let j = 0; j <= str1.length; j++) {
            matrix[0][j] = j;
        }
        
        for (let i = 1; i <= str2.length; i++) {
            for (let j = 1; j <= str1.length; j++) {
                if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                    matrix[i][j] = matrix[i - 1][j - 1];
                } else {
                    matrix[i][j] = Math.min(
                        matrix[i - 1][j - 1] + 1,
                        matrix[i][j - 1] + 1,
                        matrix[i - 1][j] + 1
                    );
                }
            }
        }
        
        return matrix[str2.length][str1.length];
    }
    
    classifyRiskLevel(percentage) {
        if (percentage >= 80) return { level: 5, label: 'عالي جداً' };
        if (percentage >= 60) return { level: 4, label: 'عالي' };
        if (percentage >= 40) return { level: 3, label: 'متوسط' };
        if (percentage >= 20) return { level: 2, label: 'منخفض' };
        return { level: 1, label: 'ضئيل' };
    }
}

module.exports = EnhancedSimilarityAnalyzer;
