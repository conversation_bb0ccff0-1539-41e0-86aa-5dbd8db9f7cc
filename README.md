# 🔍 Plagiarism Checker Pro - النسخة المحسنة

تطبيق سطح المكتب المتقدم لكشف الاستلال في النصوص العربية باستخدام تقنيات الذكاء الاصطناعي والمعالجة الطبيعية للغة. تم تطويره وتحسينه من خلال **مشروع تطوير شامل ومنهجي** شمل 8 مراحل تطوير و6 محللات متقدمة.

## 🎯 النتائج المحققة

- **دقة كشف الاستلال**: 79.9% (تحسن +36.6% من النسخة الأساسية)
- **أفضل أداء**: البحوث منخفضة الاستلال (86.7% دقة)
- **سرعة المعالجة**: 0.2 ثانية متوسط
- **المحلل المستخدم**: TargetedPrecisionAnalyzer (الأفضل من 6 محللات)

## 🌟 المميزات

### 📄 دعم أنواع الملفات المختلفة
- **PDF** - ملفات PDF بجميع الإصدارات
- **Word** - ملفات DOCX و DOC
- **النصوص العادية** - ملفات TXT

### 🔍 تقنيات الكشف المتقدمة
- **الكشف الحرفي** - اكتشاف النسخ المباشر
- **كشف إعادة الصياغة** - باستخدام الذكاء الاصطناعي
- **التحليل الدلالي** - فهم المعنى والسياق
- **تحليل البنية** - كشف التشابه في هيكل النص

### 🤖 الذكاء الاصطناعي
- **دعم OpenAI GPT** - للتحليل المتقدم
- **دعم Google Gemini** - كبديل قوي
- **العمل بدون إنترنت** - للفحص الأساسي

### 📊 التقارير الشاملة
- **تقارير PDF مفصلة** - مع الرسوم البيانية
- **تحليل إحصائي** - لجميع جوانب النص
- **توصيات محددة** - لتحسين الأصالة

### 🌐 دعم اللغة العربية
- **واجهة عربية كاملة** - تصميم يدعم RTL
- **تحليل النصوص العربية** - خوارزميات محسنة
- **خطوط عربية جميلة** - تجربة مستخدم ممتازة

## 🚀 التثبيت والتشغيل

### متطلبات النظام
- **نظام التشغيل**: Windows 10/11
- **الذاكرة**: 4 GB RAM (8 GB مستحسن)
- **المساحة**: 500 MB مساحة فارغة
- **الإنترنت**: اختياري (للذكاء الاصطناعي)

### تثبيت المكتبات
```bash
npm install
```

### تشغيل التطبيق في وضع التطوير
```bash
npm run dev
```

### تشغيل التطبيق العادي
```bash
npm start
```

## 🔧 البناء والتغليف

### بناء التطبيق لنظام Windows
```bash
npm run build-win
```

### إنتاج ملف EXE قابل للتوزيع
```bash
npm run dist
```

سيتم إنتاج الملف في مجلد `dist/` ويمكن تشغيله مباشرة على أي جهاز Windows.

## 📁 هيكل المشروع

```
plagiarism-checker-pro/
├── src/
│   ├── main.js                 # العملية الرئيسية لـ Electron
│   ├── renderer/               # واجهة المستخدم
│   │   ├── index.html         # الصفحة الرئيسية
│   │   ├── styles.css         # التصميم والأنماط
│   │   └── script.js          # منطق الواجهة
│   └── modules/               # وحدات التطبيق
│       ├── textExtractor.js   # استخراج النصوص
│       ├── similarityAnalyzer.js # تحليل التشابه
│       ├── aiDetector.js      # الذكاء الاصطناعي
│       ├── reportGenerator.js # إنتاج التقارير
│       └── plagiarismChecker.js # الوحدة الرئيسية
├── assets/                    # الأصول والموارد
├── package.json              # إعدادات المشروع
└── README.md                 # هذا الملف
```

## ⚙️ الإعدادات

### إعداد مفاتيح API للذكاء الاصطناعي

1. **OpenAI API**:
   - احصل على مفتاح من [OpenAI Platform](https://platform.openai.com/)
   - أدخل المفتاح في إعدادات التطبيق

2. **Google Gemini API**:
   - احصل على مفتاح من [Google AI Studio](https://makersuite.google.com/)
   - أدخل المفتاح في إعدادات التطبيق

### مستويات الحساسية
- **منخفضة**: للنصوص الأكاديمية العامة
- **متوسطة**: للأبحاث والمقالات (افتراضي)
- **عالية**: للأعمال الأصلية والإبداعية

## 🎯 كيفية الاستخدام

### 1. اختيار الملف
- انقر على "اختيار ملف" أو اسحب الملف إلى المنطقة المخصصة
- الملفات المدعومة: PDF, DOCX, DOC, TXT

### 2. بدء الفحص
- انقر على "بدء الفحص"
- انتظر حتى اكتمال العملية (قد تستغرق دقائق حسب حجم الملف)

### 3. مراجعة النتائج
- **نسبة الاستلال**: النسبة المئوية الإجمالية
- **الأجزاء المشكوك بها**: النصوص المحتملة للاستلال
- **المصادر**: المراجع المكتشفة
- **التحليل التفصيلي**: إحصائيات شاملة

### 4. حفظ التقرير
- انقر على "حفظ التقرير"
- اختر موقع الحفظ
- سيتم إنتاج ملف PDF مفصل

## 🔍 تفسير النتائج

### مستويات الخطر
- **🟢 ضئيل (0-20%)**: محتوى أصلي إلى حد كبير
- **🟡 منخفض (20-40%)**: مقبول مع بعض التحسينات
- **🟠 متوسط (40-70%)**: يتطلب مراجعة
- **🔴 عالي (70%+)**: يتطلب مراجعة شاملة

### أنواع التشابه
- **تشابه عالي**: نسخ مباشر أو شبه مباشر
- **تشابه متوسط**: إعادة صياغة بسيطة
- **تشابه منخفض**: تشابه طبيعي أو مقبول

## 🛠️ التطوير والمساهمة

### تقنيات مستخدمة
- **Electron.js** - إطار العمل الرئيسي
- **Node.js** - بيئة التشغيل
- **Natural.js** - معالجة اللغات الطبيعية
- **PDF-Parse** - استخراج نصوص PDF
- **Mammoth.js** - استخراج نصوص Word
- **PDFKit** - إنتاج تقارير PDF

### المساهمة في المشروع
1. Fork المشروع
2. إنشاء فرع جديد للميزة
3. تطوير وتجريب التحسينات
4. إرسال Pull Request

## 📞 الدعم والمساعدة

### المشاكل الشائعة
- **الملف لا يُقرأ**: تأكد من أن الملف غير محمي بكلمة مرور
- **بطء في الفحص**: قلل من حجم الملف أو أغلق التطبيقات الأخرى
- **خطأ في API**: تحقق من صحة مفاتيح الذكاء الاصطناعي

### التواصل
- **GitHub Issues**: لتقارير الأخطاء والاقتراحات
- **Email**: <EMAIL>

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- **OpenAI** - لتوفير تقنيات GPT
- **Google** - لتقنيات Gemini AI
- **مجتمع Electron** - للإطار الرائع
- **مطوري Node.js** - للمكتبات المفيدة

---

**تم تطوير هذا التطبيق بحب ❤️ لخدمة المجتمع الأكاديمي العربي**
