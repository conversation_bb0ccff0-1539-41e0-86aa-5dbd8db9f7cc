const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * نظام المعايرة الذكية للوصول لدقة 95%+
 */
class SmartCalibrationSystem {
    constructor() {
        this.checker = new PlagiarismChecker();
        this.testFiles = [
            { file: 'test_calibrated_20_percent.txt', expected: 20 },
            { file: 'test_calibrated_60_percent.txt', expected: 60 },
            { file: 'test_calibrated_90_percent.txt', expected: 90 }
        ];
        
        // مجموعة من الإعدادات للاختبار
        this.configurationSets = [
            // إعدادات محافظة
            {
                name: 'Conservative',
                useAdvanced: false,
                thresholds: { high: 70, medium: 55, low: 40 },
                weights: { jaccard: 30, cosine: 30, levenshtein: 25, semantic: 15 }
            },
            // إعدادات متوازنة
            {
                name: 'Balanced',
                useAdvanced: false,
                thresholds: { high: 60, medium: 45, low: 30 },
                weights: { jaccard: 40, cosine: 35, levenshtein: 20, semantic: 5 }
            },
            // إعدادات حساسة
            {
                name: 'Sensitive',
                useAdvanced: false,
                thresholds: { high: 50, medium: 35, low: 20 },
                weights: { jaccard: 50, cosine: 30, levenshtein: 15, semantic: 5 }
            },
            // إعدادات عالية الحساسية
            {
                name: 'HighSensitive',
                useAdvanced: false,
                thresholds: { high: 40, medium: 25, low: 15 },
                weights: { jaccard: 60, cosine: 25, levenshtein: 10, semantic: 5 }
            },
            // إعدادات مخصصة للنصوص العربية
            {
                name: 'ArabicOptimized',
                useAdvanced: false,
                thresholds: { high: 45, medium: 30, low: 18 },
                weights: { jaccard: 45, cosine: 40, levenshtein: 12, semantic: 3 }
            }
        ];
    }
    
    /**
     * تشغيل المعايرة الذكية
     */
    async runSmartCalibration() {
        console.log('🧠 بدء نظام المعايرة الذكية للوصول لدقة 95%+');
        console.log('=' .repeat(70));
        console.log('🎯 الهدف: العثور على أفضل إعدادات للوصول لدقة 95%+');
        console.log('🔧 سيتم اختبار 5 مجموعات مختلفة من الإعدادات');
        console.log('=' .repeat(70));
        
        const results = [];
        
        for (const config of this.configurationSets) {
            console.log(`\n🔧 اختبار إعدادات: ${config.name}`);
            console.log(`   العتبات: High ${config.thresholds.high}%, Medium ${config.thresholds.medium}%, Low ${config.thresholds.low}%`);
            console.log(`   الأوزان: Jaccard ${config.weights.jaccard}%, Cosine ${config.weights.cosine}%, Levenshtein ${config.weights.levenshtein}%, Semantic ${config.weights.semantic}%`);
            
            const configResult = await this.testConfiguration(config);
            results.push(configResult);
            
            console.log(`   📊 النتيجة: دقة ${configResult.avgAccuracy.toFixed(1)}%, نجاح ${configResult.successRate.toFixed(1)}%`);
        }
        
        // تحليل النتائج واختيار الأفضل
        const bestConfig = this.findBestConfiguration(results);
        
        console.log('\n' + '=' .repeat(70));
        console.log('🏆 أفضل إعدادات تم العثور عليها');
        console.log('=' .repeat(70));
        
        console.log(`🥇 الإعدادات الفائزة: ${bestConfig.name}`);
        console.log(`📈 الدقة المحققة: ${bestConfig.avgAccuracy.toFixed(1)}%`);
        console.log(`✅ معدل النجاح: ${bestConfig.successRate.toFixed(1)}%`);
        console.log(`⏱️ متوسط الوقت: ${bestConfig.avgTime.toFixed(0)}ms`);
        
        // تطبيق الإعدادات الأفضل
        await this.applyBestConfiguration(bestConfig);
        
        // اختبار نهائي مع الإعدادات الأفضل
        console.log('\n🎯 اختبار نهائي مع الإعدادات المحسنة...');
        const finalResult = await this.finalTest(bestConfig);
        
        return finalResult;
    }
    
    /**
     * اختبار مجموعة إعدادات محددة
     */
    async testConfiguration(config) {
        // تطبيق الإعدادات
        this.applyConfiguration(config);
        
        const testResults = [];
        
        for (const testFile of this.testFiles) {
            const filePath = path.join(__dirname, 'test-files', testFile.file);
            
            if (!fs.existsSync(filePath)) {
                continue;
            }
            
            try {
                const startTime = Date.now();
                this.checker.aiDetector.isEnabled = false;
                
                const result = await this.checker.checkFile(filePath);
                const endTime = Date.now();
                
                const processingTime = endTime - startTime;
                const difference = Math.abs(result.plagiarismPercentage - testFile.expected);
                const accuracy = Math.max(0, 100 - (difference / testFile.expected) * 100);
                
                testResults.push({
                    file: testFile.file,
                    expected: testFile.expected,
                    actual: result.plagiarismPercentage,
                    accuracy: accuracy,
                    processingTime: processingTime,
                    passed: accuracy >= 80
                });
                
            } catch (error) {
                console.log(`   ❌ خطأ في ${testFile.file}: ${error.message}`);
            }
        }
        
        // حساب المتوسطات
        const avgAccuracy = testResults.reduce((sum, r) => sum + r.accuracy, 0) / testResults.length;
        const avgTime = testResults.reduce((sum, r) => sum + r.processingTime, 0) / testResults.length;
        const successRate = (testResults.filter(r => r.passed).length / testResults.length) * 100;
        
        return {
            name: config.name,
            config: config,
            avgAccuracy: avgAccuracy,
            successRate: successRate,
            avgTime: avgTime,
            testResults: testResults,
            score: this.calculateConfigScore(avgAccuracy, successRate, avgTime)
        };
    }
    
    /**
     * تطبيق مجموعة إعدادات
     */
    applyConfiguration(config) {
        // تعديل إعدادات النظام
        this.checker.useAdvancedAnalyzer = config.useAdvanced;
        this.checker.useConfigurableAnalyzer = !config.useAdvanced; // استخدام المحلل القابل للتخصيص

        // تحديث إعدادات المحلل القابل للتخصيص
        const analyzerConfig = {
            thresholds: {
                high: config.thresholds.high / 100,
                medium: config.thresholds.medium / 100,
                low: config.thresholds.low / 100
            },
            weights: {
                jaccard: config.weights.jaccard / 100,
                cosine: config.weights.cosine / 100,
                levenshtein: config.weights.levenshtein / 100,
                semantic: config.weights.semantic / 100
            }
        };

        this.checker.updateAnalyzerConfig(analyzerConfig);
    }
    
    /**
     * حساب نقاط الإعدادات
     */
    calculateConfigScore(accuracy, successRate, time) {
        // وزن أكبر للدقة ومعدل النجاح
        const accuracyScore = accuracy * 0.5;
        const successScore = successRate * 0.4;
        const timeScore = Math.max(0, (2000 - time) / 2000) * 0.1; // مكافأة للسرعة
        
        return accuracyScore + successScore + timeScore;
    }
    
    /**
     * العثور على أفضل إعدادات
     */
    findBestConfiguration(results) {
        // ترتيب حسب النقاط
        results.sort((a, b) => b.score - a.score);
        
        console.log('\n📊 ترتيب الإعدادات حسب الأداء:');
        results.forEach((result, index) => {
            console.log(`   ${index + 1}. ${result.name}: دقة ${result.avgAccuracy.toFixed(1)}%, نجاح ${result.successRate.toFixed(1)}%, نقاط ${result.score.toFixed(1)}`);
        });
        
        return results[0]; // أفضل إعدادات
    }
    
    /**
     * تطبيق أفضل إعدادات
     */
    async applyBestConfiguration(bestConfig) {
        console.log(`\n🔧 تطبيق الإعدادات الأفضل: ${bestConfig.name}`);
        this.applyConfiguration(bestConfig.config);
        
        // حفظ الإعدادات الأفضل
        const configData = {
            timestamp: new Date().toISOString(),
            bestConfiguration: bestConfig.config,
            performance: {
                avgAccuracy: bestConfig.avgAccuracy,
                successRate: bestConfig.successRate,
                avgTime: bestConfig.avgTime,
                score: bestConfig.score
            },
            allResults: bestConfig.testResults
        };
        
        fs.writeFileSync('best_configuration.json', JSON.stringify(configData, null, 2));
        console.log('💾 تم حفظ الإعدادات الأفضل: best_configuration.json');
    }
    
    /**
     * اختبار نهائي
     */
    async finalTest(bestConfig) {
        const finalResults = [];
        
        for (const testFile of this.testFiles) {
            const filePath = path.join(__dirname, 'test-files', testFile.file);
            
            if (!fs.existsSync(filePath)) {
                continue;
            }
            
            try {
                const startTime = Date.now();
                this.checker.aiDetector.isEnabled = false;
                
                const result = await this.checker.checkFile(filePath);
                const endTime = Date.now();
                
                const processingTime = endTime - startTime;
                const difference = Math.abs(result.plagiarismPercentage - testFile.expected);
                const accuracy = Math.max(0, 100 - (difference / testFile.expected) * 100);
                
                console.log(`   📄 ${testFile.file}: ${result.plagiarismPercentage}% (متوقع: ${testFile.expected}%) - دقة: ${accuracy.toFixed(1)}%`);
                
                finalResults.push({
                    file: testFile.file,
                    expected: testFile.expected,
                    actual: result.plagiarismPercentage,
                    accuracy: accuracy,
                    processingTime: processingTime,
                    passed: accuracy >= 85
                });
                
            } catch (error) {
                console.log(`   ❌ خطأ في ${testFile.file}: ${error.message}`);
            }
        }
        
        // حساب النتائج النهائية
        const finalAvgAccuracy = finalResults.reduce((sum, r) => sum + r.accuracy, 0) / finalResults.length;
        const finalSuccessRate = (finalResults.filter(r => r.passed).length / finalResults.length) * 100;
        const finalAvgTime = finalResults.reduce((sum, r) => sum + r.processingTime, 0) / finalResults.length;
        
        console.log('\n🎯 النتائج النهائية مع الإعدادات المحسنة:');
        console.log(`   متوسط الدقة: ${finalAvgAccuracy.toFixed(1)}%`);
        console.log(`   معدل النجاح: ${finalSuccessRate.toFixed(1)}%`);
        console.log(`   متوسط الوقت: ${finalAvgTime.toFixed(0)}ms`);
        
        // تقييم تحقيق الأهداف
        const targetsAchieved = [
            finalAvgAccuracy >= 95,
            finalSuccessRate >= 90,
            finalAvgTime < 1000
        ].filter(Boolean).length;
        
        console.log(`\n🎯 تقييم تحقيق الأهداف:`);
        console.log(`   دقة 95%+: ${finalAvgAccuracy >= 95 ? '✅' : '❌'} (${finalAvgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 90%+: ${finalSuccessRate >= 90 ? '✅' : '❌'} (${finalSuccessRate.toFixed(1)}%)`);
        console.log(`   أداء < 1000ms: ${finalAvgTime < 1000 ? '✅' : '❌'} (${finalAvgTime.toFixed(0)}ms)`);
        console.log(`   الأهداف المحققة: ${targetsAchieved}/3`);
        
        if (targetsAchieved === 3) {
            console.log('\n🎉 تم تحقيق جميع الأهداف! النظام جاهز للإنتاج');
        } else if (finalAvgAccuracy >= 90) {
            console.log('\n🎯 دقة ممتازة! قريب جداً من الهدف النهائي');
        } else if (finalAvgAccuracy >= 80) {
            console.log('\n✅ تحسن كبير! النظام يعمل بكفاءة جيدة');
        } else {
            console.log('\n🔧 تحسن ملحوظ لكن يحتاج مزيد من العمل');
        }
        
        return {
            avgAccuracy: finalAvgAccuracy,
            successRate: finalSuccessRate,
            avgTime: finalAvgTime,
            targetsAchieved: targetsAchieved,
            bestConfig: bestConfig.name
        };
    }
}

// تشغيل النظام
async function main() {
    try {
        const calibrationSystem = new SmartCalibrationSystem();
        const result = await calibrationSystem.runSmartCalibration();
        
        console.log('\n🏆 خلاصة المعايرة الذكية:');
        console.log(`   أفضل إعدادات: ${result.bestConfig}`);
        console.log(`   الدقة النهائية: ${result.avgAccuracy.toFixed(1)}%`);
        console.log(`   معدل النجاح: ${result.successRate.toFixed(1)}%`);
        console.log(`   الأهداف المحققة: ${result.targetsAchieved}/3`);
        
    } catch (error) {
        console.error('❌ خطأ في المعايرة:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { SmartCalibrationSystem };
