const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * تحليل عميق للفجوات الحالية في النظام
 */
async function deepGapAnalysis() {
    console.log('🔍 تحليل عميق للفجوات الحالية في النظام');
    console.log('=' .repeat(70));
    console.log('🎯 الهدف: فهم الأسباب الجذرية لعدم تحقيق دقة 95%');
    console.log('📊 الوضع الحالي: دقة 51.7%, معدل نجاح 33.3%');
    console.log('=' .repeat(70));
    
    const checker = new PlagiarismChecker();
    const analysisResults = [];
    
    // ملفات الاختبار المعايرة
    const testFiles = [
        { file: 'test_calibrated_20_percent.txt', expected: 20, description: 'نص مع 20% عبارات مرجعية' },
        { file: 'test_calibrated_60_percent.txt', expected: 60, description: 'نص مع 60% عبارات مرجعية' },
        { file: 'test_calibrated_90_percent.txt', expected: 90, description: 'نص مع 90% عبارات مرجعية' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 تحليل عميق: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            // تشغيل الفحص مع تحليل مفصل
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            
            // تحليل مفصل للنتائج
            const analysis = await analyzeDetailedResults(result, testCase, checker);
            analysisResults.push(analysis);
            
            console.log(`   📊 النتيجة الفعلية: ${result.plagiarismPercentage}%`);
            console.log(`   🎯 الفرق عن المتوقع: ${Math.abs(result.plagiarismPercentage - testCase.expected)}%`);
            console.log(`   📈 دقة الكشف: ${analysis.accuracy.toFixed(1)}%`);
            
            // تحليل تفصيلي للخوارزميات
            console.log(`   🔬 تحليل الخوارزميات:`);
            console.log(`      Jaccard: ${(analysis.similarities.jaccard * 100).toFixed(1)}% (وزن: ${(analysis.weights.jaccard * 100).toFixed(1)}%)`);
            console.log(`      Cosine: ${(analysis.similarities.cosine * 100).toFixed(1)}% (وزن: ${(analysis.weights.cosine * 100).toFixed(1)}%)`);
            console.log(`      Levenshtein: ${(analysis.similarities.levenshtein * 100).toFixed(1)}% (وزن: ${(analysis.weights.levenshtein * 100).toFixed(1)}%)`);
            console.log(`      Semantic: ${(analysis.similarities.semantic * 100).toFixed(1)}% (وزن: ${(analysis.weights.semantic * 100).toFixed(1)}%)`);
            console.log(`      Exact Match: ${(analysis.similarities.exactMatch * 100).toFixed(1)}%`);
            
            // تحليل العتبات
            console.log(`   ⚙️ تحليل العتبات:`);
            console.log(`      العتبة المستخدمة: ${(analysis.threshold * 100).toFixed(1)}%`);
            console.log(`      النتيجة النهائية: ${(analysis.finalSimilarity * 100).toFixed(1)}%`);
            console.log(`      فوق العتبة؟ ${analysis.finalSimilarity > analysis.threshold ? 'نعم ✅' : 'لا ❌'}`);
            
            // تحليل الأجزاء المشبوهة
            console.log(`   🔍 تحليل الأجزاء المشبوهة:`);
            console.log(`      عدد الأجزاء: ${analysis.suspiciousCount}`);
            console.log(`      متوسط التشابه: ${analysis.avgSuspiciousSimilarity.toFixed(1)}%`);
            console.log(`      أعلى تشابه: ${analysis.maxSuspiciousSimilarity.toFixed(1)}%`);
            
        } catch (error) {
            console.log(`   ❌ خطأ في التحليل: ${error.message}`);
        }
    }
    
    // تحليل شامل للفجوات
    console.log('\n' + '=' .repeat(70));
    console.log('📊 تحليل شامل للفجوات المكتشفة');
    console.log('=' .repeat(70));
    
    const gapAnalysis = analyzeOverallGaps(analysisResults);
    
    // عرض الفجوات الرئيسية
    console.log(`\n🔍 الفجوات الرئيسية المكتشفة:`);
    gapAnalysis.gaps.forEach((gap, index) => {
        console.log(`   ${index + 1}. ${gap.title}`);
        console.log(`      📋 الوصف: ${gap.description}`);
        console.log(`      📊 التأثير: ${gap.impact}`);
        console.log(`      🎯 الأولوية: ${gap.priority}`);
        console.log(`      💡 الحل المقترح: ${gap.solution}`);
        console.log('');
    });
    
    // توصيات محددة
    console.log(`\n💡 التوصيات المحددة للوصول لدقة 95%:`);
    gapAnalysis.recommendations.forEach((rec, index) => {
        console.log(`   ${index + 1}. ${rec.action}`);
        console.log(`      🎯 التحسن المتوقع: ${rec.expectedImprovement}`);
        console.log(`      ⏱️ الوقت المطلوب: ${rec.timeRequired}`);
        console.log(`      🔧 التعقيد: ${rec.complexity}`);
        console.log('');
    });
    
    // حفظ تحليل الفجوات
    const reportData = {
        timestamp: new Date().toISOString(),
        analysis_type: 'deep_gap_analysis',
        current_performance: {
            accuracy: 51.7,
            success_rate: 33.3,
            processing_time: 465
        },
        target_performance: {
            accuracy: 95,
            success_rate: 90,
            processing_time: 1000
        },
        gaps: gapAnalysis.gaps,
        recommendations: gapAnalysis.recommendations,
        detailed_results: analysisResults
    };
    
    fs.writeFileSync('gap_analysis_report.json', JSON.stringify(reportData, null, 2));
    console.log(`\n💾 تم حفظ تحليل الفجوات: gap_analysis_report.json`);
    
    return gapAnalysis;
}

/**
 * تحليل مفصل لنتائج ملف واحد
 */
async function analyzeDetailedResults(result, testCase, checker) {
    const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
    const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
    
    // تحليل الأجزاء المشبوهة
    let avgSuspiciousSimilarity = 0;
    let maxSuspiciousSimilarity = 0;
    
    if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
        const similarities = result.suspiciousSegments.map(seg => seg.similarity * 100);
        avgSuspiciousSimilarity = similarities.reduce((sum, sim) => sum + sim, 0) / similarities.length;
        maxSuspiciousSimilarity = Math.max(...similarities);
    }
    
    return {
        file: testCase.file,
        expected: testCase.expected,
        actual: result.plagiarismPercentage,
        accuracy: accuracy,
        difference: difference,
        similarities: result.analysis.similarities || {},
        weights: result.analysis.weights || {},
        threshold: result.analysis.adaptiveThreshold || 0,
        finalSimilarity: result.plagiarismPercentage / 100,
        suspiciousCount: result.suspiciousSegments ? result.suspiciousSegments.length : 0,
        avgSuspiciousSimilarity: avgSuspiciousSimilarity,
        maxSuspiciousSimilarity: maxSuspiciousSimilarity,
        riskLevel: result.riskLevel.label
    };
}

/**
 * تحليل شامل للفجوات
 */
function analyzeOverallGaps(analysisResults) {
    const gaps = [
        {
            title: "قاعدة البيانات المرجعية محدودة",
            description: "130 عبارة مرجعية فقط غير كافية لكشف دقيق",
            impact: "عالي - يؤثر على جميع أنواع النصوص",
            priority: "عالية جداً",
            solution: "توسيع القاعدة إلى 500+ عبارة مع تحسين الجودة"
        },
        {
            title: "خوارزميات التشابه تحتاج تحسين",
            description: "الخوارزميات الحالية لا تكتشف التشابه الدلالي بدقة كافية",
            impact: "عالي - خاصة للنصوص المعاد صياغتها",
            priority: "عالية",
            solution: "تطوير خوارزمية hybrid مع تحليل دلالي متقدم"
        },
        {
            title: "العتبات والأوزان تحتاج ضبط دقيق",
            description: "العتبات الحالية لا تحقق التوازن المطلوب بين الدقة والحساسية",
            impact: "متوسط إلى عالي",
            priority: "متوسطة",
            solution: "استخدام machine learning لضبط تلقائي"
        },
        {
            title: "معالجة النصوص العربية تحتاج تطوير",
            description: "التحديات الخاصة بالنصوص العربية لم تُعالج بالكامل",
            impact: "عالي للنصوص العربية",
            priority: "عالية",
            solution: "تطوير معالجة متخصصة للنصوص العربية"
        },
        {
            title: "تكامل الذكاء الاصطناعي محدود",
            description: "استخدام AI حالياً محدود ولا يساهم بشكل كافي في الدقة",
            impact: "متوسط",
            priority: "متوسطة",
            solution: "تحسين تكامل AI للتحليل الدلالي المتقدم"
        }
    ];
    
    const recommendations = [
        {
            action: "توسيع قاعدة البيانات المرجعية فوراً",
            expectedImprovement: "+20-30% في الدقة",
            timeRequired: "2-3 أيام",
            complexity: "متوسط"
        },
        {
            action: "تطوير خوارزمية hybrid متقدمة",
            expectedImprovement: "+15-25% في الدقة",
            timeRequired: "1-2 أسبوع",
            complexity: "عالي"
        },
        {
            action: "ضبط ذكي للعتبات والأوزان",
            expectedImprovement: "+10-15% في الدقة",
            timeRequired: "3-5 أيام",
            complexity: "متوسط"
        },
        {
            action: "تحسين معالجة النصوص العربية",
            expectedImprovement: "+10-20% للنصوص العربية",
            timeRequired: "1 أسبوع",
            complexity: "متوسط إلى عالي"
        },
        {
            action: "تطوير تكامل AI متقدم",
            expectedImprovement: "+5-15% في الدقة",
            timeRequired: "1 أسبوع",
            complexity: "عالي"
        }
    ];
    
    return { gaps, recommendations };
}

// تشغيل التحليل
async function main() {
    try {
        const gapAnalysis = await deepGapAnalysis();
        
        console.log('\n🎯 خلاصة تحليل الفجوات:');
        console.log(`   عدد الفجوات المكتشفة: ${gapAnalysis.gaps.length}`);
        console.log(`   عدد التوصيات: ${gapAnalysis.recommendations.length}`);
        console.log(`   التحسن المتوقع الإجمالي: +50-80% في الدقة`);
        console.log(`   الوقت المطلوب للتنفيذ: 2-4 أسابيع`);
        
        console.log('\n🚀 الخطوة التالية: تنفيذ التوصيات بالترتيب المقترح');
        
    } catch (error) {
        console.error('❌ خطأ في تحليل الفجوات:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { deepGapAnalysis };
