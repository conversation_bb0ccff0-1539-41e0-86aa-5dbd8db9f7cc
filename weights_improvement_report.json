{"timestamp": "2025-07-05T00:13:19.073Z", "test_type": "weights_improvement_test", "old_weights": {"jaccard": 30, "cosine": 45, "levenshtein": 20, "semantic": 5}, "new_weights": {"jaccard": 50, "cosine": 35, "levenshtein": 12, "semantic": 3}, "results": {"avgAccuracy": 42.**************, "successRate": 33.33333333333333, "avgTime": 365, "avgThreshold": 25.666666666666664, "improvement": {"accuracy": -10.287641117052885, "successRate": 0.03333333333333144}}, "detailed_results": [{"file": "test_large_10_percent.txt", "expected": 15, "actual": 11, "accuracy": 73.33333333333333, "processingTime": 342, "threshold": 0.26, "suspiciousCount": 0, "passed": true}, {"file": "test_large_50_percent.txt", "expected": 55, "actual": 18, "accuracy": 32.727272727272734, "processingTime": 299, "threshold": 0.25, "suspiciousCount": 15, "passed": false}, {"file": "test_large_90_percent.txt", "expected": 85, "actual": 18, "accuracy": 21.17647058823529, "processingTime": 454, "threshold": 0.26, "suspiciousCount": 11, "passed": false}]}