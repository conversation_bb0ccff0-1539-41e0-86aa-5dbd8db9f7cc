const fs = require('fs');
const path = require('path');

/**
 * إصلاح قاعدة البيانات المرجعية وإنشاء ملف صحيح
 */
function fixReferenceDatabase() {
    console.log('🔧 إصلاح قاعدة البيانات المرجعية...');
    
    // قراءة الملف الحالي
    const dataPath = path.join(__dirname, 'src/data/reference_phrases.json');
    let currentData;
    
    try {
        const fileContent = fs.readFileSync(dataPath, 'utf8');
        currentData = JSON.parse(fileContent);
        console.log(`📊 الملف الحالي يحتوي على: ${currentData.academic_phrases.length} عبارة`);
    } catch (error) {
        console.error('❌ خطأ في قراءة الملف:', error.message);
        currentData = { academic_phrases: [] };
    }
    
    // إنشاء قاعدة بيانات موسعة
    const expandedPhrases = [
        // العبارات الأساسية الموجودة
        ...currentData.academic_phrases,
        
        // عبارات إضافية للوصول لـ 500+ عبارة
        "تعتبر هذه الدراسة من الدراسات الرائدة في المجال",
        "يهدف هذا البحث إلى سد الفجوة المعرفية الموجودة",
        "تساهم نتائج هذه الدراسة في إثراء الأدبيات العلمية",
        "يمكن تطبيق نتائج هذا البحث في الواقع العملي",
        "تفتح هذه الدراسة آفاقاً جديدة للبحث المستقبلي",
        "تتميز هذه الدراسة بالأصالة والحداثة في الطرح",
        "يملأ هذا البحث فجوة مهمة في الأدبيات المتخصصة",
        "تقدم هذه الدراسة إسهاماً علمياً قيماً في المجال",
        "يعتبر هذا الموضوع من الموضوعات الحديثة والمهمة",
        "تكتسب هذه الدراسة أهميتها من طبيعة الموضوع المبحوث",
        
        "اعتمدت الدراسة على المنهج الوصفي التحليلي المناسب",
        "تم استخدام المنهج التجريبي لتحقيق أهداف البحث",
        "اتبع الباحث المنهج المسحي في جمع البيانات",
        "تم تطبيق المنهج النوعي لفهم الظاهرة بعمق",
        "استخدمت الدراسة المنهج المختلط للحصول على نتائج شاملة",
        "اعتمد البحث على المنهج التاريخي لتتبع التطورات",
        "تم استخدام منهج دراسة الحالة لفهم السياق",
        "اتبعت الدراسة المنهج المقارن لإبراز الفروق",
        "استخدم الباحث المنهج الاستقرائي في التحليل",
        "تم تطبيق المنهج الاستنباطي للوصول للنتائج",
        
        "تكونت عينة الدراسة من مجموعة متنوعة من المشاركين",
        "تم اختيار العينة بطريقة عشوائية طبقية",
        "اشتملت عينة البحث على فئات مختلفة من المجتمع",
        "تم تحديد حجم العينة باستخدام المعادلات الإحصائية",
        "اختار الباحث عينة قصدية تخدم أهداف الدراسة",
        "تمثلت عينة الدراسة في مجموعة من الخبراء المتخصصين",
        "شملت العينة أفراداً من خلفيات متنوعة",
        "تم التأكد من تمثيل العينة للمجتمع الأصلي",
        "اختيرت العينة وفقاً لمعايير محددة مسبقاً",
        "تنوعت خصائص أفراد العينة لضمان الشمولية",
        
        "تم جمع البيانات باستخدام أدوات متعددة ومتنوعة",
        "استخدم الباحث الاستبانة كأداة رئيسية لجمع البيانات",
        "تم إجراء مقابلات شخصية مع عينة من المشاركين",
        "اعتمدت الدراسة على الملاحظة المباشرة كأداة للبحث",
        "تم استخدام تحليل الوثائق كمصدر للمعلومات",
        "أجرى الباحث مجموعات نقاش مركزة لجمع البيانات",
        "تم تطبيق اختبارات مقننة لقياس المتغيرات",
        "استخدمت الدراسة المقاييس المعيارية المعتمدة",
        "تم جمع البيانات من مصادر أولية وثانوية",
        "اعتمد البحث على أدوات جمع البيانات الموثوقة",
        
        "أظهرت نتائج التحليل الإحصائي وجود فروق دالة إحصائياً",
        "كشفت النتائج عن علاقة ارتباطية قوية بين المتغيرات",
        "بينت الدراسة وجود تأثير إيجابي للمتغير المستقل",
        "أشارت النتائج إلى عدم وجود فروق ذات دلالة إحصائية",
        "دلت النتائج على فعالية البرنامج المطبق في الدراسة",
        "أوضحت النتائج وجود تباين في آراء المشاركين",
        "كشف التحليل عن وجود عوامل مؤثرة متعددة",
        "بينت النتائج أن هناك حاجة لمزيد من البحث",
        "أظهرت الدراسة تحسناً ملحوظاً في المؤشرات المدروسة",
        "دلت النتائج على صحة الفرضيات المطروحة",
        
        "يوصي الباحث بضرورة تطوير البرامج التدريبية",
        "تقترح الدراسة إجراء بحوث مستقبلية في هذا المجال",
        "ينصح بتطبيق نتائج هذه الدراسة في الميدان العملي",
        "توصي الدراسة بإشراك جميع الأطراف المعنية",
        "يقترح الباحث وضع استراتيجيات واضحة للتطبيق",
        "تدعو الدراسة إلى ضرورة التطوير المستمر",
        "ينصح بتوفير الإمكانيات اللازمة للتطبيق",
        "توصي الدراسة بإجراء تقييم دوري للبرامج",
        "يقترح الباحث تطوير أدوات قياس جديدة",
        "تدعو الدراسة إلى تعزيز التعاون بين المؤسسات",
        
        "تشير الأدبيات النظرية إلى أهمية هذا الموضوع",
        "أكدت الدراسات السابقة على ضرورة البحث في هذا المجال",
        "يتفق معظم الباحثين على أهمية هذه القضية",
        "هناك إجماع في الأدبيات على ضرورة التطوير",
        "تباينت آراء الباحثين حول هذا الموضوع",
        "أشارت دراسة سابقة إلى وجود علاقة بين المتغيرين",
        "في دراسة أجراها الباحث فلان تبين أن",
        "وفقاً للنظرية المعتمدة في هذا البحث",
        "تستند هذه الدراسة إلى الإطار النظري المناسب",
        "يمكن تفسير هذه النتيجة في ضوء النظرية",
        
        "واجه الباحث عدة تحديات أثناء إجراء هذا البحث",
        "تتمثل حدود الدراسة في عدة جوانب مهمة",
        "من أبرز صعوبات البحث عدم توفر البيانات الكافية",
        "اقتصرت الدراسة على فئة محددة من المجتمع",
        "تم إجراء البحث في ظروف زمنية ومكانية محددة",
        "يُعتبر حجم العينة من محددات هذه الدراسة",
        "تأثرت نتائج البحث بعدة عوامل خارجية",
        "لم تتمكن الدراسة من تناول جميع الجوانب المطلوبة",
        "يُنصح بأخذ هذه المحددات في الاعتبار عند التطبيق",
        "رغم هذه التحديات، تمكن البحث من تحقيق أهدافه",
        
        "تم الاعتماد على مجموعة متنوعة من المراجع العلمية",
        "استند البحث إلى مصادر علمية موثوقة ومعتمدة",
        "تم الرجوع إلى أحدث الدراسات والبحوث في المجال",
        "اعتمد الباحث على مراجع عربية وأجنبية متنوعة",
        "تنوعت مصادر المعلومات بين الكتب والدوريات العلمية",
        "تم الاستعانة بقواعد البيانات العلمية المتخصصة",
        "استخدم الباحث مراجع حديثة ومعاصرة في المجال",
        "تم التأكد من مصداقية جميع المراجع المستخدمة",
        "اشتملت قائمة المراجع على مصادر متخصصة ومتنوعة",
        "تم توثيق جميع المراجع وفقاً للمعايير العلمية المعتمدة",
        
        "يتطلب تطبيق هذه النتائج توفير الإمكانيات المناسبة",
        "ينبغي مراعاة الظروف المحلية عند تطبيق التوصيات",
        "يحتاج تنفيذ التوصيات إلى تضافر جهود جميع الأطراف",
        "من الضروري وضع خطة زمنية واضحة ومحددة للتطبيق",
        "يتطلب النجاح في التطبيق دعماً قوياً من الإدارة العليا",
        "ينبغي تدريب الكوادر المختصة قبل البدء في التطبيق",
        "يحتاج التطبيق إلى متابعة مستمرة وتقييم دوري",
        "من المهم إشراك جميع الأطراف المعنية في عملية التطبيق",
        "يتطلب التطبيق الناجح توفير الموارد المالية الكافية",
        "ينبغي مراعاة التدرج في تطبيق التوصيات المقترحة",
        
        "تفتح نتائج هذا البحث المجال أمام دراسات مستقبلية متنوعة",
        "يمكن البناء على هذه النتائج في بحوث لاحقة",
        "تحتاج بعض النتائج إلى مزيد من التحقق والدراسة",
        "يُقترح إجراء دراسات طولية في هذا المجال المهم",
        "من المفيد تكرار هذه الدراسة في بيئات مختلفة",
        "تستدعي النتائج إجراء بحوث تطبيقية متخصصة",
        "يمكن توسيع نطاق البحث ليشمل متغيرات أخرى",
        "تحتاج هذه النتائج إلى تأكيد من خلال دراسات أخرى",
        "يُنصح بإجراء دراسات مقارنة بين مجتمعات مختلفة",
        "تفتح هذه الدراسة آفاقاً جديدة للبحث العلمي المتقدم"
    ];
    
    // إزالة التكرارات
    const uniquePhrases = [...new Set(expandedPhrases)];
    
    // إنشاء البيانات الجديدة
    const newData = {
        academic_phrases: uniquePhrases
    };
    
    // حفظ الملف الجديد
    try {
        fs.writeFileSync(dataPath, JSON.stringify(newData, null, 2), 'utf8');
        console.log(`✅ تم إنشاء قاعدة بيانات جديدة تحتوي على ${uniquePhrases.length} عبارة فريدة`);
        
        // التحقق من الحفظ
        const verifyData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
        console.log(`🔍 التحقق: الملف المحفوظ يحتوي على ${verifyData.academic_phrases.length} عبارة`);
        
        return uniquePhrases.length;
    } catch (error) {
        console.error('❌ خطأ في حفظ الملف:', error.message);
        return 0;
    }
}

// تشغيل الإصلاح
if (require.main === module) {
    const count = fixReferenceDatabase();
    if (count > 0) {
        console.log(`🎉 تم إصلاح قاعدة البيانات بنجاح! العدد النهائي: ${count} عبارة`);
    } else {
        console.log('❌ فشل في إصلاح قاعدة البيانات');
    }
}

module.exports = { fixReferenceDatabase };
