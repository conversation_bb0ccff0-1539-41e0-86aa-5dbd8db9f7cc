const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار تأثير تحسين أوزان الخوارزميات
 */
async function testWeightsImprovement() {
    console.log('⚖️ اختبار تأثير تحسين أوزان الخوارزميات');
    console.log('=' .repeat(60));
    console.log('🔧 الأوزان الجديدة:');
    console.log('   Jaccard: 50% (كان 30%) - الأولوية للنصوص العربية');
    console.log('   Cosine: 35% (كان 45%) - مهم لكن أقل');
    console.log('   Levenshtein: 12% (كان 20%) - أقل أهمية');
    console.log('   Semantic: 3% (كان 5%) - أقل أهمية');
    console.log('=' .repeat(60));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار
    const testFiles = [
        { file: 'test_large_10_percent.txt', expected: 15, description: 'نص أصلي' },
        { file: 'test_large_50_percent.txt', expected: 55, description: 'نص مختلط' },
        { file: 'test_large_90_percent.txt', expected: 85, description: 'نص مستل' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 70; // معيار مؤقت
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   ⚙️ العتبة: ${(result.analysis.adaptiveThreshold * 100).toFixed(1)}%`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            // عرض تفاصيل الأوزان المستخدمة
            if (result.analysis && result.analysis.weights) {
                console.log(`   ⚖️ الأوزان المستخدمة:`);
                console.log(`      Jaccard: ${(result.analysis.weights.jaccard * 100).toFixed(1)}%`);
                console.log(`      Cosine: ${(result.analysis.weights.cosine * 100).toFixed(1)}%`);
                console.log(`      Levenshtein: ${(result.analysis.weights.levenshtein * 100).toFixed(1)}%`);
                console.log(`      Semantic: ${(result.analysis.weights.semantic * 100).toFixed(1)}%`);
            }
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                threshold: result.analysis.adaptiveThreshold,
                suspiciousCount: result.suspiciousSegments.length,
                weights: result.analysis.weights,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج
    console.log('\n' + '=' .repeat(60));
    console.log('📊 تحليل تأثير تحسين الأوزان');
    console.log('=' .repeat(60));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgThreshold = validResults.reduce((sum, r) => sum + r.threshold, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`📈 النتائج بعد تحسين الأوزان:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (كان 52.7%)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (كان 33.3%)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
        console.log(`   متوسط العتبة: ${(avgThreshold * 100).toFixed(1)}%`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 52.7;
        const successImprovement = successRate - 33.3;
        
        console.log(`\n📊 التحسن المحقق:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تحليل الأوزان المستخدمة
        if (validResults[0] && validResults[0].weights) {
            console.log(`\n⚖️ متوسط الأوزان المستخدمة:`);
            const avgWeights = {
                jaccard: validResults.reduce((sum, r) => sum + (r.weights?.jaccard || 0), 0) / validResults.length,
                cosine: validResults.reduce((sum, r) => sum + (r.weights?.cosine || 0), 0) / validResults.length,
                levenshtein: validResults.reduce((sum, r) => sum + (r.weights?.levenshtein || 0), 0) / validResults.length,
                semantic: validResults.reduce((sum, r) => sum + (r.weights?.semantic || 0), 0) / validResults.length
            };
            
            console.log(`   Jaccard: ${(avgWeights.jaccard * 100).toFixed(1)}%`);
            console.log(`   Cosine: ${(avgWeights.cosine * 100).toFixed(1)}%`);
            console.log(`   Levenshtein: ${(avgWeights.levenshtein * 100).toFixed(1)}%`);
            console.log(`   Semantic: ${(avgWeights.semantic * 100).toFixed(1)}%`);
        }
        
        // تقييم التحسن
        if (avgAccuracy >= 80 && successRate >= 70) {
            console.log(`\n🎉 تحسن ممتاز! الأوزان الجديدة فعالة جداً`);
        } else if (avgAccuracy >= 65 && successRate >= 50) {
            console.log(`\n✅ تحسن جيد! الأوزان في الاتجاه الصحيح`);
        } else if (avgAccuracy > 52.7) {
            console.log(`\n📈 تحسن ملحوظ! يحتاج مزيد من التحسين`);
        } else {
            console.log(`\n⚠️ تحسن محدود! قد نحتاج أوزان مختلفة`);
        }
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج:`);
        validResults.forEach(result => {
            const improvement = result.accuracy > 70 ? '✅ تحسن' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${improvement}`);
        });
        
        // حفظ النتائج
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'weights_improvement_test',
            old_weights: { jaccard: 30, cosine: 45, levenshtein: 20, semantic: 5 },
            new_weights: { jaccard: 50, cosine: 35, levenshtein: 12, semantic: 3 },
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgThreshold: avgThreshold * 100,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results
        };
        
        fs.writeFileSync('weights_improvement_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير: weights_improvement_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            improvement: accuracyImprovement
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testWeightsImprovement();
        
        if (results) {
            console.log('\n🎯 الخلاصة:');
            console.log(`   الدقة الجديدة: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            
            if (results.avgAccuracy >= 80) {
                console.log('🎉 هدف الدقة 80%+ تحقق! ننتقل للخطوة التالية');
            } else if (results.improvement > 10) {
                console.log('📈 تحسن كبير! نحتاج مزيد من التحسينات');
            } else {
                console.log('🔧 تحسن محدود، نحتاج تحسينات إضافية');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testWeightsImprovement };
