const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار الملفات المعايرة الجديدة
 */
async function testCalibratedFiles() {
    console.log('🎯 اختبار الملفات المعايرة الجديدة');
    console.log('=' .repeat(60));
    console.log('📋 الملفات الجديدة تحتوي على عبارات من قاعدة البيانات المرجعية:');
    console.log('   📄 test_calibrated_20_percent.txt - 20% عبارات مرجعية');
    console.log('   📄 test_calibrated_60_percent.txt - 60% عبارات مرجعية');
    console.log('   📄 test_calibrated_90_percent.txt - 90% عبارات مرجعية');
    console.log('=' .repeat(60));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار المعايرة الجديدة
    const testFiles = [
        { file: 'test_calibrated_20_percent.txt', expected: 20, description: 'نص مع 20% عبارات مرجعية' },
        { file: 'test_calibrated_60_percent.txt', expected: 60, description: 'نص مع 60% عبارات مرجعية' },
        { file: 'test_calibrated_90_percent.txt', expected: 90, description: 'نص مع 90% عبارات مرجعية' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 80; // معيار أعلى للملفات المعايرة
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   ⚙️ العتبة: ${(result.analysis.adaptiveThreshold * 100).toFixed(1)}%`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            // عرض تفاصيل التشابه
            if (result.analysis && result.analysis.similarities) {
                console.log(`   🔬 تفاصيل التشابه:`);
                console.log(`      Jaccard: ${(result.analysis.similarities.jaccard * 100).toFixed(1)}%`);
                console.log(`      Cosine: ${(result.analysis.similarities.cosine * 100).toFixed(1)}%`);
                console.log(`      Levenshtein: ${(result.analysis.similarities.levenshtein * 100).toFixed(1)}%`);
                console.log(`      Semantic: ${(result.analysis.similarities.semantic * 100).toFixed(1)}%`);
                console.log(`      Exact Match Bonus: ${(result.analysis.similarities.exactMatch * 100).toFixed(1)}%`);
            }
            
            // عرض بعض الأجزاء المشبوهة
            if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
                console.log(`   📝 أمثلة على الأجزاء المشبوهة:`);
                result.suspiciousSegments.slice(0, 3).forEach((segment, index) => {
                    console.log(`      ${index + 1}. "${segment.text.substring(0, 60)}..." (${(segment.similarity * 100).toFixed(1)}%)`);
                });
            }
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                threshold: result.analysis.adaptiveThreshold,
                suspiciousCount: result.suspiciousSegments.length,
                similarities: result.analysis.similarities,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج
    console.log('\n' + '=' .repeat(60));
    console.log('📊 تحليل نتائج الملفات المعايرة');
    console.log('=' .repeat(60));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgThreshold = validResults.reduce((sum, r) => sum + r.threshold, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`📈 النتائج مع الملفات المعايرة:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (كان 36.2%)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (كان 0%)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
        console.log(`   متوسط العتبة: ${(avgThreshold * 100).toFixed(1)}%`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 36.2;
        const successImprovement = successRate - 0;
        
        console.log(`\n📊 التحسن المحقق:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تحليل متوسط التشابه
        if (validResults[0] && validResults[0].similarities) {
            console.log(`\n🔬 متوسط قيم التشابه:`);
            const avgSimilarities = {
                jaccard: validResults.reduce((sum, r) => sum + (r.similarities?.jaccard || 0), 0) / validResults.length,
                cosine: validResults.reduce((sum, r) => sum + (r.similarities?.cosine || 0), 0) / validResults.length,
                levenshtein: validResults.reduce((sum, r) => sum + (r.similarities?.levenshtein || 0), 0) / validResults.length,
                semantic: validResults.reduce((sum, r) => sum + (r.similarities?.semantic || 0), 0) / validResults.length,
                exactMatch: validResults.reduce((sum, r) => sum + (r.similarities?.exactMatch || 0), 0) / validResults.length
            };
            
            console.log(`   Jaccard: ${(avgSimilarities.jaccard * 100).toFixed(1)}%`);
            console.log(`   Cosine: ${(avgSimilarities.cosine * 100).toFixed(1)}%`);
            console.log(`   Levenshtein: ${(avgSimilarities.levenshtein * 100).toFixed(1)}%`);
            console.log(`   Semantic: ${(avgSimilarities.semantic * 100).toFixed(1)}%`);
            console.log(`   Exact Match Bonus: ${(avgSimilarities.exactMatch * 100).toFixed(1)}%`);
        }
        
        // تقييم التحسن
        if (avgAccuracy >= 85 && successRate >= 80) {
            console.log(`\n🎉 تحسن ممتاز! الملفات المعايرة تعطي نتائج دقيقة`);
        } else if (avgAccuracy >= 70 && successRate >= 60) {
            console.log(`\n✅ تحسن كبير! الملفات المعايرة في الاتجاه الصحيح`);
        } else if (avgAccuracy > 36.2) {
            console.log(`\n📈 تحسن ملحوظ! الملفات المعايرة تحسن الدقة`);
        } else {
            console.log(`\n⚠️ تحسن محدود! قد نحتاج مراجعة الخوارزميات`);
        }
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج:`);
        validResults.forEach(result => {
            const improvement = result.accuracy >= 80 ? '🎯 ممتاز' : result.accuracy >= 60 ? '📈 جيد' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${improvement}`);
        });
        
        // تحليل الأداء
        console.log(`\n⚡ تحليل الأداء:`);
        console.log(`   متوسط الوقت: ${avgTime.toFixed(0)}ms`);
        console.log(`   الهدف: < 1000ms ${avgTime < 1000 ? '✅' : '❌'}`);
        
        // تقييم تحقيق الأهداف
        console.log(`\n🎯 تقييم تحقيق الأهداف:`);
        console.log(`   دقة 95%+: ${avgAccuracy >= 95 ? '✅' : '❌'} (${avgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 90%+: ${successRate >= 90 ? '✅' : '❌'} (${successRate.toFixed(1)}%)`);
        console.log(`   أداء < 1000ms: ${avgTime < 1000 ? '✅' : '❌'} (${avgTime.toFixed(0)}ms)`);
        
        const targetsAchieved = [
            avgAccuracy >= 95,
            successRate >= 90,
            avgTime < 1000
        ].filter(Boolean).length;
        
        console.log(`   الأهداف المحققة: ${targetsAchieved}/3`);
        
        // حفظ النتائج
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'calibrated_files_test',
            description: 'اختبار ملفات معايرة تحتوي على عبارات من قاعدة البيانات المرجعية',
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgThreshold: avgThreshold * 100,
                targetsAchieved: targetsAchieved,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results
        };
        
        fs.writeFileSync('calibrated_files_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير: calibrated_files_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            targetsAchieved,
            improvement: accuracyImprovement
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testCalibratedFiles();
        
        if (results) {
            console.log('\n🎯 الخلاصة النهائية:');
            console.log(`   الدقة: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(0)}ms`);
            console.log(`   الأهداف المحققة: ${results.targetsAchieved}/3`);
            
            if (results.targetsAchieved === 3) {
                console.log('🎉 تم تحقيق جميع الأهداف! النظام جاهز للإنتاج');
            } else if (results.avgAccuracy >= 85) {
                console.log('🎯 دقة ممتازة! نحتاج تحسينات طفيفة فقط');
            } else if (results.improvement > 30) {
                console.log('📈 تحسن كبير! الملفات المعايرة تؤكد فعالية النظام');
            } else {
                console.log('🔧 نحتاج مزيد من التحسينات');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testCalibratedFiles };
