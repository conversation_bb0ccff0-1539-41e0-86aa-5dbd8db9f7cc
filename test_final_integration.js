const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار نهائي للتكامل مع التركيز على المحلل متعدد اللغات
 */
async function testFinalIntegration() {
    console.log('🎯 اختبار نهائي للتكامل مع التركيز على المحلل متعدد اللغات');
    console.log('=' .repeat(80));
    
    // إنشاء checker مع تفعيل المحلل متعدد اللغات فقط
    const checker = new PlagiarismChecker();
    
    // تأكد من تفعيل المحلل متعدد اللغات
    checker.useMultilingualAnalyzer = true;
    checker.useFinalIndependentAnalyzer = false;
    
    console.log(`🔧 إعدادات المحلل:`);
    console.log(`   🌐 المحلل متعدد اللغات: ${checker.useMultilingualAnalyzer ? '✅ مفعل' : '❌ معطل'}`);
    console.log(`   🎯 المحلل النهائي المستقل: ${checker.useFinalIndependentAnalyzer ? '✅ مفعل' : '❌ معطل'}`);
    console.log(`   🔧 مستخرج النصوص المحسن: ${checker.useEnhancedTextExtractor ? '✅ مفعل' : '❌ معطل'}`);
    
    // اختبار ملف عربي واحد
    console.log('\n🇸🇦 اختبار ملف عربي:');
    try {
        const arabicFile = path.join(__dirname, 'real-research-tests/research_low_plagiarism_15percent.txt');
        
        if (fs.existsSync(arabicFile)) {
            const startTime = Date.now();
            const result = await checker.checkFile(arabicFile);
            const endTime = Date.now();
            
            console.log(`✅ النتائج:`);
            console.log(`   📊 نسبة الاستلال: ${result.plagiarismPercentage}%`);
            console.log(`   🌐 اللغة: ${result.extractionInfo ? result.extractionInfo.languageName : 'غير محدد'}`);
            console.log(`   🔧 استخراج محسن: ${result.extractionInfo ? result.extractionInfo.enhancedExtraction : 'غير محدد'}`);
            console.log(`   🌐 تحليل متعدد اللغات: ${result.analysis && result.analysis.multilingualAnalysis ? '✅ نعم' : '❌ لا'}`);
            console.log(`   🚫 تجاهل Gemini: ${result.analysis && result.analysis.geminiIgnored ? '✅ نعم' : '❌ لا'}`);
            console.log(`   ⏱️ الوقت: ${((endTime - startTime) / 1000).toFixed(3)} ثانية`);
            
            if (result.analysis && result.analysis.multilingualAnalysis) {
                console.log('🎉 المحلل متعدد اللغات يعمل بنجاح!');
            } else {
                console.log('⚠️ المحلل متعدد اللغات لا يعمل كما متوقع');
            }
        } else {
            console.log('❌ الملف العربي غير موجود');
        }
    } catch (error) {
        console.error('❌ خطأ في اختبار الملف العربي:', error.message);
    }
    
    // إنشاء ملف إنجليزي مؤقت واختباره
    console.log('\n🇺🇸 اختبار ملف إنجليزي:');
    const englishContent = `Title: Digital Technology in Education

This study aims to investigate the relationship between digital technology and educational outcomes. The research methodology employed in this study is comprehensive, incorporating both quantitative and qualitative approaches. The findings of this research contribute to the existing literature by providing empirical evidence for the effectiveness of digital learning tools.

The comprehensive analysis provides detailed insights into the subject matter of technology integration. The systematic investigation reveals important patterns and relationships between digital tools and academic performance. The implementation framework provides systematic guidance for practitioners seeking to integrate technology effectively.`;
    
    const tempEnglishFile = path.join(__dirname, 'temp_english_test.txt');
    
    try {
        fs.writeFileSync(tempEnglishFile, englishContent, 'utf8');
        console.log('✅ تم إنشاء ملف إنجليزي مؤقت');
        
        const startTime = Date.now();
        const result = await checker.checkFile(tempEnglishFile);
        const endTime = Date.now();
        
        console.log(`✅ النتائج:`);
        console.log(`   📊 نسبة الاستلال: ${result.plagiarismPercentage}%`);
        console.log(`   🌐 اللغة: ${result.extractionInfo ? result.extractionInfo.languageName : 'غير محدد'}`);
        console.log(`   🔧 استخراج محسن: ${result.extractionInfo ? result.extractionInfo.enhancedExtraction : 'غير محدد'}`);
        console.log(`   🌐 تحليل متعدد اللغات: ${result.analysis && result.analysis.multilingualAnalysis ? '✅ نعم' : '❌ لا'}`);
        console.log(`   🚫 تجاهل Gemini: ${result.analysis && result.analysis.geminiIgnored ? '✅ نعم' : '❌ لا'}`);
        console.log(`   ⏱️ الوقت: ${((endTime - startTime) / 1000).toFixed(3)} ثانية`);
        
        if (result.analysis && result.analysis.multilingualAnalysis) {
            console.log('🎉 المحلل متعدد اللغات يعمل بنجاح للإنجليزية!');
        } else {
            console.log('⚠️ المحلل متعدد اللغات لا يعمل كما متوقع للإنجليزية');
        }
        
        // حذف الملف المؤقت
        fs.unlinkSync(tempEnglishFile);
        console.log('✅ تم حذف الملف المؤقت');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الملف الإنجليزي:', error.message);
        
        // محاولة حذف الملف المؤقت في حالة الخطأ
        try {
            if (fs.existsSync(tempEnglishFile)) {
                fs.unlinkSync(tempEnglishFile);
                console.log('✅ تم حذف الملف المؤقت (تنظيف)');
            }
        } catch (cleanupError) {
            console.log('⚠️ لم يتم حذف الملف المؤقت');
        }
    }
    
    console.log('\n' + '=' .repeat(80));
    console.log('✅ تم الانتهاء من الاختبار النهائي للتكامل');
}

// تشغيل الاختبار
async function main() {
    try {
        await testFinalIntegration();
    } catch (error) {
        console.error('❌ خطأ في الاختبار النهائي:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testFinalIntegration };
