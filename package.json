{"name": "plagiarism-checker-pro", "version": "1.0.0", "description": "تطبيق احترافي لكشف الاستلال الأكاديمي - Professional Plagiarism Detection Desktop App", "main": "src/main.js", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "dist": "electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "keywords": ["plagiarism", "checker", "academic", "desktop", "electron", "arabic", "ai"], "author": {"name": "Plagiarism Checker Pro", "email": "<EMAIL>"}, "license": "MIT", "devDependencies": {"electron": "^28.3.3", "electron-builder": "^24.13.3"}, "dependencies": {"axios": "^1.10.0", "electron-store": "^8.2.0", "fast-levenshtein": "^3.0.0", "fs-extra": "^11.3.0", "mammoth": "^1.9.1", "natural": "^6.12.0", "pdf-parse": "^1.1.1", "pdfkit": "^0.14.0"}, "build": {"appId": "com.plagiarismchecker.desktop", "productName": "Plagiarism Checker Pro", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*", "package.json"], "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Plagiarism Checker Pro"}, "extraResources": [{"from": "assets/", "to": "assets/", "filter": ["**/*"]}]}, "homepage": "https://github.com/plagiarism-checker-pro", "repository": {"type": "git", "url": "https://github.com/plagiarism-checker-pro/desktop-app.git"}}