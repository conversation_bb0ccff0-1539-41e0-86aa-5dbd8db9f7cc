const fs = require('fs');
const path = require('path');
const pdfParse = require('pdf-parse');
const mammoth = require('mammoth');

/**
 * مستخرج النصوص المحسن متعدد الصيغ واللغات
 * يدعم PDF, DOC/DOCX, TXT مع تحسينات للعربية والإنجليزية
 */
class EnhancedTextExtractor {
    constructor() {
        this.supportedFormats = ['.pdf', '.docx', '.doc', '.txt'];

        console.log('🔧 تم تهيئة مستخرج النصوص المحسن مع دعم شامل للصيغ:');
        console.log('   📄 PDF - Portable Document Format');
        console.log('   📄 DOC/DOCX - Microsoft Word Documents');
        console.log('   📄 TXT - Plain Text Files');
        console.log('   🌐 دعم كامل للعربية والإنجليزية');
        this.supportedLanguages = ['ar', 'en'];
        
        // إعدادات خاصة لكل لغة
        this.languageSettings = {
            ar: {
                name: 'العربية',
                direction: 'rtl',
                cleaningPatterns: {
                    // أنماط تنظيف خاصة بالعربية
                    diacritics: /[\u064B-\u065F\u0670\u0640]/g, // التشكيل والتطويل
                    extraSpaces: /\s+/g,
                    punctuation: /[،؛؟!]/g
                },
                academicKeywords: [
                    'دراسة', 'بحث', 'تحليل', 'نتائج', 'منهج', 'منهجية',
                    'استنتاج', 'توصيات', 'فرضية', 'عينة', 'إحصائي', 'معنوية'
                ]
            },
            en: {
                name: 'English',
                direction: 'ltr',
                cleaningPatterns: {
                    // أنماط تنظيف خاصة بالإنجليزية
                    extraSpaces: /\s+/g,
                    punctuation: /[,;?!]/g,
                    specialChars: /[^\w\s.,!?;:'"()-]/g
                },
                academicKeywords: [
                    'study', 'research', 'analysis', 'results', 'method', 'methodology',
                    'conclusion', 'recommendations', 'hypothesis', 'sample', 'statistical', 'significant'
                ]
            }
        };
        
        console.log('✅ مستخرج النصوص المحسن جاهز مع دعم شامل متعدد الصيغ واللغات');
    }

    /**
     * استخراج النص من ملف مع تحديد اللغة تلقائياً
     * @param {File|string} file - الملف أو مسار الملف
     * @returns {Promise<Object>} النص المستخرج مع معلومات اللغة
     */
    async extractText(file) {
        try {
            let filePath, fileName;
            
            if (typeof file === 'string') {
                filePath = file;
                fileName = path.basename(file);
            } else {
                filePath = file.path;
                fileName = file.name;
            }

            const extension = path.extname(fileName).toLowerCase();
            
            if (!this.supportedFormats.includes(extension)) {
                throw new Error(`نوع الملف ${extension} غير مدعوم. الصيغ المدعومة: ${this.supportedFormats.join(', ')}`);
            }

            console.log(`📄 استخراج النص من ${fileName} (${extension})`);

            let rawText;
            switch (extension) {
                case '.pdf':
                    rawText = await this.extractFromPDF(filePath);
                    break;
                case '.docx':
                    rawText = await this.extractFromDocx(filePath);
                    break;
                case '.doc':
                    rawText = await this.extractFromDoc(filePath);
                    break;
                case '.txt':
                    rawText = await this.extractFromTxt(filePath);
                    break;
                default:
                    throw new Error(`نوع الملف ${extension} غير مدعوم`);
            }

            // تحديد اللغة تلقائياً
            const detectedLanguage = this.detectLanguage(rawText);
            console.log(`🌐 اللغة المكتشفة: ${this.languageSettings[detectedLanguage].name} (${detectedLanguage})`);

            // تنظيف النص حسب اللغة
            const cleanedText = this.cleanTextByLanguage(rawText, detectedLanguage);

            // حساب الإحصائيات
            const statistics = this.getEnhancedTextStatistics(cleanedText, detectedLanguage);

            return {
                text: cleanedText,
                rawText: rawText,
                language: detectedLanguage,
                languageName: this.languageSettings[detectedLanguage].name,
                format: extension,
                formatName: extension.replace('.', '').toUpperCase(),
                fileName: fileName,
                statistics: statistics,
                extractionSuccess: true,
                enhancedExtraction: true
            };

        } catch (error) {
            console.error('❌ خطأ في استخراج النص:', error);
            throw new Error(`فشل في استخراج النص: ${error.message}`);
        }
    }

    /**
     * استخراج النص من ملف PDF مع تحسينات
     */
    async extractFromPDF(filePath) {
        try {
            const dataBuffer = fs.readFileSync(filePath);

            // خيارات محسنة لاستخراج النص من PDF مع دعم أفضل للعربية
            const options = {
                normalizeWhitespace: true,
                disableCombineTextItems: false,
                // تحسينات إضافية للنصوص العربية
                max: 0, // لا حد أقصى للصفحات
                version: 'v1.10.100' // استخدام إصدار محدد
            };

            let data;
            try {
                data = await pdfParse(dataBuffer, options);
            } catch (parseError) {
                // محاولة ثانية بخيارات مختلفة في حالة فشل الأولى
                console.log('⚠️ محاولة استخراج PDF بخيارات بديلة...');
                const fallbackOptions = {
                    normalizeWhitespace: false,
                    disableCombineTextItems: true
                };
                data = await pdfParse(dataBuffer, fallbackOptions);
            }

            if (!data.text || data.text.trim().length === 0) {
                throw new Error('الملف فارغ أو لا يحتوي على نص قابل للقراءة');
            }

            // تنظيف إضافي للنص المستخرج من PDF مع دعم أفضل للعربية
            let cleanedText = data.text
                .replace(/\s+/g, ' ') // توحيد المسافات
                .replace(/\n\s*\n/g, '\n\n') // تنظيف الأسطر الفارغة
                .replace(/[\u200B-\u200D\uFEFF]/g, '') // إزالة الأحرف غير المرئية
                .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\u0020-\u007E\s]/g, '') // الاحتفاظ بالعربية والإنجليزية فقط
                .trim();

            // إذا كان النص فارغاً أو قصيراً جداً، نحاول استخراج مختلف
            if (cleanedText.length < 50) {
                console.log('⚠️ النص المستخرج قصير جداً، محاولة استخراج بديلة...');
                // استخراج النص الخام بدون تنظيف مفرط
                cleanedText = data.text.trim();
            }

            console.log(`📊 PDF: ${data.numpages} صفحة، ${cleanedText.length} حرف`);
            return cleanedText;

        } catch (error) {
            throw new Error(`خطأ في قراءة ملف PDF: ${error.message}`);
        }
    }

    /**
     * استخراج النص من ملف Word (DOCX) مع تحسينات
     */
    async extractFromDocx(filePath) {
        try {
            const options = {
                // تحسينات لاستخراج النص من Word
                convertImage: mammoth.images.ignoreAll,
                includeDefaultStyleMap: true
            };
            
            const result = await mammoth.extractRawText({ path: filePath }, options);
            
            if (!result.value || result.value.trim().length === 0) {
                throw new Error('الملف فارغ أو لا يحتوي على نص قابل للقراءة');
            }
            
            // طباعة تحذيرات إن وجدت
            if (result.messages && result.messages.length > 0) {
                console.log('⚠️ تحذيرات استخراج Word:', result.messages.length);
            }
            
            console.log(`📊 DOCX: ${result.value.length} حرف`);
            return result.value;
            
        } catch (error) {
            throw new Error(`خطأ في قراءة ملف Word: ${error.message}`);
        }
    }

    /**
     * استخراج النص من ملف Word القديم (DOC)
     */
    async extractFromDoc(filePath) {
        try {
            // محاولة قراءة الملف كـ DOCX أولاً
            const result = await mammoth.extractRawText({ path: filePath });
            
            if (!result.value || result.value.trim().length === 0) {
                throw new Error('الملف فارغ أو لا يحتوي على نص قابل للقراءة');
            }
            
            console.log(`📊 DOC: ${result.value.length} حرف`);
            return result.value;
            
        } catch (error) {
            throw new Error(`خطأ في قراءة ملف Word القديم: ${error.message}`);
        }
    }

    /**
     * استخراج النص من ملف نصي مع تحديد الترميز
     */
    async extractFromTxt(filePath) {
        try {
            // محاولة قراءة الملف بترميزات مختلفة
            let text;
            const encodings = ['utf8', 'utf16le', 'latin1'];
            
            for (const encoding of encodings) {
                try {
                    text = fs.readFileSync(filePath, encoding);
                    if (text && text.trim().length > 0) {
                        console.log(`📊 TXT: ${text.length} حرف (${encoding})`);
                        break;
                    }
                } catch (err) {
                    continue;
                }
            }
            
            if (!text || text.trim().length === 0) {
                throw new Error('الملف فارغ أو لا يمكن قراءته');
            }
            
            return text;
            
        } catch (error) {
            throw new Error(`خطأ في قراءة الملف النصي: ${error.message}`);
        }
    }

    /**
     * تحديد لغة النص تلقائياً
     */
    detectLanguage(text) {
        if (!text || text.trim().length === 0) {
            return 'ar'; // افتراضي
        }

        // حساب نسبة الأحرف العربية
        const arabicChars = text.match(/[\u0600-\u06FF]/g);
        const englishChars = text.match(/[a-zA-Z]/g);
        
        const arabicRatio = arabicChars ? arabicChars.length / text.length : 0;
        const englishRatio = englishChars ? englishChars.length / text.length : 0;

        // تحديد اللغة بناءً على النسبة الأعلى
        if (arabicRatio > englishRatio && arabicRatio > 0.1) {
            return 'ar';
        } else if (englishRatio > arabicRatio && englishRatio > 0.1) {
            return 'en';
        } else {
            // في حالة عدم الوضوح، استخدم الكلمات المفتاحية
            const arabicKeywords = this.languageSettings.ar.academicKeywords;
            const englishKeywords = this.languageSettings.en.academicKeywords;
            
            let arabicKeywordCount = 0;
            let englishKeywordCount = 0;
            
            const lowerText = text.toLowerCase();
            
            arabicKeywords.forEach(keyword => {
                if (lowerText.includes(keyword)) arabicKeywordCount++;
            });
            
            englishKeywords.forEach(keyword => {
                if (lowerText.includes(keyword)) englishKeywordCount++;
            });
            
            return arabicKeywordCount >= englishKeywordCount ? 'ar' : 'en';
        }
    }

    /**
     * تنظيف النص حسب اللغة
     */
    cleanTextByLanguage(text, language) {
        if (!text) return '';
        
        const settings = this.languageSettings[language];
        let cleanedText = text;

        // تنظيف عام
        cleanedText = cleanedText
            .replace(/\n\s*\n/g, '\n') // إزالة الأسطر الفارغة المتعددة
            .replace(settings.cleaningPatterns.extraSpaces, ' ') // إزالة المسافات الزائدة
            .trim(); // إزالة المسافات في البداية والنهاية

        // تنظيف خاص بالعربية
        if (language === 'ar') {
            cleanedText = cleanedText
                .replace(settings.cleaningPatterns.diacritics, '') // إزالة التشكيل
                .replace(/[""]/g, '"') // توحيد علامات التنصيص
                .replace(/['']/g, "'"); // توحيد علامات الاقتباس
        }

        // تنظيف خاص بالإنجليزية
        if (language === 'en') {
            cleanedText = cleanedText
                .replace(settings.cleaningPatterns.specialChars, ' ') // إزالة الأحرف الخاصة
                .replace(/\s+([,.!?;:])/g, '$1') // إزالة المسافات قبل علامات الترقيم
                .replace(/([,.!?;:])([^\s])/g, '$1 $2'); // إضافة مسافة بعد علامات الترقيم
        }

        return cleanedText;
    }

    /**
     * حساب إحصائيات النص المحسنة
     */
    getEnhancedTextStatistics(text, language) {
        if (!text) {
            return {
                characters: 0,
                words: 0,
                sentences: 0,
                paragraphs: 0,
                language: language,
                academicDensity: 0
            };
        }

        const words = this.splitIntoWords(text, language);
        const sentences = this.splitIntoSentences(text, language);
        const paragraphs = this.splitIntoParagraphs(text);
        
        // حساب الكثافة الأكاديمية
        const academicKeywords = this.languageSettings[language].academicKeywords;
        let academicCount = 0;
        
        const lowerText = text.toLowerCase();
        academicKeywords.forEach(keyword => {
            const regex = new RegExp(keyword, 'gi');
            const matches = lowerText.match(regex);
            academicCount += matches ? matches.length : 0;
        });
        
        const academicDensity = words.length > 0 ? academicCount / words.length : 0;

        return {
            characters: text.length,
            words: words.length,
            sentences: sentences.length,
            paragraphs: paragraphs.length,
            language: language,
            languageName: this.languageSettings[language].name,
            academicDensity: academicDensity,
            academicKeywordCount: academicCount,
            averageWordsPerSentence: sentences.length > 0 ? Math.round(words.length / sentences.length) : 0,
            averageSentencesPerParagraph: paragraphs.length > 0 ? Math.round(sentences.length / paragraphs.length) : 0,
            readabilityScore: this.calculateReadabilityScore(words.length, sentences.length, language)
        };
    }

    /**
     * تقسيم النص إلى كلمات حسب اللغة
     */
    splitIntoWords(text, language) {
        if (!text) return [];
        
        let words;
        if (language === 'ar') {
            // تقسيم خاص بالعربية
            words = text
                .toLowerCase()
                .split(/\s+/)
                .filter(word => word.length > 1)
                .map(word => word.replace(/[^\u0600-\u06FF\w]/g, ''));
        } else {
            // تقسيم خاص بالإنجليزية
            words = text
                .toLowerCase()
                .split(/\s+/)
                .filter(word => word.length > 2)
                .map(word => word.replace(/[^a-zA-Z]/g, ''));
        }
        
        return words.filter(word => word.length > 0);
    }

    /**
     * تقسيم النص إلى جمل حسب اللغة
     */
    splitIntoSentences(text, language) {
        if (!text) return [];
        
        let sentencePattern;
        if (language === 'ar') {
            sentencePattern = /[.!?؟]+/;
        } else {
            sentencePattern = /[.!?]+/;
        }
        
        const sentences = text
            .split(sentencePattern)
            .map(sentence => sentence.trim())
            .filter(sentence => sentence.length > 10);
        
        return sentences;
    }

    /**
     * تقسيم النص إلى فقرات
     */
    splitIntoParagraphs(text) {
        if (!text) return [];
        
        const paragraphs = text
            .split(/\n\s*\n/)
            .map(paragraph => paragraph.trim())
            .filter(paragraph => paragraph.length > 50);
        
        return paragraphs;
    }

    /**
     * حساب نقاط القابلية للقراءة
     */
    calculateReadabilityScore(wordCount, sentenceCount, language) {
        if (sentenceCount === 0) return 0;
        
        const avgWordsPerSentence = wordCount / sentenceCount;
        
        // معادلة بسيطة للقابلية للقراءة
        if (language === 'ar') {
            // معايير خاصة بالعربية
            if (avgWordsPerSentence < 15) return 90;
            if (avgWordsPerSentence < 20) return 70;
            if (avgWordsPerSentence < 25) return 50;
            return 30;
        } else {
            // معايير خاصة بالإنجليزية
            if (avgWordsPerSentence < 12) return 90;
            if (avgWordsPerSentence < 17) return 70;
            if (avgWordsPerSentence < 22) return 50;
            return 30;
        }
    }

    /**
     * التحقق من دعم نوع الملف
     */
    isSupported(fileName) {
        const extension = path.extname(fileName).toLowerCase();
        return this.supportedFormats.includes(extension);
    }

    /**
     * الحصول على معلومات الصيغ المدعومة
     */
    getSupportedFormats() {
        return {
            formats: this.supportedFormats,
            languages: this.supportedLanguages,
            description: {
                '.pdf': 'ملفات PDF - Portable Document Format',
                '.docx': 'ملفات Word الحديثة - Microsoft Word 2007+',
                '.doc': 'ملفات Word القديمة - Microsoft Word 97-2003',
                '.txt': 'ملفات نصية عادية - Plain Text Files'
            }
        };
    }
}

module.exports = EnhancedTextExtractor;
