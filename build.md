# دليل البناء والتغليف - Plagiarism Checker Pro

## 🚀 خطوات إنتاج ملف EXE

### 1. التأكد من تثبيت جميع المكتبات
```bash
npm install
```

### 2. اختبار التطبيق
```bash
# تشغيل في وضع التطوير
npm run dev

# أو تشغيل عادي
npm start
```

### 3. بناء التطبيق للتوزيع
```bash
# بناء لنظام Windows
npm run build-win

# أو بناء عام
npm run build
```

### 4. إنتاج ملف EXE نهائي
```bash
npm run dist
```

## 📁 ملفات الإخراج

بعد تشغيل `npm run dist`، ستجد الملفات في مجلد `dist/`:

- **Plagiarism Checker Pro Setup 1.0.0.exe** - ملف التثبيت
- **win-unpacked/** - مجلد التطبيق غير المضغوط
- **Plagiarism Checker Pro 1.0.0.exe** - التطبيق المحمول

## ⚙️ إعدادات البناء

تم تكوين إعدادات البناء في `package.json`:

```json
"build": {
  "appId": "com.plagiarismchecker.desktop",
  "productName": "Plagiarism Checker Pro",
  "directories": {
    "output": "dist"
  },
  "files": [
    "src/**/*",
    "assets/**/*",
    "node_modules/**/*",
    "package.json"
  ],
  "win": {
    "target": [
      {
        "target": "nsis",
        "arch": ["x64", "ia32"]
      }
    ],
    "icon": "assets/icon.ico",
    "requestedExecutionLevel": "asInvoker"
  },
  "nsis": {
    "oneClick": false,
    "allowToChangeInstallationDirectory": true,
    "createDesktopShortcut": true,
    "createStartMenuShortcut": true,
    "shortcutName": "Plagiarism Checker Pro"
  }
}
```

## 🔧 استكشاف الأخطاء

### مشكلة: فشل في البناء
**الحل:**
```bash
# تنظيف cache
npm cache clean --force

# إعادة تثبيت المكتبات
rm -rf node_modules
npm install

# المحاولة مرة أخرى
npm run dist
```

### مشكلة: حجم الملف كبير
**الحل:**
- إزالة المكتبات غير المستخدمة
- استخدام `--publish=never` لتجنب رفع الملفات

### مشكلة: خطأ في الأذونات
**الحل:**
- تشغيل Command Prompt كمدير
- التأكد من عدم تشغيل antivirus

## 📦 التوزيع

### ملف التثبيت (NSIS)
- يتضمن installer مع خيارات التثبيت
- ينشئ shortcuts في سطح المكتب وقائمة البداية
- يدعم إلغاء التثبيت

### التطبيق المحمول
- ملف EXE واحد قابل للتشغيل مباشرة
- لا يحتاج تثبيت
- يمكن نسخه على USB

## 🎯 نصائح للتحسين

### تقليل حجم الملف
```json
"build": {
  "compression": "maximum",
  "nsis": {
    "differentialPackage": false
  }
}
```

### إضافة أيقونة مخصصة
1. ضع ملف `icon.ico` في مجلد `assets/`
2. تأكد من أن الأيقونة بحجم 256x256 أو أكبر

### إعدادات الأمان
```json
"win": {
  "certificateFile": "path/to/certificate.p12",
  "certificatePassword": "password",
  "publisherName": "Your Name"
}
```

## 🚀 النشر والتوزيع

### رفع على GitHub Releases
```bash
npm run dist -- --publish=always
```

### إنشاء Auto-updater
```json
"publish": {
  "provider": "github",
  "owner": "your-username",
  "repo": "plagiarism-checker-pro"
}
```

## 📋 قائمة التحقق النهائية

- [ ] اختبار التطبيق في وضع التطوير
- [ ] التأكد من عمل جميع الوظائف
- [ ] فحص الأيقونات والموارد
- [ ] اختبار البناء المحلي
- [ ] اختبار ملف EXE على جهاز نظيف
- [ ] التحقق من حجم الملف النهائي
- [ ] اختبار التثبيت وإلغاء التثبيت

## 🎉 النتيجة النهائية

بعد اتباع هذه الخطوات، ستحصل على:

1. **ملف تثبيت احترافي** - `Plagiarism Checker Pro Setup 1.0.0.exe`
2. **تطبيق محمول** - يعمل بدون تثبيت
3. **واجهة عربية كاملة** - تدعم RTL
4. **كشف استلال متقدم** - مع AI
5. **تقارير PDF** - احترافية ومفصلة

---

**🎯 الهدف تحقق: تطبيق سطح مكتب احترافي لكشف الاستلال جاهز للاستخدام!**
