const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار النظام المعزز بالذكاء الاصطناعي للوصول لدقة 95%+
 */
async function testAIEnhancedSystem() {
    console.log('🤖 اختبار النظام المعزز بالذكاء الاصطناعي للوصول لدقة 95%+');
    console.log('=' .repeat(80));
    console.log('🔧 التحسينات المطبقة:');
    console.log('   🤖 محلل معزز بالذكاء الاصطناعي مع تحليل دلالي متقدم');
    console.log('   🧠 نماذج ذكية للتعرف على الأنماط الأكاديمية');
    console.log('   🎯 تحليل متعدد المستويات: حرفي + دلالي + أنماط + سياق');
    console.log('   ⚖️ أوزان محسنة: Exact 40%, Semantic 30%, Pattern 20%, Context 10%');
    console.log('   🚀 تضخيم ذكي للنتائج العالية وتحليل الثقة');
    console.log('   📊 التحسن المتوقع: +60-80% في الدقة للوصول لـ 95%+');
    console.log('=' .repeat(80));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار المعايرة
    const testFiles = [
        { file: 'test_calibrated_20_percent.txt', expected: 20, description: 'نص مع 20% عبارات مرجعية' },
        { file: 'test_calibrated_60_percent.txt', expected: 60, description: 'نص مع 60% عبارات مرجعية' },
        { file: 'test_calibrated_90_percent.txt', expected: 90, description: 'نص مع 90% عبارات مرجعية' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 90; // معيار عالي جداً للذكاء الاصطناعي
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments ? result.suspiciousSegments.length : 0}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            // عرض تفاصيل التحليل المعزز بالذكاء الاصطناعي
            if (result.analysis && result.analysis.aiEnhanced) {
                console.log(`   🤖 تحليل الذكاء الاصطناعي:`);
                console.log(`      تطابق حرفي: ${result.analysis.exactMatches} مطابقة`);
                console.log(`      تطابق دلالي: ${result.analysis.semanticMatches} مطابقة`);
                console.log(`      أنماط مكتشفة: ${result.analysis.patternMatches} نمط`);
                console.log(`      تحليل السياق: ${result.analysis.contextMatches} عنصر`);
                console.log(`      نقاط الثقة: ${(result.analysis.confidenceScore * 100).toFixed(1)}%`);
                console.log(`      إجمالي المراجع: ${result.analysis.totalReferences}`);
            }
            
            // عرض أمثلة على الأجزاء المشبوهة المكتشفة بالذكاء الاصطناعي
            if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
                console.log(`   📝 أمثلة على الأجزاء المكتشفة بالذكاء الاصطناعي:`);
                result.suspiciousSegments.slice(0, 3).forEach((segment, index) => {
                    console.log(`      ${index + 1}. [${segment.type}] "${segment.text.substring(0, 60)}..." (${(segment.similarity * 100).toFixed(1)}%)`);
                });
            }
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                exactMatches: result.analysis ? result.analysis.exactMatches : 0,
                semanticMatches: result.analysis ? result.analysis.semanticMatches : 0,
                patternMatches: result.analysis ? result.analysis.patternMatches : 0,
                contextMatches: result.analysis ? result.analysis.contextMatches : 0,
                confidenceScore: result.analysis ? result.analysis.confidenceScore : 0,
                suspiciousCount: result.suspiciousSegments ? result.suspiciousSegments.length : 0,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            console.error(error.stack);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج النهائية للذكاء الاصطناعي
    console.log('\n' + '=' .repeat(80));
    console.log('📊 تحليل النتائج مع النظام المعزز بالذكاء الاصطناعي');
    console.log('=' .repeat(80));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgConfidence = validResults.reduce((sum, r) => sum + r.confidenceScore, 0) / validResults.length;
        const avgExactMatches = validResults.reduce((sum, r) => sum + r.exactMatches, 0) / validResults.length;
        const avgSemanticMatches = validResults.reduce((sum, r) => sum + r.semanticMatches, 0) / validResults.length;
        const avgPatternMatches = validResults.reduce((sum, r) => sum + r.patternMatches, 0) / validResults.length;
        const avgSuspiciousCount = validResults.reduce((sum, r) => sum + r.suspiciousCount, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`🤖 النتائج مع الذكاء الاصطناعي المعزز:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (كان 51.7%)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (كان 33.3%)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
        console.log(`   متوسط نقاط الثقة: ${(avgConfidence * 100).toFixed(1)}%`);
        console.log(`   متوسط التطابق الحرفي: ${avgExactMatches.toFixed(1)} مطابقة`);
        console.log(`   متوسط التطابق الدلالي: ${avgSemanticMatches.toFixed(1)} مطابقة`);
        console.log(`   متوسط الأنماط المكتشفة: ${avgPatternMatches.toFixed(1)} نمط`);
        console.log(`   متوسط الأجزاء المشبوهة: ${avgSuspiciousCount.toFixed(1)} جزء`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 51.7;
        const successImprovement = successRate - 33.3;
        
        console.log(`\n📊 التحسن المحقق بالذكاء الاصطناعي:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تقييم التحسن مع الذكاء الاصطناعي
        if (avgAccuracy >= 95 && successRate >= 90) {
            console.log(`\n🎉 هدف محقق! الذكاء الاصطناعي وصل للدقة المطلوبة 95%+!`);
        } else if (avgAccuracy >= 90 && successRate >= 80) {
            console.log(`\n🎯 تحسن ممتاز! الذكاء الاصطناعي قريب جداً من الهدف`);
        } else if (avgAccuracy >= 80 && successRate >= 60) {
            console.log(`\n✅ تحسن كبير! الذكاء الاصطناعي يعمل بكفاءة جيدة`);
        } else if (avgAccuracy >= 70 && successRate >= 40) {
            console.log(`\n📈 تحسن ملحوظ! الذكاء الاصطناعي في الاتجاه الصحيح`);
        } else if (avgAccuracy > 51.7) {
            console.log(`\n🔧 تحسن محدود! الذكاء الاصطناعي يحتاج مزيد من التطوير`);
        } else {
            console.log(`\n⚠️ لا يوجد تحسن! نحتاج إعادة تقييم النهج`);
        }
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج مع الذكاء الاصطناعي:`);
        validResults.forEach(result => {
            const grade = result.accuracy >= 95 ? '🎯 ممتاز' : 
                         result.accuracy >= 90 ? '🤖 ممتاز بالذكاء الاصطناعي' :
                         result.accuracy >= 80 ? '✅ جيد جداً' : 
                         result.accuracy >= 70 ? '📈 جيد' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${grade}`);
            console.log(`      🤖 AI: ${result.exactMatches}حرفي + ${result.semanticMatches}دلالي + ${result.patternMatches}نمط (ثقة: ${(result.confidenceScore * 100).toFixed(1)}%)`);
        });
        
        // تقييم تحقيق الأهداف النهائية مع الذكاء الاصطناعي
        console.log(`\n🎯 تقييم تحقيق الأهداف النهائية مع الذكاء الاصطناعي:`);
        console.log(`   دقة 95%+: ${avgAccuracy >= 95 ? '✅' : '❌'} (${avgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 90%+: ${successRate >= 90 ? '✅' : '❌'} (${successRate.toFixed(1)}%)`);
        console.log(`   أداء < 1000ms: ${avgTime < 1000 ? '✅' : '❌'} (${avgTime.toFixed(0)}ms)`);
        console.log(`   ثقة AI 80%+: ${(avgConfidence * 100) >= 80 ? '✅' : '❌'} (${(avgConfidence * 100).toFixed(1)}%)`);
        
        const targetsAchieved = [
            avgAccuracy >= 95,
            successRate >= 90,
            avgTime < 1000,
            (avgConfidence * 100) >= 80
        ].filter(Boolean).length;
        
        console.log(`   الأهداف المحققة: ${targetsAchieved}/4`);
        
        // تحليل فعالية الذكاء الاصطناعي
        console.log(`\n🧠 تحليل فعالية الذكاء الاصطناعي:`);
        console.log(`   متوسط الكشف الحرفي: ${avgExactMatches.toFixed(1)} مطابقة/ملف`);
        console.log(`   متوسط الكشف الدلالي: ${avgSemanticMatches.toFixed(1)} مطابقة/ملف`);
        console.log(`   متوسط كشف الأنماط: ${avgPatternMatches.toFixed(1)} نمط/ملف`);
        console.log(`   فعالية الكشف الإجمالية: ${((avgExactMatches + avgSemanticMatches + avgPatternMatches) / 3).toFixed(1)}`);
        
        // حفظ النتائج النهائية
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'ai_enhanced_system_test',
            ai_features: {
                semantic_analysis: true,
                pattern_recognition: true,
                context_analysis: true,
                confidence_scoring: true,
                multi_level_detection: true
            },
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgConfidence: avgConfidence * 100,
                avgExactMatches: avgExactMatches,
                avgSemanticMatches: avgSemanticMatches,
                avgPatternMatches: avgPatternMatches,
                avgSuspiciousCount: avgSuspiciousCount,
                targetsAchieved: targetsAchieved,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results
        };
        
        fs.writeFileSync('ai_enhanced_system_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير النهائي للذكاء الاصطناعي: ai_enhanced_system_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            avgConfidence: avgConfidence * 100,
            targetsAchieved,
            improvement: accuracyImprovement
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testAIEnhancedSystem();
        
        if (results) {
            console.log('\n🎯 الخلاصة النهائية للذكاء الاصطناعي:');
            console.log(`   الدقة النهائية: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن الإجمالي: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(0)}ms`);
            console.log(`   ثقة الذكاء الاصطناعي: ${results.avgConfidence.toFixed(1)}%`);
            console.log(`   الأهداف المحققة: ${results.targetsAchieved}/4`);
            
            if (results.targetsAchieved === 4) {
                console.log('🎉 تم تحقيق جميع الأهداف! الذكاء الاصطناعي ناجح تماماً!');
            } else if (results.avgAccuracy >= 95) {
                console.log('🎯 دقة ممتازة! الذكاء الاصطناعي وصل للهدف الأساسي');
            } else if (results.avgAccuracy >= 90) {
                console.log('🤖 دقة ممتازة! الذكاء الاصطناعي قريب جداً من الهدف');
            } else if (results.improvement > 30) {
                console.log('📈 تحسن كبير! الذكاء الاصطناعي فعال');
            } else {
                console.log('🔧 نحتاج مزيد من تطوير الذكاء الاصطناعي');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الذكاء الاصطناعي:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testAIEnhancedSystem };
