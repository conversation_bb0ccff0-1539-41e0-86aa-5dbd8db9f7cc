<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>محول PDF إلى DOCX المتقدم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 30px;
        }

        .header h1 {
            color: #333;
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
            line-height: 1.6;
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }

        .feature h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.2em;
        }

        .feature p {
            color: #666;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .upload-section {
            background: #f8f9fa;
            border: 2px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            margin: 30px 0;
            transition: all 0.3s ease;
        }

        .upload-section:hover {
            border-color: #764ba2;
            background: #f0f0f0;
        }

        .upload-section.dragover {
            border-color: #28a745;
            background: #e8f5e8;
        }

        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .convert-btn {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
            display: none;
        }

        .convert-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .convert-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .file-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: none;
        }

        .file-info h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        .file-info p {
            color: #333;
            margin: 5px 0;
        }

        .progress-section {
            margin: 20px 0;
            display: none;
        }

        .progress-bar {
            background: #e0e0e0;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            background: linear-gradient(45deg, #28a745, #20c997);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .status-message {
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            display: none;
        }

        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .download-section {
            display: none;
            margin: 20px 0;
        }

        .download-btn {
            background: linear-gradient(45deg, #17a2b8, #138496);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .tech-info {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            text-align: right;
        }

        .tech-info h3 {
            color: #333;
            margin-bottom: 15px;
        }

        .tech-list {
            list-style: none;
            padding: 0;
        }

        .tech-list li {
            color: #666;
            margin: 8px 0;
            padding-right: 20px;
            position: relative;
        }

        .tech-list li:before {
            content: "✅";
            position: absolute;
            right: 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px;
                margin: 10px;
            }

            .header h1 {
                font-size: 2em;
            }

            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 محول PDF إلى DOCX المتقدم</h1>
            <p>تحويل ملفات PDF إلى مستندات Word قابلة للتحرير مع الحفاظ على التنسيق والجودة العالية</p>
        </div>

        <div class="features">
            <div class="feature">
                <h3>🌐 دعم شامل للغات</h3>
                <p>دعم مثالي للعربية والإنجليزية مع تحديد تلقائي للغة واتجاه النص</p>
            </div>
            <div class="feature">
                <h3>🔧 تقنيات متقدمة</h3>
                <p>استخدام الحل الجذري المطور لاستخراج النصوص العربية بدقة عالية</p>
            </div>
            <div class="feature">
                <h3>📝 حفظ التنسيق</h3>
                <p>الحفاظ على العناوين والفقرات والقوائم والجداول مع التنسيق الأصلي</p>
            </div>
            <div class="feature">
                <h3>⚡ سرعة وجودة</h3>
                <p>تحويل سريع مع ضمان الجودة العالية ومعالجة متقدمة للأخطاء</p>
            </div>
        </div>

        <div class="upload-section" id="uploadSection">
            <div class="upload-icon">📄</div>
            <h3>اختر ملف PDF للتحويل</h3>
            <p>اسحب وأفلت ملف PDF هنا أو انقر لاختيار الملف</p>
            <input type="file" id="fileInput" class="file-input" accept=".pdf">
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                📁 اختيار ملف PDF
            </button>
        </div>

        <div class="file-info" id="fileInfo">
            <h4>📋 معلومات الملف المحدد:</h4>
            <p id="fileName"></p>
            <p id="fileSize"></p>
            <p id="fileType"></p>
        </div>

        <button class="convert-btn" id="convertBtn" onclick="convertFile()">
            🔄 تحويل إلى DOCX
        </button>

        <div class="progress-section" id="progressSection">
            <h4>⏳ جاري التحويل...</h4>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="progressText">بدء التحويل...</p>
        </div>

        <div class="status-message" id="statusMessage"></div>

        <div class="download-section" id="downloadSection">
            <h4>✅ تم التحويل بنجاح!</h4>
            <a href="#" class="download-btn" id="downloadBtn" download>
                📥 تحميل ملف DOCX
            </a>
        </div>

        <div class="tech-info">
            <h3>🔧 التقنيات المستخدمة:</h3>
            <ul class="tech-list">
                <li>الحل الجذري لاستخراج النصوص العربية</li>
                <li>تحليل متقدم للغة والهيكل</li>
                <li>تحسين ثوري للنصوص</li>
                <li>حل طارئ ذكي للحالات الصعبة</li>
                <li>معالجة متقدمة للأخطاء</li>
                <li>دعم كامل للتنسيق والخطوط</li>
            </ul>
        </div>
    </div>

    <script>
        let selectedFile = null;

        // إعداد منطقة السحب والإفلات
        const uploadSection = document.getElementById('uploadSection');
        const fileInput = document.getElementById('fileInput');
        const fileInfo = document.getElementById('fileInfo');
        const convertBtn = document.getElementById('convertBtn');

        // منع السلوك الافتراضي للسحب والإفلات
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadSection.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // تأثيرات بصرية للسحب والإفلات
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadSection.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadSection.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            uploadSection.classList.add('dragover');
        }

        function unhighlight(e) {
            uploadSection.classList.remove('dragover');
        }

        // معالجة إفلات الملف
        uploadSection.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }

        // معالجة اختيار الملف
        fileInput.addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });

        function handleFiles(files) {
            if (files.length > 0) {
                const file = files[0];
                if (file.type === 'application/pdf') {
                    selectedFile = file;
                    showFileInfo(file);
                } else {
                    showStatus('يرجى اختيار ملف PDF صالح', 'error');
                }
            }
        }

        function showFileInfo(file) {
            document.getElementById('fileName').textContent = `الاسم: ${file.name}`;
            document.getElementById('fileSize').textContent = `الحجم: ${formatFileSize(file.size)}`;
            document.getElementById('fileType').textContent = `النوع: ${file.type}`;
            
            fileInfo.style.display = 'block';
            convertBtn.style.display = 'inline-block';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 بايت';
            const k = 1024;
            const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showStatus(message, type) {
            const statusMessage = document.getElementById('statusMessage');
            statusMessage.textContent = message;
            statusMessage.className = `status-message status-${type}`;
            statusMessage.style.display = 'block';
            
            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 5000);
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = text;
        }

        async function convertFile() {
            if (!selectedFile) {
                showStatus('يرجى اختيار ملف PDF أولاً', 'error');
                return;
            }

            // إخفاء الأقسام غير المطلوبة
            document.getElementById('downloadSection').style.display = 'none';
            document.getElementById('statusMessage').style.display = 'none';
            
            // إظهار شريط التقدم
            document.getElementById('progressSection').style.display = 'block';
            convertBtn.disabled = true;

            try {
                // محاكاة عملية التحويل (في التطبيق الحقيقي، ستكون هذه استدعاءات API)
                updateProgress(10, 'بدء استخراج النص من PDF...');
                await sleep(1000);

                updateProgress(30, 'تطبيق الحل الجذري لاستخراج النصوص العربية...');
                await sleep(1500);

                updateProgress(50, 'تحليل وتنظيم المحتوى...');
                await sleep(1000);

                updateProgress(70, 'إنشاء مستند Word مع التنسيق المناسب...');
                await sleep(1500);

                updateProgress(90, 'حفظ المستند النهائي...');
                await sleep(800);

                updateProgress(100, 'تم التحويل بنجاح!');
                await sleep(500);

                // إخفاء شريط التقدم وإظهار رابط التحميل
                document.getElementById('progressSection').style.display = 'none';
                
                // إنشاء ملف وهمي للتحميل (في التطبيق الحقيقي، سيكون هذا الملف المحول فعلياً)
                const dummyContent = createDummyDocxContent();
                const blob = new Blob([dummyContent], { type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' });
                const url = URL.createObjectURL(blob);
                
                const downloadBtn = document.getElementById('downloadBtn');
                downloadBtn.href = url;
                downloadBtn.download = selectedFile.name.replace('.pdf', '.docx');
                
                document.getElementById('downloadSection').style.display = 'block';
                showStatus('تم تحويل الملف بنجاح! يمكنك الآن تحميل ملف DOCX', 'success');

            } catch (error) {
                document.getElementById('progressSection').style.display = 'none';
                showStatus('حدث خطأ أثناء التحويل: ' + error.message, 'error');
            } finally {
                convertBtn.disabled = false;
            }
        }

        function createDummyDocxContent() {
            // محتوى وهمي لملف DOCX (في التطبيق الحقيقي، سيكون هذا المحتوى المحول فعلياً)
            return `تم تحويل الملف: ${selectedFile.name}\nتاريخ التحويل: ${new Date().toLocaleString('ar-SA')}\n\nهذا مثال على المحتوى المحول من PDF إلى DOCX باستخدام المحول المتقدم.`;
        }

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // رسالة ترحيب
        window.addEventListener('load', function() {
            console.log('🚀 محول PDF إلى DOCX المتقدم جاهز للاستخدام');
            console.log('✅ دعم شامل للعربية والإنجليزية');
            console.log('🔧 استخدام الحل الجذري المطور');
        });
    </script>
</body>
</html>
