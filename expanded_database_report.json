{"timestamp": "2025-07-05T00:55:03.602Z", "test_type": "expanded_database_test", "database_expansion": {"old_size": 130, "new_size": "410+", "increase_percentage": 215}, "results": {"avgAccuracy": 51.666666666666664, "successRate": 33.33333333333333, "avgTime": 377, "avgThreshold": 44.333333333333336, "targetsAchieved": 1, "improvement": {"accuracy": -0.033333333333338544, "successRate": 0.03333333333333144}}, "detailed_results": [{"file": "test_calibrated_20_percent.txt", "expected": 20, "actual": 70, "accuracy": 0, "processingTime": 325, "threshold": 0.45, "suspiciousCount": 15, "passed": false}, {"file": "test_calibrated_60_percent.txt", "expected": 60, "actual": 69, "accuracy": 85, "processingTime": 354, "threshold": 0.45, "suspiciousCount": 15, "passed": true}, {"file": "test_calibrated_90_percent.txt", "expected": 90, "actual": 63, "accuracy": 70, "processingTime": 452, "threshold": 0.43, "suspiciousCount": 15, "passed": false}]}