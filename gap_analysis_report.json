{"timestamp": "2025-07-05T00:50:37.668Z", "analysis_type": "deep_gap_analysis", "current_performance": {"accuracy": 51.7, "success_rate": 33.3, "processing_time": 465}, "target_performance": {"accuracy": 95, "success_rate": 90, "processing_time": 1000}, "gaps": [{"title": "قاعدة البيانات المرجعية محدودة", "description": "130 عبارة مرجعية فقط غير كافية لكشف دقيق", "impact": "عالي - يؤثر على جميع أنواع النصوص", "priority": "عالية جداً", "solution": "توسيع القاعدة إلى 500+ عبارة مع تحسين الجودة"}, {"title": "خوارزميات التشابه تحتاج تحسين", "description": "الخوارزميات الحالية لا تكتشف التشابه الدلالي بدقة كافية", "impact": "عالي - خاصة للنصوص المعاد صياغتها", "priority": "عالية", "solution": "تطوير خوارزمية hybrid مع تحليل دلالي متقدم"}, {"title": "العتبات والأوزان تحتاج ضبط دقيق", "description": "العتبات الحالية لا تحقق التوازن المطلوب بين الدقة والحساسية", "impact": "متوسط إلى عالي", "priority": "متوسطة", "solution": "استخدام machine learning لضبط تلقائي"}, {"title": "معالجة النصوص العربية تحتاج تطوير", "description": "التحديات الخاصة بالنصوص العربية لم تُعالج بالكامل", "impact": "عالي للنصوص العربية", "priority": "عالية", "solution": "تطوير معالجة متخصصة للنصوص العربية"}, {"title": "تكامل الذكاء الاصطناعي محدود", "description": "استخدام AI حالياً محدود ولا يساهم بشكل كافي في الدقة", "impact": "متوسط", "priority": "متوسطة", "solution": "تحسين تكامل AI للتحليل الدلالي المتقدم"}], "recommendations": [{"action": "توسيع قاعدة البيانات المرجعية فوراً", "expectedImprovement": "+20-30% في الدقة", "timeRequired": "2-3 <PERSON><PERSON><PERSON><PERSON>", "complexity": "متوسط"}, {"action": "تطوير خوارزمية hybrid متقدمة", "expectedImprovement": "+15-25% في الدقة", "timeRequired": "1-2 أسبوع", "complexity": "عالي"}, {"action": "ضبط ذكي للعتبات والأوزان", "expectedImprovement": "+10-15% في الدقة", "timeRequired": "3-5 <PERSON><PERSON><PERSON><PERSON>", "complexity": "متوسط"}, {"action": "تحسين معالجة النصوص العربية", "expectedImprovement": "+10-20% للنصوص العربية", "timeRequired": "1 أسبوع", "complexity": "متوسط إلى عالي"}, {"action": "تطوير تكامل AI متقدم", "expectedImprovement": "+5-15% في الدقة", "timeRequired": "1 أسبوع", "complexity": "عالي"}], "detailed_results": [{"file": "test_calibrated_20_percent.txt", "expected": 20, "actual": 66, "accuracy": 0, "difference": 46, "similarities": {}, "weights": {}, "threshold": 0.45, "finalSimilarity": 0.66, "suspiciousCount": 15, "avgSuspiciousSimilarity": 77.3196304366271, "maxSuspiciousSimilarity": 92.87804491538634, "riskLevel": "متوسط"}, {"file": "test_calibrated_60_percent.txt", "expected": 60, "actual": 69, "accuracy": 85, "difference": 9, "similarities": {}, "weights": {}, "threshold": 0.45, "finalSimilarity": 0.69, "suspiciousCount": 15, "avgSuspiciousSimilarity": 78.00749763953654, "maxSuspiciousSimilarity": 89.75597640416855, "riskLevel": "متوسط"}, {"file": "test_calibrated_90_percent.txt", "expected": 90, "actual": 63, "accuracy": 70, "difference": 27, "similarities": {}, "weights": {}, "threshold": 0.43, "finalSimilarity": 0.63, "suspiciousCount": 15, "avgSuspiciousSimilarity": 70.39907597893615, "maxSuspiciousSimilarity": 78.93301423474468, "riskLevel": "متوسط"}]}