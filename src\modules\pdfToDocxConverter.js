const fs = require('fs');
const path = require('path');
const { Document, Packer, Paragraph, TextRun, HeadingLevel, AlignmentType, Table, TableRow, TableCell } = require('docx');
const EnhancedTextExtractor = require('./enhancedTextExtractor');

/**
 * محول PDF إلى DOCX متقدم
 * يستفيد من الحل الجذري المطور لاستخراج النصوص العربية
 * مع الحفاظ على التنسيق والجودة العالية
 */
class PDFToDocxConverter {
    constructor() {
        this.textExtractor = new EnhancedTextExtractor();
        this.supportedLanguages = {
            ar: {
                name: 'العربية',
                direction: 'rtl',
                fontFamily: 'Arial Unicode MS'
            },
            en: {
                name: 'English',
                direction: 'ltr',
                fontFamily: '<PERSON><PERSON><PERSON>'
            }
        };
        
        console.log('🔄 تم تهيئة محول PDF إلى DOCX المتقدم');
        console.log('✅ دعم شامل للعربية والإنجليزية مع الحل الجذري');
    }

    /**
     * تحويل ملف PDF إلى DOCX
     * @param {string} pdfPath - مسار ملف PDF
     * @param {string} outputPath - مسار ملف DOCX الناتج
     * @param {Object} options - خيارات التحويل
     */
    async convertPDFToDocx(pdfPath, outputPath, options = {}) {
        try {
            console.log(`🚀 بدء تحويل PDF إلى DOCX: ${path.basename(pdfPath)}`);
            console.log('🔧 استخدام الحل الجذري المطور لاستخراج النصوص العربية');
            
            // التحقق من وجود الملف
            if (!fs.existsSync(pdfPath)) {
                throw new Error(`ملف PDF غير موجود: ${pdfPath}`);
            }

            // استخراج النص باستخدام الحل الجذري المطور
            const startTime = Date.now();
            const extractedContent = await this.extractContentWithAdvancedTechniques(pdfPath);
            const extractionTime = Date.now() - startTime;
            
            console.log(`📊 تم استخراج المحتوى في ${extractionTime}ms`);
            console.log(`📄 النص: ${extractedContent.text.length} حرف`);
            console.log(`🌐 اللغة: ${extractedContent.language} (${this.supportedLanguages[extractedContent.language].name})`);
            
            // تحليل وتنظيم المحتوى
            const structuredContent = this.analyzeAndStructureContent(extractedContent);
            
            // إنشاء مستند Word مع التنسيق المناسب
            const docxDocument = await this.createFormattedDocxDocument(structuredContent, options);
            
            // حفظ المستند
            await this.saveDocxDocument(docxDocument, outputPath);
            
            const totalTime = Date.now() - startTime;
            console.log(`✅ تم التحويل بنجاح في ${totalTime}ms`);
            console.log(`📁 الملف محفوظ في: ${outputPath}`);
            
            return {
                success: true,
                inputFile: pdfPath,
                outputFile: outputPath,
                language: extractedContent.language,
                languageName: this.supportedLanguages[extractedContent.language].name,
                textLength: extractedContent.text.length,
                processingTime: totalTime,
                extractionTime: extractionTime,
                structure: {
                    paragraphs: structuredContent.paragraphs.length,
                    headings: structuredContent.headings.length,
                    lists: structuredContent.lists.length,
                    tables: structuredContent.tables.length
                }
            };
            
        } catch (error) {
            console.error(`❌ خطأ في تحويل PDF إلى DOCX:`, error.message);
            throw new Error(`فشل تحويل PDF إلى DOCX: ${error.message}`);
        }
    }

    /**
     * استخراج المحتوى باستخدام التقنيات المتقدمة
     */
    async extractContentWithAdvancedTechniques(pdfPath) {
        console.log('🔧 تطبيق التقنيات المتقدمة لاستخراج المحتوى...');

        try {
            // استخدام مستخرج النصوص المحسن مع الحل الجذري
            const extractionResult = await this.textExtractor.extractText(pdfPath);

            // استخراج النص من النتيجة
            const textString = extractionResult.text || extractionResult.rawText || '';
            const detectedLanguage = extractionResult.language || 'en';

            console.log(`🔍 النص المستخرج: ${textString.length} حرف`);
            console.log(`📝 أول 100 حرف: "${textString.substring(0, 100)}..."`);
            console.log(`🌐 اللغة من المستخرج: ${extractionResult.languageName} (${detectedLanguage})`);

            if (!textString || textString.trim().length === 0) {
                throw new Error('لم يتم استخراج أي نص من الملف');
            }

            // تحسين النص باستخدام التقنيات الثورية
            const enhancedText = this.applyRevolutionaryTextEnhancement(textString, detectedLanguage);
            console.log(`🔧 النص المحسن: ${enhancedText.length} حرف`);

            // استخراج معلومات إضافية
            const metadata = this.extractDocumentMetadata(enhancedText, detectedLanguage);

            return {
                text: enhancedText,
                language: detectedLanguage,
                metadata: metadata,
                originalLength: textString.length,
                enhancedLength: enhancedText.length,
                extractionInfo: extractionResult
            };

        } catch (error) {
            console.error('❌ خطأ في استخراج المحتوى:', error.message);
            throw error;
        }
    }

    /**
     * تطبيق التحسين الثوري للنص (مستوحى من الحل الجذري)
     */
    applyRevolutionaryTextEnhancement(text, language) {
        console.log('🔧 تطبيق التحسين الثوري للنص...');

        // التأكد من أن النص هو string
        let enhancedText = typeof text === 'string' ? text : String(text || '');

        if (!enhancedText || enhancedText.trim().length === 0) {
            console.log('⚠️ النص فارغ، لا يمكن التحسين');
            return '';
        }

        if (language === 'ar') {
            // تحسينات خاصة بالعربية
            enhancedText = this.enhanceArabicText(enhancedText);
        } else {
            // تحسينات خاصة بالإنجليزية
            enhancedText = this.enhanceEnglishText(enhancedText);
        }

        // تحسينات عامة
        enhancedText = this.applyGeneralEnhancements(enhancedText);

        return enhancedText;
    }

    /**
     * تحسين النص العربي
     */
    enhanceArabicText(text) {
        return text
            .replace(/[\u200B-\u200D\uFEFF\u061C]/g, '') // إزالة الأحرف غير المرئية
            .replace(/\s+/g, ' ') // توحيد المسافات
            .replace(/\n\s*\n/g, '\n\n') // تنظيف الأسطر الفارغة
            .replace(/([.!?])\s*\n/g, '$1\n\n') // إضافة مسافة بعد نهاية الجمل
            .trim();
    }

    /**
     * تحسين النص الإنجليزي
     */
    enhanceEnglishText(text) {
        return text
            .replace(/\s+/g, ' ') // توحيد المسافات
            .replace(/\n\s*\n/g, '\n\n') // تنظيف الأسطر الفارغة
            .replace(/([.!?])\s*\n/g, '$1\n\n') // إضافة مسافة بعد نهاية الجمل
            .replace(/\b(the|and|or|but|in|on|at|to|for|of|with|by)\s+/gi, (match) => match.toLowerCase()) // تصحيح حالة الأحرف
            .trim();
    }

    /**
     * تطبيق تحسينات عامة
     */
    applyGeneralEnhancements(text) {
        return text
            .replace(/\t/g, '    ') // تحويل التابات إلى مسافات
            .replace(/\r\n/g, '\n') // توحيد نهايات الأسطر
            .replace(/\r/g, '\n') // توحيد نهايات الأسطر
            .trim();
    }

    /**
     * استخراج معلومات المستند
     */
    extractDocumentMetadata(text, language) {
        const lines = text.split('\n').filter(line => line.trim().length > 0);
        const words = text.split(/\s+/).filter(word => word.length > 0);
        
        // تحديد العناوين المحتملة
        const potentialHeadings = lines.filter(line => {
            const trimmed = line.trim();
            return trimmed.length < 100 && 
                   (trimmed.match(/^[A-Z\u0600-\u06FF]/) || 
                    trimmed.includes(':') || 
                    trimmed.match(/^\d+\./));
        });

        // تحديد الفقرات
        const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 0);

        return {
            totalLines: lines.length,
            totalWords: words.length,
            totalCharacters: text.length,
            potentialHeadings: potentialHeadings.length,
            paragraphs: paragraphs.length,
            language: language,
            estimatedReadingTime: Math.ceil(words.length / (language === 'ar' ? 150 : 200)) // كلمة في الدقيقة
        };
    }

    /**
     * تحليل وتنظيم المحتوى
     */
    analyzeAndStructureContent(extractedContent) {
        console.log('📊 تحليل وتنظيم المحتوى...');
        
        const { text, language, metadata } = extractedContent;
        const lines = text.split('\n').filter(line => line.trim().length > 0);
        
        const structure = {
            headings: [],
            paragraphs: [],
            lists: [],
            tables: [],
            language: language,
            direction: this.supportedLanguages[language].direction
        };

        let currentSection = null;
        
        for (let i = 0; i < lines.length; i++) {
            const line = lines[i].trim();
            
            if (this.isHeading(line, language)) {
                structure.headings.push({
                    text: line,
                    level: this.determineHeadingLevel(line),
                    index: i
                });
                currentSection = 'heading';
            } else if (this.isList(line)) {
                if (currentSection !== 'list') {
                    structure.lists.push([]);
                }
                structure.lists[structure.lists.length - 1].push(line);
                currentSection = 'list';
            } else if (this.isTable(line)) {
                if (currentSection !== 'table') {
                    structure.tables.push([]);
                }
                structure.tables[structure.tables.length - 1].push(line);
                currentSection = 'table';
            } else if (line.length > 10) { // فقرة عادية
                structure.paragraphs.push({
                    text: line,
                    index: i
                });
                currentSection = 'paragraph';
            }
        }

        console.log(`📊 تم تحليل المحتوى: ${structure.headings.length} عنوان، ${structure.paragraphs.length} فقرة، ${structure.lists.length} قائمة، ${structure.tables.length} جدول`);
        
        return structure;
    }

    /**
     * تحديد ما إذا كان السطر عنواناً
     */
    isHeading(line, language) {
        if (line.length > 100) return false;
        
        // أنماط العناوين العربية
        if (language === 'ar') {
            return line.match(/^(الفصل|الباب|المبحث|المطلب|أولاً|ثانياً|ثالثاً|رابعاً|خامساً)/) ||
                   line.match(/^\d+[\.\-\s]/) ||
                   line.match(/^[أ-ي][\.\-\s]/) ||
                   (line.length < 50 && line.includes(':'));
        }
        
        // أنماط العناوين الإنجليزية
        return line.match(/^(Chapter|Section|Part|Introduction|Conclusion|Abstract|References)/) ||
               line.match(/^\d+[\.\-\s]/) ||
               line.match(/^[A-Z][a-z]*:/) ||
               (line.length < 50 && line.match(/^[A-Z]/));
    }

    /**
     * تحديد مستوى العنوان
     */
    determineHeadingLevel(line) {
        if (line.match(/^(الفصل|Chapter|Part)/)) return 1;
        if (line.match(/^(الباب|Section|المبحث)/)) return 2;
        if (line.match(/^(المطلب|Subsection)/)) return 3;
        if (line.match(/^\d+\./)) return 2;
        if (line.match(/^\d+\.\d+/)) return 3;
        return 2; // افتراضي
    }

    /**
     * تحديد ما إذا كان السطر قائمة
     */
    isList(line) {
        return line.match(/^[\-\*\+•]\s/) ||
               line.match(/^\d+[\.\)]\s/) ||
               line.match(/^[أ-ي][\.\)]\s/) ||
               line.match(/^[a-z][\.\)]\s/);
    }

    /**
     * تحديد ما إذا كان السطر جدولاً
     */
    isTable(line) {
        return line.includes('|') && line.split('|').length > 2;
    }

    /**
     * إنشاء مستند Word مع التنسيق
     */
    async createFormattedDocxDocument(structuredContent, options = {}) {
        console.log('📝 إنشاء مستند Word مع التنسيق المناسب...');

        const { language, direction } = structuredContent;
        const languageSettings = this.supportedLanguages[language];

        // إضافة العناوين والفقرات
        const allContent = this.mergeContentByOrder(structuredContent);
        const sections = [];

        // إذا لم يكن هناك محتوى، أضف فقرة افتراضية
        if (allContent.length === 0) {
            sections.push(new Paragraph({
                children: [
                    new TextRun({
                        text: 'تم تحويل هذا المستند من PDF إلى DOCX باستخدام المحول المتقدم.',
                        font: languageSettings.fontFamily,
                        size: 24
                    })
                ],
                alignment: direction === 'rtl' ? AlignmentType.RIGHT : AlignmentType.LEFT
            }));
        } else {
            // إضافة المحتوى المستخرج
            for (const item of allContent) {
                if (item.type === 'heading') {
                    sections.push(new Paragraph({
                        children: [
                            new TextRun({
                                text: item.text,
                                font: languageSettings.fontFamily,
                                size: item.level === 1 ? 32 : 28,
                                bold: true
                            })
                        ],
                        alignment: direction === 'rtl' ? AlignmentType.RIGHT : AlignmentType.LEFT,
                        spacing: { after: 240, before: 240 }
                    }));
                } else if (item.type === 'paragraph') {
                    sections.push(new Paragraph({
                        children: [
                            new TextRun({
                                text: item.text,
                                font: languageSettings.fontFamily,
                                size: 24
                            })
                        ],
                        alignment: direction === 'rtl' ? AlignmentType.RIGHT : AlignmentType.LEFT,
                        spacing: { after: 120 }
                    }));
                } else if (item.type === 'list') {
                    for (const listItem of item.items) {
                        sections.push(new Paragraph({
                            children: [
                                new TextRun({
                                    text: listItem,
                                    font: languageSettings.fontFamily,
                                    size: 24
                                })
                            ],
                            alignment: direction === 'rtl' ? AlignmentType.RIGHT : AlignmentType.LEFT,
                            indent: { left: 720 }, // مسافة بادئة للقائمة
                            spacing: { after: 60 }
                        }));
                    }
                }
            }
        }

        // إنشاء المستند مع الأقسام
        const doc = new Document({
            creator: 'PDF to DOCX Converter',
            title: options.title || 'مستند محول من PDF',
            description: 'تم التحويل باستخدام محول PDF إلى DOCX المتقدم',
            sections: [
                {
                    properties: {},
                    children: sections
                }
            ]
        });

        return doc;
    }

    /**
     * دمج المحتوى حسب الترتيب الأصلي
     */
    mergeContentByOrder(structuredContent) {
        const allItems = [];
        
        // إضافة العناوين
        structuredContent.headings.forEach(heading => {
            allItems.push({
                type: 'heading',
                text: heading.text,
                level: heading.level,
                index: heading.index
            });
        });
        
        // إضافة الفقرات
        structuredContent.paragraphs.forEach(paragraph => {
            allItems.push({
                type: 'paragraph',
                text: paragraph.text,
                index: paragraph.index
            });
        });
        
        // إضافة القوائم
        structuredContent.lists.forEach((list, listIndex) => {
            allItems.push({
                type: 'list',
                items: list,
                index: 1000 + listIndex // ترتيب تقريبي
            });
        });
        
        // ترتيب حسب الفهرس الأصلي
        return allItems.sort((a, b) => a.index - b.index);
    }

    /**
     * حفظ مستند Word
     */
    async saveDocxDocument(doc, outputPath) {
        console.log(`💾 حفظ مستند Word في: ${outputPath}`);
        
        try {
            // إنشاء المجلد إذا لم يكن موجوداً
            const outputDir = path.dirname(outputPath);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }
            
            // إنشاء وحفظ المستند
            const buffer = await Packer.toBuffer(doc);
            fs.writeFileSync(outputPath, buffer);
            
            console.log('✅ تم حفظ المستند بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في حفظ المستند:', error.message);
            throw new Error(`فشل حفظ المستند: ${error.message}`);
        }
    }

    /**
     * تحويل متعدد الملفات
     */
    async convertMultipleFiles(inputDir, outputDir, options = {}) {
        console.log(`🔄 تحويل متعدد الملفات من: ${inputDir} إلى: ${outputDir}`);
        
        try {
            const pdfFiles = fs.readdirSync(inputDir)
                .filter(file => file.toLowerCase().endsWith('.pdf'))
                .map(file => path.join(inputDir, file));
            
            if (pdfFiles.length === 0) {
                throw new Error('لا توجد ملفات PDF في المجلد المحدد');
            }
            
            console.log(`📄 تم العثور على ${pdfFiles.length} ملف PDF`);
            
            const results = [];
            let successCount = 0;
            let failCount = 0;
            
            for (const pdfFile of pdfFiles) {
                try {
                    const fileName = path.basename(pdfFile, '.pdf');
                    const outputFile = path.join(outputDir, `${fileName}.docx`);
                    
                    console.log(`\n🔄 تحويل: ${path.basename(pdfFile)}`);
                    const result = await this.convertPDFToDocx(pdfFile, outputFile, options);
                    
                    results.push(result);
                    successCount++;
                    console.log(`✅ نجح: ${path.basename(pdfFile)}`);
                    
                } catch (error) {
                    console.error(`❌ فشل: ${path.basename(pdfFile)} - ${error.message}`);
                    results.push({
                        success: false,
                        inputFile: pdfFile,
                        error: error.message
                    });
                    failCount++;
                }
            }
            
            console.log(`\n📊 ملخص التحويل المتعدد:`);
            console.log(`✅ نجح: ${successCount}/${pdfFiles.length}`);
            console.log(`❌ فشل: ${failCount}/${pdfFiles.length}`);
            
            return {
                totalFiles: pdfFiles.length,
                successCount,
                failCount,
                results
            };
            
        } catch (error) {
            console.error('❌ خطأ في التحويل المتعدد:', error.message);
            throw error;
        }
    }

    /**
     * الحصول على معلومات الدعم
     */
    getSupportInfo() {
        return {
            supportedInputFormats: ['PDF'],
            supportedOutputFormats: ['DOCX'],
            supportedLanguages: Object.keys(this.supportedLanguages),
            features: [
                'استخراج نص متقدم باستخدام الحل الجذري',
                'دعم شامل للعربية والإنجليزية',
                'الحفاظ على التنسيق والهيكل',
                'تحديد تلقائي للغة واتجاه النص',
                'معالجة العناوين والفقرات والقوائم',
                'تحويل متعدد الملفات',
                'معالجة متقدمة للأخطاء'
            ],
            version: '1.0.0',
            author: 'PDF to DOCX Converter - Advanced Edition'
        };
    }
}

module.exports = PDFToDocxConverter;
