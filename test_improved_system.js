const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار النظام المحسن مع قاعدة البيانات الموسعة
 */
async function testImprovedSystem() {
    console.log('🚀 اختبار النظام المحسن مع قاعدة البيانات الموسعة');
    console.log('=' .repeat(70));
    console.log('🔧 التحسينات المطبقة:');
    console.log('   📚 قاعدة بيانات موسعة: 198 عبارة مرجعية');
    console.log('   🔧 محلل قابل للتخصيص مع إعدادات محسنة');
    console.log('   ⚖️ أوزان محسنة للنصوص العربية');
    console.log('   🎯 التحسن المتوقع: +40-60% في الدقة');
    console.log('=' .repeat(70));
    
    const checker = new PlagiarismChecker();
    
    // تطبيق أفضل إعدادات من المعايرة السابقة
    const bestConfig = {
        thresholds: {
            high: 0.45,     // 45%
            medium: 0.30,   // 30%
            low: 0.18       // 18%
        },
        weights: {
            jaccard: 0.45,      // 45%
            cosine: 0.40,       // 40%
            levenshtein: 0.12,  // 12%
            semantic: 0.03      // 3%
        }
    };
    
    checker.updateAnalyzerConfig(bestConfig);
    console.log('⚙️ تم تطبيق أفضل إعدادات من المعايرة السابقة');
    
    const results = [];
    
    // ملفات الاختبار المعايرة
    const testFiles = [
        { file: 'test_calibrated_20_percent.txt', expected: 20, description: 'نص مع 20% عبارات مرجعية' },
        { file: 'test_calibrated_60_percent.txt', expected: 60, description: 'نص مع 60% عبارات مرجعية' },
        { file: 'test_calibrated_90_percent.txt', expected: 90, description: 'نص مع 90% عبارات مرجعية' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 85; // معيار عالي
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments ? result.suspiciousSegments.length : 0}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            // عرض تفاصيل التحليل
            if (result.analysis) {
                console.log(`   🔬 تفاصيل التحليل:`);
                console.log(`      أقصى تشابه: ${(result.analysis.maxSimilarity * 100).toFixed(1)}%`);
                console.log(`      متوسط التشابه: ${(result.analysis.avgSimilarity * 100).toFixed(1)}%`);
                console.log(`      العتبة التكيفية: ${(result.analysis.adaptiveThreshold * 100).toFixed(1)}%`);
                console.log(`      المراجع المفحوصة: ${result.analysis.referencesChecked || 'غير محدد'}`);
            }
            
            // عرض أمثلة على الأجزاء المشبوهة
            if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
                console.log(`   📝 أمثلة على الأجزاء المشبوهة:`);
                result.suspiciousSegments.slice(0, 2).forEach((segment, index) => {
                    console.log(`      ${index + 1}. "${segment.text.substring(0, 50)}..." (${(segment.similarity * 100).toFixed(1)}%)`);
                });
            }
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                maxSimilarity: result.analysis ? result.analysis.maxSimilarity : 0,
                avgSimilarity: result.analysis ? result.analysis.avgSimilarity : 0,
                referencesChecked: result.analysis ? result.analysis.referencesChecked : 0,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج النهائية
    console.log('\n' + '=' .repeat(70));
    console.log('📊 تحليل النتائج مع النظام المحسن');
    console.log('=' .repeat(70));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgMaxSimilarity = validResults.reduce((sum, r) => sum + r.maxSimilarity, 0) / validResults.length;
        const avgReferencesChecked = validResults.reduce((sum, r) => sum + r.referencesChecked, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`📈 النتائج مع النظام المحسن:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (كان 51.7%)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (كان 33.3%)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
        console.log(`   متوسط أقصى تشابه: ${(avgMaxSimilarity * 100).toFixed(1)}%`);
        console.log(`   متوسط المراجع المفحوصة: ${avgReferencesChecked.toFixed(0)}`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 51.7;
        const successImprovement = successRate - 33.3;
        
        console.log(`\n📊 التحسن المحقق:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تقييم التحسن
        if (avgAccuracy >= 95 && successRate >= 90) {
            console.log(`\n🎉 هدف محقق! تم الوصول للدقة المطلوبة 95%+!`);
        } else if (avgAccuracy >= 85 && successRate >= 70) {
            console.log(`\n🎯 تحسن ممتاز! قريب جداً من الهدف المطلوب`);
        } else if (avgAccuracy >= 70 && successRate >= 50) {
            console.log(`\n✅ تحسن كبير! النظام يعمل بكفاءة جيدة`);
        } else if (avgAccuracy > 51.7) {
            console.log(`\n📈 تحسن ملحوظ! في الاتجاه الصحيح`);
        } else {
            console.log(`\n⚠️ تحسن محدود! نحتاج مزيد من التطوير`);
        }
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج:`);
        validResults.forEach(result => {
            const grade = result.accuracy >= 95 ? '🎯 ممتاز' : 
                         result.accuracy >= 85 ? '✅ جيد جداً' : 
                         result.accuracy >= 70 ? '📈 جيد' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${grade}`);
        });
        
        // تقييم تحقيق الأهداف النهائية
        console.log(`\n🎯 تقييم تحقيق الأهداف النهائية:`);
        console.log(`   دقة 95%+: ${avgAccuracy >= 95 ? '✅' : '❌'} (${avgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 90%+: ${successRate >= 90 ? '✅' : '❌'} (${successRate.toFixed(1)}%)`);
        console.log(`   أداء < 1000ms: ${avgTime < 1000 ? '✅' : '❌'} (${avgTime.toFixed(0)}ms)`);
        
        const targetsAchieved = [
            avgAccuracy >= 95,
            successRate >= 90,
            avgTime < 1000
        ].filter(Boolean).length;
        
        console.log(`   الأهداف المحققة: ${targetsAchieved}/3`);
        
        // حفظ النتائج النهائية
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'improved_system_test',
            improvements: {
                database_size: 198,
                configurable_analyzer: true,
                optimized_weights: bestConfig.weights,
                adaptive_thresholds: bestConfig.thresholds
            },
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgMaxSimilarity: avgMaxSimilarity * 100,
                avgReferencesChecked: avgReferencesChecked,
                targetsAchieved: targetsAchieved,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results
        };
        
        fs.writeFileSync('improved_system_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير النهائي: improved_system_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            targetsAchieved,
            improvement: accuracyImprovement
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testImprovedSystem();
        
        if (results) {
            console.log('\n🎯 الخلاصة النهائية:');
            console.log(`   الدقة النهائية: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن الإجمالي: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(0)}ms`);
            console.log(`   الأهداف المحققة: ${results.targetsAchieved}/3`);
            
            if (results.targetsAchieved === 3) {
                console.log('🎉 تم تحقيق جميع الأهداف! النظام جاهز للإنتاج');
            } else if (results.avgAccuracy >= 90) {
                console.log('🎯 دقة ممتازة! قريب جداً من الهدف النهائي');
            } else if (results.improvement > 20) {
                console.log('📈 تحسن كبير! النظام في الاتجاه الصحيح');
            } else {
                console.log('🔧 تحسن ملحوظ لكن يحتاج مزيد من العمل');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testImprovedSystem };
