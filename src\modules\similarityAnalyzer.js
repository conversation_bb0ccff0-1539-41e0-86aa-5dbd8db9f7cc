const natural = require('natural');
const levenshtein = require('fast-levenshtein');
const fs = require('fs');
const path = require('path');

/**
 * وحدة تحليل التشابه والكشف عن الاستلال
 */
class SimilarityAnalyzer {
    constructor() {
        this.threshold = {
            high: 0.60,      // تشابه عالي جداً - استلال مؤكد (رفع إلى 0.60 للدقة)
            medium: 0.45,    // تشابه متوسط - مشكوك به (رفع إلى 0.45)
            low: 0.30        // تشابه منخفض - قد يكون طبيعي (رفع إلى 0.30)
        };

        // نظام caching ذكي للأداء
        this.cache = new Map();
        this.maxCacheSize = 1000;
        this.cacheHits = 0;
        this.cacheMisses = 0;

        // فهرسة للبحث السريع
        this.referenceIndex = new Map();
        this.preprocessedReferences = new Map();

        // كلمات الإيقاف العربية والإنجليزية
        this.stopWords = new Set([
            // عربي
            'في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'ذلك', 'تلك',
            'التي', 'الذي', 'التي', 'اللذان', 'اللتان', 'الذين', 'اللواتي',
            'كان', 'كانت', 'يكون', 'تكون', 'أن', 'إن', 'لكن', 'لكن', 'غير',
            'بعد', 'قبل', 'أثناء', 'خلال', 'عند', 'لدى', 'حول', 'نحو',
            // إنجليزي
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being',
            'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
            'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        ]);

        // تحميل قاعدة البيانات المرجعية
        this.loadReferenceDatabase();

        // إنشاء الفهرسة للبحث السريع بعد تحميل البيانات
        this.createSearchIndex();
    }

    /**
     * تحميل قاعدة البيانات المرجعية الموسعة
     */
    loadReferenceDatabase() {
        try {
            const dbPath = path.join(__dirname, '..', 'data', 'reference_phrases.json');
            const data = fs.readFileSync(dbPath, 'utf8');
            const referenceData = JSON.parse(data);

            // دمج جميع أنواع العبارات
            this.referenceTexts = [
                ...referenceData.academic_phrases,
                ...referenceData.common_transitions,
                ...referenceData.research_verbs
            ];

            // سيتم طباعة الرسالة في constructor
        } catch (error) {
            console.warn('⚠️ تعذر تحميل قاعدة البيانات المرجعية، استخدام القاعدة الافتراضية:', error.message);
            // قاعدة بيانات احتياطية
            this.referenceTexts = [
                "في الختام، يمكن القول أن هذا البحث قد توصل إلى نتائج مهمة",
                "تهدف هذه الدراسة إلى تحليل وفهم الظاهرة المدروسة",
                "من خلال هذا البحث، تم التوصل إلى عدة استنتاجات مهمة",
                "تشير النتائج إلى وجود علاقة قوية بين المتغيرات المدروسة",
                "يوصي الباحث بإجراء المزيد من الدراسات في هذا المجال",
                "بناءً على ما تقدم، يمكن استنتاج أن",
                "تبين من خلال التحليل الإحصائي أن",
                "أظهرت النتائج وجود فروق ذات دلالة إحصائية",
                "يهدف هذا البحث إلى دراسة وتحليل",
                "من أهم التوصيات التي خلص إليها البحث"
            ];
        }
    }

    /**
     * تحليل النص للكشف عن الاستلال
     * @param {string} text - النص المراد تحليله
     * @returns {Promise<Object>} نتائج التحليل
     */
    async analyzeText(text) {
        try {
            const sentences = this.splitIntoSentences(text);
            const suspiciousSegments = [];
            let totalSimilarityScore = 0;
            let checkedSegments = 0;

            // تحديد خصائص النص للنظام التكيفي
            const textLength = text.split(/\s+/).length;
            const contentType = this.detectContentType(text);

            console.log(`📊 تحليل نص: ${textLength} كلمة، نوع: ${contentType}`);

            // تحليل كل جملة مع النظام التكيفي
            for (let i = 0; i < sentences.length; i++) {
                const sentence = sentences[i];

                if (sentence.length < 30) continue; // تجاهل الجمل القصيرة

                // استخدام التحليل التكيفي للجملة
                let maxSimilarity = 0;
                let bestMatch = '';
                let sentenceAnalysis = null;

                // استخدام البحث السريع للجمل أيضاً
                const sentenceRelevantIndices = this.findRelevantReferences(sentence);
                const sentenceRelevantTexts = sentenceRelevantIndices.length > 0 ?
                    sentenceRelevantIndices.slice(0, 10).map(index => this.referenceTexts[index]) :
                    this.referenceTexts.slice(0, 20);

                for (const referenceText of sentenceRelevantTexts) {
                    const analysis = this.analyzeTextComprehensively(sentence, referenceText);

                    if (analysis.similarity > maxSimilarity) {
                        maxSimilarity = analysis.similarity;
                        bestMatch = referenceText;
                        sentenceAnalysis = analysis;
                    }
                }

                if (sentenceAnalysis && maxSimilarity > sentenceAnalysis.threshold * 0.8) {
                    suspiciousSegments.push({
                        text: sentence,
                        similarity: maxSimilarity,
                        threshold: sentenceAnalysis.threshold,
                        matchedText: bestMatch.substring(0, 100) + '...',
                        position: i,
                        type: sentenceAnalysis.riskLevel.label,
                        color: sentenceAnalysis.riskLevel.color,
                        contentType: sentenceAnalysis.contentType
                    });
                }

                totalSimilarityScore += maxSimilarity;
                checkedSegments++;
            }

            // تحليل الفقرات للكشف عن التشابه الهيكلي
            const paragraphs = this.splitIntoParagraphs(text);
            const structuralAnalysis = await this.analyzeStructuralSimilarity(paragraphs);

            // تحليل شامل للنص كاملاً مع تحسين الأداء
            let maxOverallSimilarity = 0;
            let bestOverallMatch = null;

            // استخدام البحث السريع للحصول على النصوص ذات الصلة فقط
            const relevantIndices = this.findRelevantReferences(text);
            const relevantTexts = relevantIndices.length > 0 ?
                relevantIndices.map(index => this.referenceTexts[index]) :
                this.referenceTexts.slice(0, 50); // أخذ أول 50 نص إذا لم توجد نتائج

            console.log(`🔍 فحص ${relevantTexts.length} نص مرجعي من أصل ${this.referenceTexts.length}`);

            for (const referenceText of relevantTexts) {
                const analysis = this.analyzeTextComprehensively(text, referenceText);

                if (analysis.similarity > maxOverallSimilarity) {
                    maxOverallSimilarity = analysis.similarity;
                    bestOverallMatch = analysis;
                }
            }

            // حساب النسبة الإجمالية للاستلال بطريقة محسنة
            const averageSimilarity = checkedSegments > 0 ? totalSimilarityScore / checkedSegments : 0;
            const finalSimilarity = Math.max(averageSimilarity, maxOverallSimilarity);
            const plagiarismPercentage = Math.round(finalSimilarity * 100);

            // تحديد مستوى الخطر باستخدام العتبة التكيفية
            const adaptiveThreshold = bestOverallMatch ?
                bestOverallMatch.threshold :
                this.calculateDynamicThreshold(textLength, contentType);

            let riskLevel;
            if (finalSimilarity >= adaptiveThreshold + 0.15) {
                riskLevel = { level: 'very_high', label: 'عالي جداً', color: 'darkred' };
            } else if (finalSimilarity >= adaptiveThreshold + 0.05) {
                riskLevel = { level: 'high', label: 'عالي', color: 'red' };
            } else if (finalSimilarity >= adaptiveThreshold) {
                riskLevel = { level: 'medium', label: 'متوسط', color: 'orange' };
            } else if (finalSimilarity >= adaptiveThreshold - 0.1) {
                riskLevel = { level: 'low', label: 'منخفض', color: 'yellow' };
            } else {
                riskLevel = { level: 'minimal', label: 'ضئيل', color: 'green' };
            }

            // إزالة التكرارات وترتيب النتائج
            const uniqueSuspiciousSegments = this.removeDuplicateSegments(suspiciousSegments)
                .sort((a, b) => b.similarity - a.similarity)
                .slice(0, 15); // أخذ أعلى 15 نتيجة

            console.log(`📈 نتائج التحليل: ${plagiarismPercentage}% استلال، ${uniqueSuspiciousSegments.length} جزء مشبوه`);

            return {
                plagiarismPercentage,
                averageSimilarity,
                maxSimilarity: maxOverallSimilarity,
                overallScore: finalSimilarity,
                suspiciousSegments: uniqueSuspiciousSegments,
                riskLevel,
                contentType,
                textLength,
                adaptiveThreshold,
                structuralAnalysis: { patterns: [], score: 0 }, // مبسط للآن
                statistics: {
                    totalSentences: sentences.length,
                    checkedSentences: checkedSegments,
                    suspiciousCount: uniqueSuspiciousSegments.length,
                    highSimilarityCount: uniqueSuspiciousSegments.filter(s => s.similarity > adaptiveThreshold + 0.1).length,
                    mediumSimilarityCount: uniqueSuspiciousSegments.filter(s => s.similarity > adaptiveThreshold && s.similarity <= adaptiveThreshold + 0.1).length,
                    totalReferences: this.referenceTexts.length,
                    detectionMethod: 'تكيفي متقدم'
                },
                recommendations: this.generateEnhancedRecommendations(plagiarismPercentage, uniqueSuspiciousSegments.length, contentType)
            };

        } catch (error) {
            console.error('خطأ في تحليل النص:', error);
            throw new Error(`فشل في تحليل النص: ${error.message}`);
        }
    }

    /**
     * فحص تشابه جملة واحدة
     * @param {string} sentence - الجملة المراد فحصها
     * @returns {Promise<Object>} نتائج التشابه
     */
    async checkSentenceSimilarity(sentence) {
        let maxSimilarity = 0;
        let matchedText = '';

        // مقارنة مع النصوص المرجعية
        for (const refText of this.referenceTexts) {
            const similarity = this.calculateSimilarity(sentence, refText);
            
            if (similarity > maxSimilarity) {
                maxSimilarity = similarity;
                matchedText = refText;
            }
        }

        // فحص التشابه مع أجزاء أخرى من نفس النص
        const internalSimilarity = await this.checkInternalSimilarity(sentence);
        
        if (internalSimilarity.similarity > maxSimilarity) {
            maxSimilarity = internalSimilarity.similarity;
            matchedText = internalSimilarity.matchedText;
        }

        return {
            maxSimilarity,
            matchedText,
            internalMatch: internalSimilarity.similarity > 0.7
        };
    }

    /**
     * حساب التشابه بين نصين
     * @param {string} text1 - النص الأول
     * @param {string} text2 - النص الثاني
     * @returns {number} درجة التشابه (0-1)
     */
    calculateSimilarity(text1, text2, textLength = null, contentType = 'academic') {
        // تنظيف النصوص
        const cleanText1 = this.preprocessText(text1);
        const cleanText2 = this.preprocessText(text2);

        // حساب التشابه باستخدام عدة طرق
        const jaccardSimilarity = this.calculateJaccardSimilarity(cleanText1, cleanText2);
        const cosineSimilarity = this.calculateCosineSimilarity(cleanText1, cleanText2);
        const levenshteinSimilarity = this.calculateLevenshteinSimilarity(cleanText1, cleanText2);
        const semanticSimilarity = this.calculateSemanticSimilarity(cleanText1, cleanText2);
        const exactMatchBonus = this.calculateExactMatchBonus(cleanText1, cleanText2);

        // الحصول على الأوزان التكيفية
        const weights = this.getAdaptiveWeights(textLength, contentType, {
            jaccard: jaccardSimilarity,
            cosine: cosineSimilarity,
            levenshtein: levenshteinSimilarity,
            semantic: semanticSimilarity
        });

        // حساب المتوسط المرجح التكيفي
        const baseSimilarity = (
            cosineSimilarity * weights.cosine +
            jaccardSimilarity * weights.jaccard +
            levenshteinSimilarity * weights.levenshtein +
            semanticSimilarity * weights.semantic
        );

        // إضافة مكافأة للتطابق الحرفي
        return Math.min(1.0, baseSimilarity + exactMatchBonus);
    }

    /**
     * معالجة النص قبل المقارنة
     * @param {string} text - النص
     * @returns {string} النص المعالج
     */
    preprocessText(text) {
        return text
            .toLowerCase()
            // إزالة التشكيل العربي
            .replace(/[\u064B-\u0652]/g, '')
            // توحيد الألف
            .replace(/[آأإ]/g, 'ا')
            // توحيد التاء المربوطة والهاء
            .replace(/ة/g, 'ه')
            // إزالة علامات الترقيم
            .replace(/[^\w\s\u0600-\u06FF]/g, '')
            .split(/\s+/)
            .filter(word => !this.stopWords.has(word) && word.length > 2)
            .join(' ');
    }

    /**
     * حساب تشابه Jaccard
     * @param {string} text1 - النص الأول
     * @param {string} text2 - النص الثاني
     * @returns {number} درجة التشابه
     */
    calculateJaccardSimilarity(text1, text2) {
        // تحسين Jaccard للعمل على مستويات متعددة

        // 1. تشابه الكلمات (الوزن الأساسي)
        const words1 = new Set(text1.split(/\s+/).filter(w => w.length > 2));
        const words2 = new Set(text2.split(/\s+/).filter(w => w.length > 2));

        const wordIntersection = new Set([...words1].filter(x => words2.has(x)));
        const wordUnion = new Set([...words1, ...words2]);
        const wordSimilarity = wordUnion.size > 0 ? wordIntersection.size / wordUnion.size : 0;

        // 2. تشابه العبارات (2-3 كلمات)
        const phrases1 = this.extractPhrases(text1, 2, 3);
        const phrases2 = this.extractPhrases(text2, 2, 3);

        const phraseIntersection = new Set([...phrases1].filter(x => phrases2.has(x)));
        const phraseUnion = new Set([...phrases1, ...phrases2]);
        const phraseSimilarity = phraseUnion.size > 0 ? phraseIntersection.size / phraseUnion.size : 0;

        // 3. تشابه العبارات الطويلة (4-6 كلمات) - أهم للكشف عن الاستلال
        const longPhrases1 = this.extractPhrases(text1, 4, 6);
        const longPhrases2 = this.extractPhrases(text2, 4, 6);

        const longPhraseIntersection = new Set([...longPhrases1].filter(x => longPhrases2.has(x)));
        const longPhraseUnion = new Set([...longPhrases1, ...longPhrases2]);
        const longPhraseSimilarity = longPhraseUnion.size > 0 ? longPhraseIntersection.size / longPhraseUnion.size : 0;

        // دمج النتائج مع أوزان مختلفة - التركيز على العبارات الطويلة
        return (
            wordSimilarity * 0.30 +           // الكلمات المفردة
            phraseSimilarity * 0.35 +         // العبارات القصيرة
            longPhraseSimilarity * 0.35       // العبارات الطويلة - الأهم للاستلال
        );
    }

    /**
     * استخراج العبارات من النص
     * @param {string} text - النص
     * @param {number} minLength - الحد الأدنى لطول العبارة
     * @param {number} maxLength - الحد الأقصى لطول العبارة
     * @returns {Set} مجموعة العبارات
     */
    extractPhrases(text, minLength, maxLength) {
        const words = text.split(/\s+/).filter(w => w.length > 1);
        const phrases = new Set();

        for (let i = 0; i <= words.length - minLength; i++) {
            for (let len = minLength; len <= Math.min(maxLength, words.length - i); len++) {
                const phrase = words.slice(i, i + len).join(' ');
                if (phrase.length > 10) { // تجاهل العبارات القصيرة جداً
                    phrases.add(phrase);
                }
            }
        }

        return phrases;
    }

    /**
     * حساب تشابه Cosine
     * @param {string} text1 - النص الأول
     * @param {string} text2 - النص الثاني
     * @returns {number} درجة التشابه
     */
    calculateCosineSimilarity(text1, text2) {
        // تحسين Cosine للعمل على مستويات متعددة

        // 1. تشابه الكلمات
        const words1 = text1.split(/\s+/).filter(w => w.length > 2);
        const words2 = text2.split(/\s+/).filter(w => w.length > 2);

        const allWords = [...new Set([...words1, ...words2])];

        const vector1 = allWords.map(word => words1.filter(w => w === word).length);
        const vector2 = allWords.map(word => words2.filter(w => w === word).length);

        const dotProduct = vector1.reduce((sum, val, i) => sum + val * vector2[i], 0);
        const magnitude1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
        const magnitude2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));

        const wordSimilarity = magnitude1 && magnitude2 ? dotProduct / (magnitude1 * magnitude2) : 0;

        // 2. تشابه العبارات الثنائية (bigrams)
        const bigrams1 = this.extractNGrams(text1, 2);
        const bigrams2 = this.extractNGrams(text2, 2);
        const bigramSimilarity = this.calculateVectorSimilarity(bigrams1, bigrams2);

        // 3. تشابه العبارات الثلاثية (trigrams) - مهم للكشف عن الاستلال
        const trigrams1 = this.extractNGrams(text1, 3);
        const trigrams2 = this.extractNGrams(text2, 3);
        const trigramSimilarity = this.calculateVectorSimilarity(trigrams1, trigrams2);

        // دمج النتائج مع التركيز على العبارات الأطول
        return (
            wordSimilarity * 0.30 +      // الكلمات المفردة
            bigramSimilarity * 0.35 +    // العبارات الثنائية
            trigramSimilarity * 0.35     // العبارات الثلاثية - الأهم
        );
    }

    /**
     * استخراج N-grams من النص
     * @param {string} text - النص
     * @param {number} n - حجم الـ gram
     * @returns {Array} مصفوفة الـ N-grams
     */
    extractNGrams(text, n) {
        const words = text.split(/\s+/).filter(w => w.length > 1);
        const ngrams = [];

        for (let i = 0; i <= words.length - n; i++) {
            const ngram = words.slice(i, i + n).join(' ');
            if (ngram.length > 5) { // تجاهل العبارات القصيرة جداً
                ngrams.push(ngram);
            }
        }

        return ngrams;
    }

    /**
     * حساب التشابه بين مصفوفتين باستخدام Cosine
     * @param {Array} array1 - المصفوفة الأولى
     * @param {Array} array2 - المصفوفة الثانية
     * @returns {number} درجة التشابه
     */
    calculateVectorSimilarity(array1, array2) {
        if (array1.length === 0 || array2.length === 0) return 0;

        const allItems = [...new Set([...array1, ...array2])];

        const vector1 = allItems.map(item => array1.filter(x => x === item).length);
        const vector2 = allItems.map(item => array2.filter(x => x === item).length);

        const dotProduct = vector1.reduce((sum, val, i) => sum + val * vector2[i], 0);
        const magnitude1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
        const magnitude2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));

        return magnitude1 && magnitude2 ? dotProduct / (magnitude1 * magnitude2) : 0;
    }

    /**
     * حساب تشابه Levenshtein
     * @param {string} text1 - النص الأول
     * @param {string} text2 - النص الثاني
     * @returns {number} درجة التشابه
     */
    calculateLevenshteinSimilarity(text1, text2) {
        const maxLength = Math.max(text1.length, text2.length);
        if (maxLength === 0) return 1;

        const distance = levenshtein.get(text1, text2);
        return 1 - (distance / maxLength);
    }

    /**
     * فحص التشابه الداخلي في النص
     * @param {string} sentence - الجملة المراد فحصها
     * @returns {Promise<Object>} نتائج التشابه الداخلي
     */
    async checkInternalSimilarity(sentence) {
        // هذه الوظيفة يمكن توسيعها لفحص التشابه مع قاعدة بيانات محلية
        // أو مع أجزاء أخرى من نفس المستند
        
        return {
            similarity: 0,
            matchedText: ''
        };
    }

    /**
     * تحليل التشابه الهيكلي
     * @param {Array<string>} paragraphs - الفقرات
     * @returns {Promise<Object>} نتائج التحليل الهيكلي
     */
    async analyzeStructuralSimilarity(paragraphs) {
        const structuralPatterns = [];
        
        for (let i = 0; i < paragraphs.length; i++) {
            const paragraph = paragraphs[i];
            const sentences = this.splitIntoSentences(paragraph);
            
            // تحليل بنية الفقرة
            const structure = {
                sentenceCount: sentences.length,
                averageLength: sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length,
                startsWithCommon: this.startsWithCommonPhrase(paragraph),
                endsWithCommon: this.endsWithCommonPhrase(paragraph)
            };
            
            structuralPatterns.push(structure);
        }
        
        return {
            patterns: structuralPatterns,
            suspiciousStructures: structuralPatterns.filter(p => p.startsWithCommon || p.endsWithCommon)
        };
    }

    /**
     * تحديد نوع التشابه
     * @param {number} similarity - درجة التشابه
     * @returns {string} نوع التشابه
     */
    getSimilarityType(similarity) {
        if (similarity > this.threshold.high) {
            return 'high'; // استلال مؤكد
        } else if (similarity > this.threshold.medium) {
            return 'medium'; // مشكوك به
        } else {
            return 'low'; // تشابه طبيعي
        }
    }

    /**
     * تقسيم النص إلى جمل
     * @param {string} text - النص
     * @returns {Array<string>} مصفوفة الجمل
     */
    splitIntoSentences(text) {
        return text
            .split(/[.!?]+/)
            .map(s => s.trim())
            .filter(s => s.length > 10);
    }

    /**
     * تقسيم النص إلى فقرات
     * @param {string} text - النص
     * @returns {Array<string>} مصفوفة الفقرات
     */
    splitIntoParagraphs(text) {
        return text
            .split(/\n\s*\n/)
            .map(p => p.trim())
            .filter(p => p.length > 50);
    }

    /**
     * فحص بداية الفقرة بعبارة شائعة
     * @param {string} paragraph - الفقرة
     * @returns {boolean} true إذا بدأت بعبارة شائعة
     */
    startsWithCommonPhrase(paragraph) {
        const commonStarts = [
            'في الختام',
            'بناءً على ما تقدم',
            'من خلال هذا البحث',
            'تهدف هذه الدراسة',
            'يمكن القول أن'
        ];
        
        return commonStarts.some(phrase => paragraph.startsWith(phrase));
    }

    /**
     * فحص نهاية الفقرة بعبارة شائعة
     * @param {string} paragraph - الفقرة
     * @returns {boolean} true إذا انتهت بعبارة شائعة
     */
    endsWithCommonPhrase(paragraph) {
        const commonEnds = [
            'في هذا المجال',
            'للدراسات المستقبلية',
            'نتائج مهمة',
            'استنتاجات مهمة'
        ];
        
        return commonEnds.some(phrase => paragraph.endsWith(phrase));
    }

    /**
     * إنتاج التوصيات بناءً على نتائج التحليل
     * @param {number} plagiarismPercentage - نسبة الاستلال
     * @param {Array} suspiciousSegments - الأجزاء المشكوك بها
     * @returns {Array<string>} التوصيات
     */
    generateRecommendations(plagiarismPercentage, suspiciousSegments) {
        const recommendations = [];
        
        if (plagiarismPercentage > 70) {
            recommendations.push('نسبة الاستلال عالية جداً - يجب إعادة كتابة معظم المحتوى');
            recommendations.push('استخدم الاقتباس المناسب مع ذكر المصادر');
        } else if (plagiarismPercentage > 40) {
            recommendations.push('نسبة الاستلال متوسطة - راجع الأجزاء المشكوك بها');
            recommendations.push('أضف المزيد من التحليل الشخصي والأفكار الأصلية');
        } else if (plagiarismPercentage > 20) {
            recommendations.push('نسبة الاستلال منخفضة - راجع بعض الأجزاء');
            recommendations.push('تأكد من ذكر المصادر للاقتباسات');
        } else {
            recommendations.push('نسبة الاستلال منخفضة جداً - المحتوى أصلي إلى حد كبير');
        }
        
        if (suspiciousSegments.length > 0) {
            recommendations.push(`تم العثور على ${suspiciousSegments.length} جزء مشكوك به`);
        }
        
        return recommendations;
    }

    /**
     * حساب التشابه الدلالي المحسن (Semantic Similarity)
     * @param {string} text1 - النص الأول
     * @param {string} text2 - النص الثاني
     * @returns {number} درجة التشابه الدلالي (0-1)
     */
    calculateSemanticSimilarity(text1, text2) {
        // استخدام Map للبحث السريع
        const synonymsMap = new Map([
            ['دراسة', new Set(['بحث', 'تحليل', 'فحص', 'استقصاء', 'تقصي'])],
            ['نتائج', new Set(['استنتاجات', 'خلاصة', 'مخرجات', 'حصائل', 'ثمار'])],
            ['تحليل', new Set(['دراسة', 'فحص', 'تقييم', 'تمحيص', 'بحث'])],
            ['يهدف', new Set(['يسعى', 'يرمي', 'يقصد', 'يستهدف', 'يتوخى'])],
            ['توصل', new Set(['خلص', 'انتهى', 'وصل', 'استنتج', 'أفضى'])],
            ['أظهرت', new Set(['بينت', 'كشفت', 'أوضحت', 'دلت', 'أبرزت'])],
            ['يوصي', new Set(['ينصح', 'يقترح', 'يدعو', 'يحث', 'يشير'])],
            ['مهمة', new Set(['هامة', 'بالغة', 'جوهرية', 'أساسية', 'حيوية'])],
            ['قوية', new Set(['شديدة', 'واضحة', 'بارزة', 'ملحوظة', 'كبيرة'])],
            ['علاقة', new Set(['ارتباط', 'صلة', 'رابطة', 'تناسب', 'تلازم'])]
        ]);

        const words1 = new Set(text1.split(/\s+/));
        const words2 = new Set(text2.split(/\s+/));

        // حساب التقاطع المباشر
        const directIntersection = new Set([...words1].filter(x => words2.has(x)));
        let semanticScore = directIntersection.size;

        // حساب التشابه الدلالي
        words1.forEach(word1 => {
            if (!directIntersection.has(word1)) {
                words2.forEach(word2 => {
                    if (!directIntersection.has(word2)) {
                        if (synonymsMap.has(word1) && synonymsMap.get(word1).has(word2)) {
                            semanticScore += 0.8; // مرادف
                        } else if (synonymsMap.has(word2) && synonymsMap.get(word2).has(word1)) {
                            semanticScore += 0.8; // مرادف عكسي
                        } else if (this.areRelatedWords(word1, word2)) {
                            semanticScore += 0.6; // كلمات مترابطة
                        } else if (word1.length > 3 && word2.length > 3 &&
                                 (word1.includes(word2) || word2.includes(word1))) {
                            semanticScore += 0.4; // تشابه جزئي
                        }
                    }
                });
            }
        });

        const totalWords = Math.max(words1.size, words2.size);
        return totalWords > 0 ? Math.min(1.0, semanticScore / totalWords) : 0;
    }

    /**
     * فحص ترابط الكلمات
     * @param {string} word1 - الكلمة الأولى
     * @param {string} word2 - الكلمة الثانية
     * @returns {boolean} هل الكلمتان مترابطتان
     */
    areRelatedWords(word1, word2) {
        // مجموعات كلمات مترابطة
        const relatedGroups = [
            ['بحث', 'دراسة', 'تحليل', 'فحص', 'تقييم'],
            ['نتيجة', 'استنتاج', 'خلاصة', 'حصيلة'],
            ['طالب', 'باحث', 'دارس', 'أكاديمي'],
            ['جامعة', 'كلية', 'معهد', 'مؤسسة'],
            ['نظرية', 'مفهوم', 'فكرة', 'مبدأ'],
            ['طريقة', 'منهج', 'أسلوب', 'وسيلة'],
            ['مجتمع', 'عينة', 'مجموعة', 'فئة'],
            ['إحصائي', 'رقمي', 'كمي', 'عددي']
        ];

        return relatedGroups.some(group =>
            group.includes(word1) && group.includes(word2)
        );
    }

    /**
     * حساب العتبة التدريجية حسب طول النص ونوعه
     * @param {number} textLength - طول النص
     * @param {string} contentType - نوع المحتوى
     * @returns {number} العتبة المحسوبة
     */
    calculateDynamicThreshold(textLength, contentType = 'academic') {
        // عتبة أساسية محسنة للدقة - رفع من 0.30 إلى 0.50
        let baseThreshold = 0.50;

        // تعديل حسب طول النص - عتبات متوازنة
        if (textLength < 200) {
            baseThreshold = 0.55; // نصوص قصيرة - عتبة أعلى
        } else if (textLength > 1500) {
            baseThreshold = 0.45; // نصوص طويلة جداً - عتبة أقل
        } else if (textLength > 800) {
            baseThreshold = 0.48; // نصوص طويلة - عتبة متوسطة
        }

        // تعديل حسب نوع المحتوى - توازن بين الدقة والكشف
        switch (contentType) {
            case 'academic':
                baseThreshold -= 0.05; // النصوص الأكاديمية - عتبة أقل قليلاً
                break;
            case 'creative':
                baseThreshold += 0.05; // النصوص الإبداعية - عتبة أعلى
                break;
            case 'technical':
                baseThreshold -= 0.03; // النصوص التقنية - عتبة أقل قليلاً
                break;
        }

        // التأكد من أن العتبة في النطاق المحسن (35-65%)
        return Math.max(0.35, Math.min(0.65, baseThreshold));
    }

    /**
     * الحصول على الأوزان التكيفية للخوارزميات
     * @param {number} textLength - طول النص
     * @param {string} contentType - نوع المحتوى
     * @param {Object} similarities - قيم التشابه المحسوبة
     * @returns {Object} الأوزان المحسوبة
     */
    getAdaptiveWeights(textLength, contentType, similarities) {
        // الأوزان الأساسية المتوازنة للدقة العالية
        let weights = {
            jaccard: 0.40,     // توازن جيد - مهم للنصوص العربية
            cosine: 0.40,      // توازن جيد - مهم للتشابه الدلالي
            levenshtein: 0.15, // وزن معقول للتشابه النصي
            semantic: 0.05     // وزن قليل للتشابه الدلالي
        };

        // تعديل حسب طول النص - تركيز على Jaccard للنصوص العربية
        if (textLength) {
            if (textLength > 1000) {
                // النصوص الطويلة - زيادة وزن Jaccard أكثر
                weights.jaccard += 0.08;
                weights.cosine -= 0.05;
                weights.levenshtein -= 0.03;
            } else if (textLength < 200) {
                // النصوص القصيرة - زيادة وزن Jaccard بقوة
                weights.jaccard += 0.10;
                weights.cosine -= 0.06;
                weights.levenshtein -= 0.04;
            }
        }

        // تعديل حسب نوع المحتوى - تركيز على Jaccard للنصوص العربية
        switch (contentType) {
            case 'academic':
                // النصوص الأكاديمية - زيادة وزن Jaccard للكشف الأفضل
                weights.jaccard += 0.08;
                weights.cosine -= 0.04;
                weights.levenshtein -= 0.04;
                break;
            case 'creative':
                // النصوص الإبداعية - توازن بين Jaccard و Cosine
                weights.jaccard += 0.05;
                weights.cosine += 0.03;
                weights.levenshtein -= 0.08;
                break;
            case 'technical':
                // النصوص التقنية - زيادة وزن Jaccard و Cosine
                weights.jaccard += 0.06;
                weights.cosine += 0.04;
                weights.levenshtein -= 0.10;
                break;
        }

        // تعديل ديناميكي حسب قيم التشابه - تركيز على Jaccard
        if (similarities) {
            // إذا كان هناك تطابق عالي في Jaccard، زيادة وزنه بقوة
            if (similarities.jaccard > 0.6) { // خفض العتبة من 0.8 إلى 0.6
                weights.jaccard += 0.10; // زيادة من 0.05 إلى 0.10
                weights.cosine -= 0.05;
                weights.levenshtein -= 0.05;
            }

            // إذا كان هناك تطابق عالي في Cosine، زيادة وزنه
            if (similarities.cosine > 0.7) {
                weights.cosine += 0.08;
                weights.jaccard -= 0.04;
                weights.levenshtein -= 0.04;
            }

            // تقليل أهمية Semantic إذا كان منخفض
            if (similarities.semantic < 0.3) {
                weights.semantic -= 0.02;
                weights.jaccard += 0.01;
                weights.cosine += 0.01;
            }
        }

        // التأكد من أن مجموع الأوزان = 1
        const totalWeight = Object.values(weights).reduce((sum, w) => sum + w, 0);
        Object.keys(weights).forEach(key => {
            weights[key] = weights[key] / totalWeight;
        });

        return weights;
    }

    /**
     * حساب مكافأة التطابق الحرفي
     * @param {string} text1 - النص الأول
     * @param {string} text2 - النص الثاني
     * @returns {number} مكافأة التطابق (0-0.1)
     */
    calculateExactMatchBonus(text1, text2) {
        const words1 = text1.split(/\s+/).filter(w => w.length > 2);
        const words2 = text2.split(/\s+/).filter(w => w.length > 2);

        let totalBonus = 0;

        // 1. فحص العبارات الطويلة المتطابقة (الأهم للكشف عن الاستلال)
        for (let len = 5; len >= 3; len--) { // البدء بالعبارات الطويلة
            for (let i = 0; i <= words1.length - len; i++) {
                const phrase1 = words1.slice(i, i + len).join(' ');
                if (phrase1.length < 15) continue; // تجاهل العبارات القصيرة

                for (let j = 0; j <= words2.length - len; j++) {
                    const phrase2 = words2.slice(j, j + len).join(' ');
                    if (phrase1 === phrase2) {
                        // مكافأة كبيرة للعبارات الطويلة المتطابقة
                        totalBonus += len * 0.05; // زيادة من 0.02 إلى 0.05
                        break; // تجنب العد المكرر
                    }
                }
            }
        }

        // 2. فحص التطابق المتتالي للكلمات
        let consecutiveMatches = 0;
        let maxConsecutive = 0;
        const minLength = Math.min(words1.length, words2.length);

        for (let i = 0; i < minLength; i++) {
            if (words1[i] === words2[i]) {
                consecutiveMatches++;
                maxConsecutive = Math.max(maxConsecutive, consecutiveMatches);
            } else {
                consecutiveMatches = 0;
            }
        }

        // مكافأة للتطابق المتتالي الطويل
        if (maxConsecutive >= 3) {
            totalBonus += maxConsecutive * 0.02;
        }

        // 3. فحص العبارات المتطابقة في أي موضع (substring matching)
        const text1Lower = text1.toLowerCase();
        const text2Lower = text2.toLowerCase();

        // البحث عن عبارات طويلة مشتركة
        for (let len = 20; len >= 10; len--) { // عبارات من 10-20 حرف
            for (let i = 0; i <= text1Lower.length - len; i++) {
                const substring = text1Lower.substring(i, i + len);
                if (text2Lower.includes(substring)) {
                    totalBonus += len * 0.003; // مكافأة حسب طول العبارة
                }
            }
        }

        // الحد الأقصى للمكافأة 0.25 (زيادة من 0.1)
        return Math.min(0.25, totalBonus);
    }

    /**
     * تحليل النص وتحديد نوع المحتوى تلقائياً
     * @param {string} text - النص المراد تحليله
     * @returns {string} نوع المحتوى المحدد
     */
    detectContentType(text) {
        const academicKeywords = [
            'دراسة', 'بحث', 'تحليل', 'نتائج', 'استنتاج', 'منهجية',
            'عينة', 'إحصائي', 'فرضية', 'متغير', 'توصيات'
        ];

        const creativeKeywords = [
            'قصة', 'رواية', 'شعر', 'أدب', 'فن', 'إبداع',
            'خيال', 'عاطفة', 'جمال', 'تعبير'
        ];

        const technicalKeywords = [
            'نظام', 'تقنية', 'برمجة', 'خوارزمية', 'بيانات',
            'شبكة', 'حاسوب', 'تطبيق', 'منصة', 'تكنولوجيا'
        ];

        const words = text.toLowerCase().split(/\s+/);

        let academicScore = 0;
        let creativeScore = 0;
        let technicalScore = 0;

        words.forEach(word => {
            if (academicKeywords.some(keyword => word.includes(keyword))) {
                academicScore++;
            }
            if (creativeKeywords.some(keyword => word.includes(keyword))) {
                creativeScore++;
            }
            if (technicalKeywords.some(keyword => word.includes(keyword))) {
                technicalScore++;
            }
        });

        const maxScore = Math.max(academicScore, creativeScore, technicalScore);

        if (maxScore === 0) return 'academic'; // افتراضي
        if (maxScore === academicScore) return 'academic';
        if (maxScore === creativeScore) return 'creative';
        return 'technical';
    }

    /**
     * تحليل شامل للنص مع تحديد العتبة المناسبة
     * @param {string} inputText - النص المدخل
     * @param {string} referenceText - النص المرجعي
     * @returns {Object} نتائج التحليل الشامل
     */
    analyzeTextComprehensively(inputText, referenceText) {
        const textLength = inputText.split(/\s+/).length;
        const contentType = this.detectContentType(inputText);

        // حساب التشابه مع المعايير التكيفية
        const similarity = this.calculateSimilarity(inputText, referenceText, textLength, contentType);

        // تحديد العتبة التكيفية
        const adaptiveThreshold = this.calculateDynamicThreshold(textLength, contentType);

        // تحديد مستوى الخطر
        let riskLevel;
        if (similarity >= adaptiveThreshold + 0.1) {
            riskLevel = { level: 'high', label: 'عالي', color: 'red' };
        } else if (similarity >= adaptiveThreshold) {
            riskLevel = { level: 'medium', label: 'متوسط', color: 'orange' };
        } else if (similarity >= adaptiveThreshold - 0.1) {
            riskLevel = { level: 'low', label: 'منخفض', color: 'yellow' };
        } else {
            riskLevel = { level: 'minimal', label: 'ضئيل', color: 'green' };
        }

        return {
            similarity: similarity,
            threshold: adaptiveThreshold,
            riskLevel: riskLevel,
            contentType: contentType,
            textLength: textLength,
            isPlagiarized: similarity >= adaptiveThreshold
        };
    }

    /**
     * إنتاج توصيات محسنة حسب نوع المحتوى
     * @param {number} plagiarismPercentage - نسبة الاستلال
     * @param {number} suspiciousCount - عدد الأجزاء المشبوهة
     * @param {string} contentType - نوع المحتوى
     * @returns {Array<string>} قائمة التوصيات المحسنة
     */
    generateEnhancedRecommendations(plagiarismPercentage, suspiciousCount, contentType) {
        const recommendations = [];

        // توصيات حسب نسبة الاستلال
        if (plagiarismPercentage >= 80) {
            recommendations.push('🚨 نسبة الاستلال عالية جداً - مراجعة شاملة وإعادة كتابة مطلوبة');
            recommendations.push('📚 التأكد من التوثيق الصحيح لجميع المصادر المستخدمة');
            recommendations.push('✍️ إعادة صياغة الأجزاء المستلة بأسلوب شخصي ومتميز');
        } else if (plagiarismPercentage >= 60) {
            recommendations.push('⚠️ نسبة الاستلال مرتفعة - مراجعة وتحسين مطلوب');
            recommendations.push('🔍 فحص الأجزاء المشكوك بها وإعادة صياغتها');
            recommendations.push('📖 إضافة المزيد من التحليل والرأي الشخصي');
        } else if (plagiarismPercentage >= 40) {
            recommendations.push('📝 نسبة الاستلال متوسطة - تحسينات مطلوبة');
            recommendations.push('🎯 تقليل استخدام العبارات الأكاديمية النمطية');
            recommendations.push('💡 تطوير أسلوب كتابة أكثر تميزاً');
        } else if (plagiarismPercentage >= 20) {
            recommendations.push('✅ نسبة الاستلال مقبولة مع بعض التحسينات');
            recommendations.push('🔧 مراجعة الأجزاء المشكوك بها فقط');
        } else {
            recommendations.push('🎉 نسبة الاستلال منخفضة - النص يظهر أصالة جيدة');
            recommendations.push('📈 مواصلة التطوير والحفاظ على الجودة');
        }

        // توصيات حسب عدد الأجزاء المشبوهة
        if (suspiciousCount > 10) {
            recommendations.push(`🔍 تم رصد ${suspiciousCount} جزء مشبوه - مراجعة تفصيلية مطلوبة`);
        } else if (suspiciousCount > 5) {
            recommendations.push(`📋 تم رصد ${suspiciousCount} أجزاء مشبوهة - مراجعة مطلوبة`);
        } else if (suspiciousCount > 0) {
            recommendations.push(`✏️ تم رصد ${suspiciousCount} أجزاء تحتاج مراجعة`);
        }

        // توصيات حسب نوع المحتوى
        switch (contentType) {
            case 'academic':
                recommendations.push('🎓 للنصوص الأكاديمية: تنويع المصطلحات وتجنب العبارات النمطية');
                recommendations.push('📊 إضافة تحليل نقدي وربط النتائج بالسياق الأوسع');
                break;
            case 'creative':
                recommendations.push('🎨 للنصوص الإبداعية: التركيز على الأسلوب الشخصي والتميز');
                recommendations.push('💭 تطوير الأفكار الأصيلة والتعبير المبتكر');
                break;
            case 'technical':
                recommendations.push('⚙️ للنصوص التقنية: التأكد من دقة المصطلحات والمفاهيم');
                recommendations.push('🔬 إضافة أمثلة عملية وتطبيقات محددة');
                break;
        }

        // توصيات عامة للتحسين
        recommendations.push('📚 مراجعة قواعد الاقتباس والتوثيق الأكاديمي');
        recommendations.push('🔄 استخدام أدوات إعادة الصياغة بحذر وذكاء');

        return recommendations;
    }

    /**
     * إزالة التكرارات من الأجزاء المشبوهة
     * @param {Array} segments - قائمة الأجزاء المشبوهة
     * @returns {Array} قائمة منظفة من التكرارات
     */
    removeDuplicateSegments(segments) {
        const seen = new Set();
        return segments.filter(segment => {
            const key = segment.text.substring(0, 50); // استخدام أول 50 حرف كمفتاح
            if (seen.has(key)) {
                return false;
            }
            seen.add(key);
            return true;
        });
    }

    /**
     * إنشاء فهرسة للبحث السريع في النصوص المرجعية
     */
    createSearchIndex() {
        console.log('🔍 إنشاء فهرسة للبحث السريع...');

        this.referenceTexts.forEach((text, index) => {
            // معالجة مسبقة للنص
            const preprocessed = this.preprocessText(text);
            this.preprocessedReferences.set(index, preprocessed);

            // فهرسة الكلمات
            const words = preprocessed.split(/\s+/);
            words.forEach(word => {
                if (word.length > 2 && !this.stopWords.has(word)) {
                    if (!this.referenceIndex.has(word)) {
                        this.referenceIndex.set(word, new Set());
                    }
                    this.referenceIndex.get(word).add(index);
                }
            });
        });

        console.log(`✅ تم فهرسة ${this.referenceIndex.size} كلمة فريدة`);
    }

    /**
     * إنتاج مفتاح cache للنص
     * @param {string} text - النص
     * @returns {string} مفتاح الـ cache
     */
    generateCacheKey(text) {
        // استخدام hash بسيط للنص
        let hash = 0;
        for (let i = 0; i < text.length; i++) {
            const char = text.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return `text_${Math.abs(hash)}_${text.length}`;
    }

    /**
     * الحصول على نتيجة من الـ cache
     * @param {string} cacheKey - مفتاح الـ cache
     * @returns {Object|null} النتيجة المحفوظة أو null
     */
    getCachedResult(cacheKey) {
        if (this.cache.has(cacheKey)) {
            this.cacheHits++;
            const result = this.cache.get(cacheKey);
            // تحديث وقت الوصول
            result.lastAccessed = Date.now();
            return result.data;
        }
        this.cacheMisses++;
        return null;
    }

    /**
     * حفظ نتيجة في الـ cache
     * @param {string} cacheKey - مفتاح الـ cache
     * @param {Object} data - البيانات المراد حفظها
     */
    setCachedResult(cacheKey, data) {
        // تنظيف الـ cache إذا امتلأ
        if (this.cache.size >= this.maxCacheSize) {
            this.cleanupCache();
        }

        this.cache.set(cacheKey, {
            data: data,
            created: Date.now(),
            lastAccessed: Date.now()
        });
    }

    /**
     * تنظيف الـ cache بإزالة العناصر الأقل استخداماً
     */
    cleanupCache() {
        const entries = Array.from(this.cache.entries());

        // ترتيب حسب آخر وصول
        entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);

        // إزالة 25% من العناصر الأقل استخداماً
        const toRemove = Math.floor(entries.length * 0.25);
        for (let i = 0; i < toRemove; i++) {
            this.cache.delete(entries[i][0]);
        }

        console.log(`🧹 تم تنظيف ${toRemove} عنصر من الـ cache`);
    }

    /**
     * البحث السريع في النصوص المرجعية باستخدام الفهرسة
     * @param {string} text - النص المراد البحث عنه
     * @returns {Array} مؤشرات النصوص المرجعية ذات الصلة
     */
    findRelevantReferences(text) {
        const words = this.preprocessText(text).split(/\s+/);
        const relevantIndices = new Map();

        words.forEach(word => {
            if (word.length > 2 && !this.stopWords.has(word) && this.referenceIndex.has(word)) {
                const indices = this.referenceIndex.get(word);
                indices.forEach(index => {
                    relevantIndices.set(index, (relevantIndices.get(index) || 0) + 1);
                });
            }
        });

        // ترتيب حسب عدد الكلمات المشتركة
        return Array.from(relevantIndices.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 20) // أخذ أعلى 20 نتيجة
            .map(entry => entry[0]);
    }

    /**
     * حساب التشابه مع cache
     * @param {string} text1 - النص الأول
     * @param {string} text2 - النص الثاني
     * @param {number} textLength - طول النص
     * @param {string} contentType - نوع المحتوى
     * @returns {number} درجة التشابه
     */
    calculateSimilarityWithCache(text1, text2, textLength = null, contentType = 'academic') {
        const cacheKey = this.generateCacheKey(text1 + '|' + text2);

        // محاولة الحصول من الـ cache
        const cachedResult = this.getCachedResult(cacheKey);
        if (cachedResult !== null) {
            return cachedResult;
        }

        // حساب التشابه
        const similarity = this.calculateSimilarity(text1, text2, textLength, contentType);

        // حفظ في الـ cache
        this.setCachedResult(cacheKey, similarity);

        return similarity;
    }

    /**
     * الحصول على إحصائيات الـ cache
     * @returns {Object} إحصائيات الأداء
     */
    getCacheStats() {
        const totalRequests = this.cacheHits + this.cacheMisses;
        const hitRate = totalRequests > 0 ? (this.cacheHits / totalRequests) * 100 : 0;

        return {
            cacheSize: this.cache.size,
            maxCacheSize: this.maxCacheSize,
            hits: this.cacheHits,
            misses: this.cacheMisses,
            hitRate: hitRate.toFixed(1) + '%',
            indexSize: this.referenceIndex.size
        };
    }

    /**
     * إعادة تعيين إحصائيات الـ cache
     */
    resetCacheStats() {
        this.cacheHits = 0;
        this.cacheMisses = 0;
    }
}

module.exports = SimilarityAnalyzer;
