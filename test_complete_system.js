const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار شامل للنظام الكامل مع جميع التحسينات
 * الهدف: التأكد من دقة 95%+ مع دعم متعدد الصيغ واللغات
 */
async function testCompleteSystem() {
    console.log('🚀 اختبار شامل للنظام الكامل مع جميع التحسينات');
    console.log('=' .repeat(80));
    console.log('🎯 الهدف: دقة 95%+ مع دعم متعدد الصيغ واللغات');
    console.log('📚 المميزات: مستخرج نصوص محسن + محلل متعدد اللغات + قواعد بيانات شاملة');
    console.log('🌐 اللغات: العربية (1012+ عبارة) + الإنجليزية (1075+ عبارة)');
    console.log('📄 الصيغ: PDF, DOC/DOCX, TXT');
    console.log('=' .repeat(80));
    
    const checker = new PlagiarismChecker();
    
    // اختبار الملفات العربية
    console.log('\n🇸🇦 اختبار النظام الكامل - الملفات العربية:');
    const arabicTestFiles = [
        {
            file: 'real-research-tests/research_low_plagiarism_15percent.txt',
            expectedPercentage: 15,
            expectedLanguage: 'ar',
            description: 'بحث عربي منخفض الاستلال (15%)'
        },
        {
            file: 'real-research-tests/research_medium_plagiarism_50percent.txt',
            expectedPercentage: 50,
            expectedLanguage: 'ar',
            description: 'بحث عربي متوسط الاستلال (50%)'
        },
        {
            file: 'real-research-tests/research_high_plagiarism_85percent.txt',
            expectedPercentage: 85,
            expectedLanguage: 'ar',
            description: 'بحث عربي عالي الاستلال (85%)'
        }
    ];
    
    let arabicResults = [];
    
    for (const testCase of arabicTestFiles) {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`📄 ${testCase.description}`);
        
        try {
            const filePath = path.join(__dirname, testCase.file);
            
            if (!fs.existsSync(filePath)) {
                console.log(`❌ الملف غير موجود: ${filePath}`);
                continue;
            }
            
            const startTime = Date.now();
            
            // فحص شامل بالنظام الكامل
            const result = await checker.checkFile(filePath);
            
            const endTime = Date.now();
            const processingTime = (endTime - startTime) / 1000;
            
            // تقييم النتائج
            const accuracyError = Math.abs(result.plagiarismPercentage - testCase.expectedPercentage);
            const isAccurate = accuracyError <= 0.1; // دقة ±0.1%
            const languageCorrect = result.extractionInfo ? result.extractionInfo.language === testCase.expectedLanguage : true;
            
            console.log(`✅ النتائج الشاملة:`);
            console.log(`   📊 نسبة الاستلال: ${result.plagiarismPercentage}% (متوقع: ${testCase.expectedPercentage}%)`);
            console.log(`   🎯 دقة النتيجة: ${isAccurate ? '✅ ممتازة' : '❌ تحتاج تحسين'} (خطأ: ${accuracyError.toFixed(1)}%)`);
            console.log(`   📈 مستوى الخطر: ${result.riskLevel.label} (${result.riskLevel.level}/5)`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            
            // معلومات الاستخراج المحسن
            if (result.extractionInfo) {
                console.log(`   📄 معلومات الاستخراج:`);
                console.log(`      🌐 اللغة: ${result.extractionInfo.languageName} (${result.extractionInfo.language})`);
                console.log(`      🔍 صحة اللغة: ${languageCorrect ? '✅ صحيحة' : '❌ خاطئة'}`);
                console.log(`      📁 الصيغة: ${result.extractionInfo.format}`);
                console.log(`      📊 الكلمات: ${result.extractionInfo.statistics.words}`);
                console.log(`      📈 الكثافة الأكاديمية: ${(result.extractionInfo.statistics.academicDensity * 100).toFixed(1)}%`);
                console.log(`      🔧 استخراج محسن: ${result.extractionInfo.enhancedExtraction ? '✅ نعم' : '❌ لا'}`);
            }
            
            // معلومات التحليل
            if (result.analysis) {
                console.log(`   🔬 معلومات التحليل:`);
                console.log(`      🌐 تحليل متعدد اللغات: ${result.analysis.multilingualAnalysis ? '✅ نعم' : '❌ لا'}`);
                console.log(`      🚫 تجاهل Gemini: ${result.analysis.geminiIgnored ? '✅ نعم' : '❌ لا'}`);
                console.log(`      🔧 تحليل مستقل: ${result.analysis.internalAnalysisOnly ? '✅ نعم' : '❌ لا'}`);
                if (result.analysis.databaseSize) {
                    console.log(`      📚 قاعدة البيانات: ${result.analysis.databaseSize} عبارة`);
                }
                if (result.analysis.researchType) {
                    console.log(`      🎭 نوع البحث: ${result.analysis.researchType.type} (ثقة: ${(result.analysis.researchType.confidence * 100).toFixed(1)}%)`);
                }
            }
            
            console.log(`   ⏱️ وقت المعالجة الكامل: ${processingTime.toFixed(3)} ثانية`);
            console.log(`   📝 اسم الملف: ${result.fileName}`);
            
            arabicResults.push({
                file: testCase.file,
                expected: testCase.expectedPercentage,
                actual: result.plagiarismPercentage,
                error: accuracyError,
                accurate: isAccurate,
                language: result.extractionInfo ? result.extractionInfo.language : 'unknown',
                languageCorrect: languageCorrect,
                processingTime: processingTime,
                suspiciousSegments: result.suspiciousSegments.length,
                enhancedExtraction: result.extractionInfo ? result.extractionInfo.enhancedExtraction : false,
                multilingualAnalysis: result.analysis ? result.analysis.multilingualAnalysis : false
            });
            
        } catch (error) {
            console.error(`❌ خطأ في فحص ${testCase.file}:`, error.message);
        }
    }
    
    // إنشاء ملفات اختبار إنجليزية مؤقتة
    console.log('\n🇺🇸 إنشاء ملفات اختبار إنجليزية مؤقتة:');
    
    const englishTestTexts = [
        {
            content: `Title: The Impact of Digital Technology on University Learning Methods

Abstract: This study aims to explore how digital technology affects learning methods in universities. With the rapid development of technology in recent decades, educational institutions have witnessed tremendous growth in the use of digital tools and platforms. The research methodology employed in this study is comprehensive, incorporating both quantitative and qualitative approaches to understand the various impacts of digital transformation on academic learning.

The findings of this research contribute to the existing literature by providing empirical evidence for the effectiveness of digital learning tools. The study population consists of participants from diverse backgrounds, including students, faculty members, and administrative staff from multiple universities. The data collection process follows established protocols to ensure reliability and validity of results.

The analysis reveals significant patterns in the data, demonstrating strong correlations between digital technology adoption and improved learning outcomes. The results show that students who actively engage with digital learning platforms demonstrate higher academic performance compared to those using traditional methods only.`,
            expectedPercentage: 15,
            fileName: 'temp_english_low_15.txt',
            description: 'English research - Low plagiarism (15%)'
        },
        {
            content: `Research Title: Comprehensive Analysis of Technology Integration in Higher Education

The comprehensive analysis provides detailed insights into the subject matter of technology integration in educational settings. The systematic investigation reveals important patterns and relationships between digital tools and academic performance. The rigorous methodology ensures reliable and valid research outcomes that contribute to our understanding of modern educational practices.

The extensive literature review establishes theoretical foundations for understanding how technology transforms learning environments. The empirical evidence supports the proposed research hypotheses regarding the positive impact of digital integration on student engagement and academic achievement. The statistical analysis demonstrates significant correlations between variables related to technology use and learning outcomes.

The implementation framework provides systematic guidance for practitioners seeking to integrate technology effectively in their educational contexts. The quality assurance process ensures excellence in outcomes while maintaining high standards of academic integrity. The performance metrics demonstrate superior results when comparing technology-enhanced learning with traditional approaches.`,
            expectedPercentage: 50,
            fileName: 'temp_english_medium_50.txt',
            description: 'English research - Medium plagiarism (50%)'
        },
        {
            content: `Advanced Research: Technology Implementation in Academic Institutions

The comprehensive analysis provides detailed insights into the subject matter of educational technology implementation. The systematic investigation reveals important patterns and relationships between digital transformation and academic excellence. The rigorous methodology ensures reliable and valid research outcomes that support evidence-based decision making in higher education.

The implementation framework provides systematic guidance for practitioners seeking to optimize their educational technology strategies. The quality assurance process ensures excellence in outcomes while maintaining the highest standards of academic and professional integrity. The performance metrics demonstrate superior results across multiple dimensions of educational effectiveness.

The innovation demonstrates breakthrough technology advancement in educational settings. The development process incorporates cutting-edge methodologies that transform traditional academic practices. The creative solution addresses complex challenges effectively while enabling unprecedented capabilities in learning and teaching environments.

The breakthrough algorithm improves performance significantly in educational data analysis. The novel approach generates unexpected insights into student learning patterns and academic achievement factors. The innovative model predicts outcomes accurately while supporting evidence-based educational decision making processes.`,
            expectedPercentage: 85,
            fileName: 'temp_english_high_85.txt',
            description: 'English research - High plagiarism (85%)'
        }
    ];
    
    // إنشاء الملفات المؤقتة
    const tempFiles = [];
    englishTestTexts.forEach(testText => {
        const tempPath = path.join(__dirname, testText.fileName);
        fs.writeFileSync(tempPath, testText.content, 'utf8');
        tempFiles.push(tempPath);
        console.log(`✅ تم إنشاء: ${testText.fileName}`);
    });
    
    // اختبار الملفات الإنجليزية
    console.log('\n🇺🇸 اختبار النظام الكامل - الملفات الإنجليزية:');
    let englishResults = [];
    
    for (let i = 0; i < englishTestTexts.length; i++) {
        const testCase = englishTestTexts[i];
        const tempPath = tempFiles[i];
        
        console.log(`\n${'='.repeat(60)}`);
        console.log(`📄 ${testCase.description}`);
        
        try {
            const startTime = Date.now();
            
            // فحص شامل بالنظام الكامل
            const result = await checker.checkFile(tempPath);
            
            const endTime = Date.now();
            const processingTime = (endTime - startTime) / 1000;
            
            // تقييم النتائج
            const accuracyError = Math.abs(result.plagiarismPercentage - testCase.expectedPercentage);
            const isAccurate = accuracyError <= 0.1; // دقة ±0.1%
            const languageCorrect = result.extractionInfo ? result.extractionInfo.language === 'en' : true;
            
            console.log(`✅ النتائج الشاملة:`);
            console.log(`   📊 نسبة الاستلال: ${result.plagiarismPercentage}% (متوقع: ${testCase.expectedPercentage}%)`);
            console.log(`   🎯 دقة النتيجة: ${isAccurate ? '✅ ممتازة' : '❌ تحتاج تحسين'} (خطأ: ${accuracyError.toFixed(1)}%)`);
            console.log(`   📈 مستوى الخطر: ${result.riskLevel.label} (${result.riskLevel.level}/5)`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            
            // معلومات الاستخراج المحسن
            if (result.extractionInfo) {
                console.log(`   📄 معلومات الاستخراج:`);
                console.log(`      🌐 اللغة: ${result.extractionInfo.languageName} (${result.extractionInfo.language})`);
                console.log(`      🔍 صحة اللغة: ${languageCorrect ? '✅ صحيحة' : '❌ خاطئة'}`);
                console.log(`      📁 الصيغة: ${result.extractionInfo.format}`);
                console.log(`      📊 الكلمات: ${result.extractionInfo.statistics.words}`);
                console.log(`      📈 الكثافة الأكاديمية: ${(result.extractionInfo.statistics.academicDensity * 100).toFixed(1)}%`);
                console.log(`      🔧 استخراج محسن: ${result.extractionInfo.enhancedExtraction ? '✅ نعم' : '❌ لا'}`);
            }
            
            // معلومات التحليل
            if (result.analysis) {
                console.log(`   🔬 معلومات التحليل:`);
                console.log(`      🌐 تحليل متعدد اللغات: ${result.analysis.multilingualAnalysis ? '✅ نعم' : '❌ لا'}`);
                console.log(`      🚫 تجاهل Gemini: ${result.analysis.geminiIgnored ? '✅ نعم' : '❌ لا'}`);
                console.log(`      🔧 تحليل مستقل: ${result.analysis.internalAnalysisOnly ? '✅ نعم' : '❌ لا'}`);
                if (result.analysis.databaseSize) {
                    console.log(`      📚 قاعدة البيانات: ${result.analysis.databaseSize} عبارة`);
                }
                if (result.analysis.researchType) {
                    console.log(`      🎭 نوع البحث: ${result.analysis.researchType.type} (ثقة: ${(result.analysis.researchType.confidence * 100).toFixed(1)}%)`);
                }
            }
            
            console.log(`   ⏱️ وقت المعالجة الكامل: ${processingTime.toFixed(3)} ثانية`);
            console.log(`   📝 اسم الملف: ${result.fileName}`);
            
            englishResults.push({
                description: testCase.description,
                expected: testCase.expectedPercentage,
                actual: result.plagiarismPercentage,
                error: accuracyError,
                accurate: isAccurate,
                language: result.extractionInfo ? result.extractionInfo.language : 'unknown',
                languageCorrect: languageCorrect,
                processingTime: processingTime,
                suspiciousSegments: result.suspiciousSegments.length,
                enhancedExtraction: result.extractionInfo ? result.extractionInfo.enhancedExtraction : false,
                multilingualAnalysis: result.analysis ? result.analysis.multilingualAnalysis : false
            });
            
        } catch (error) {
            console.error(`❌ خطأ في فحص ${testCase.fileName}:`, error.message);
        }
    }
    
    // تنظيف الملفات المؤقتة
    console.log('\n🧹 تنظيف الملفات المؤقتة:');
    tempFiles.forEach(tempPath => {
        try {
            fs.unlinkSync(tempPath);
            console.log(`✅ تم حذف: ${path.basename(tempPath)}`);
        } catch (error) {
            console.log(`⚠️ لم يتم حذف: ${path.basename(tempPath)}`);
        }
    });
    
    // تقرير شامل نهائي
    console.log('\n' + '=' .repeat(80));
    console.log('📊 تقرير شامل نهائي للنظام الكامل');
    console.log('=' .repeat(80));
    
    // إحصائيات العربية
    const arabicAccurate = arabicResults.filter(r => r.accurate).length;
    const arabicLanguageCorrect = arabicResults.filter(r => r.languageCorrect).length;
    const arabicEnhanced = arabicResults.filter(r => r.enhancedExtraction).length;
    const arabicMultilingual = arabicResults.filter(r => r.multilingualAnalysis).length;
    const arabicAvgError = arabicResults.length > 0 ? arabicResults.reduce((sum, r) => sum + r.error, 0) / arabicResults.length : 0;
    const arabicAvgTime = arabicResults.length > 0 ? arabicResults.reduce((sum, r) => sum + r.processingTime, 0) / arabicResults.length : 0;
    
    console.log(`\n🇸🇦 نتائج العربية الشاملة:`);
    console.log(`   📊 دقة النتائج: ${arabicAccurate}/${arabicResults.length} (${((arabicAccurate / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🌐 دقة اللغة: ${arabicLanguageCorrect}/${arabicResults.length} (${((arabicLanguageCorrect / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🔧 استخراج محسن: ${arabicEnhanced}/${arabicResults.length} (${((arabicEnhanced / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🌐 تحليل متعدد اللغات: ${arabicMultilingual}/${arabicResults.length} (${((arabicMultilingual / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   📈 متوسط الخطأ: ${arabicAvgError.toFixed(2)}%`);
    console.log(`   ⏱️ متوسط الوقت: ${arabicAvgTime.toFixed(3)} ثانية`);
    
    // إحصائيات الإنجليزية
    const englishAccurate = englishResults.filter(r => r.accurate).length;
    const englishLanguageCorrect = englishResults.filter(r => r.languageCorrect).length;
    const englishEnhanced = englishResults.filter(r => r.enhancedExtraction).length;
    const englishMultilingual = englishResults.filter(r => r.multilingualAnalysis).length;
    const englishAvgError = englishResults.length > 0 ? englishResults.reduce((sum, r) => sum + r.error, 0) / englishResults.length : 0;
    const englishAvgTime = englishResults.length > 0 ? englishResults.reduce((sum, r) => sum + r.processingTime, 0) / englishResults.length : 0;
    
    console.log(`\n🇺🇸 نتائج الإنجليزية الشاملة:`);
    console.log(`   📊 دقة النتائج: ${englishAccurate}/${englishResults.length} (${((englishAccurate / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🌐 دقة اللغة: ${englishLanguageCorrect}/${englishResults.length} (${((englishLanguageCorrect / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🔧 استخراج محسن: ${englishEnhanced}/${englishResults.length} (${((englishEnhanced / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🌐 تحليل متعدد اللغات: ${englishMultilingual}/${englishResults.length} (${((englishMultilingual / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   📈 متوسط الخطأ: ${englishAvgError.toFixed(2)}%`);
    console.log(`   ⏱️ متوسط الوقت: ${englishAvgTime.toFixed(3)} ثانية`);
    
    // النتيجة الإجمالية النهائية
    const totalTests = arabicResults.length + englishResults.length;
    const totalAccurate = arabicAccurate + englishAccurate;
    const totalLanguageCorrect = arabicLanguageCorrect + englishLanguageCorrect;
    const totalEnhanced = arabicEnhanced + englishEnhanced;
    const totalMultilingual = arabicMultilingual + englishMultilingual;
    const overallAccuracy = totalTests > 0 ? (totalAccurate / totalTests) * 100 : 0;
    const overallLanguageAccuracy = totalTests > 0 ? (totalLanguageCorrect / totalTests) * 100 : 0;
    const overallEnhanced = totalTests > 0 ? (totalEnhanced / totalTests) * 100 : 0;
    const overallMultilingual = totalTests > 0 ? (totalMultilingual / totalTests) * 100 : 0;
    
    console.log(`\n🌐 النتيجة الإجمالية النهائية:`);
    console.log(`   📊 دقة النتائج الإجمالية: ${totalAccurate}/${totalTests} (${overallAccuracy.toFixed(1)}%)`);
    console.log(`   🌐 دقة اللغة الإجمالية: ${totalLanguageCorrect}/${totalTests} (${overallLanguageAccuracy.toFixed(1)}%)`);
    console.log(`   🔧 استخراج محسن: ${totalEnhanced}/${totalTests} (${overallEnhanced.toFixed(1)}%)`);
    console.log(`   🌐 تحليل متعدد اللغات: ${totalMultilingual}/${totalTests} (${overallMultilingual.toFixed(1)}%)`);
    console.log(`   🎯 تحقيق الهدف (95%+): ${overallAccuracy >= 95 ? '✅ نعم' : '❌ لا'}`);
    
    // تقييم شامل للنظام
    const systemScore = (overallAccuracy + overallLanguageAccuracy + overallEnhanced + overallMultilingual) / 4;
    
    console.log(`\n🏆 تقييم شامل للنظام:`);
    console.log(`   📊 نقاط النظام الإجمالية: ${systemScore.toFixed(1)}%`);
    
    if (overallAccuracy >= 95 && overallLanguageAccuracy >= 95 && overallEnhanced >= 95 && overallMultilingual >= 95) {
        console.log('\n🎉🎉🎉 مبروك! النظام الكامل يحقق جميع الأهداف بامتياز!');
        console.log('✅ دقة 95%+ للاستلال');
        console.log('✅ دقة 95%+ لتحديد اللغة');
        console.log('✅ استخراج نصوص محسن 95%+');
        console.log('✅ تحليل متعدد اللغات 95%+');
        console.log('🚀 النظام جاهز للاستخدام في الإنتاج');
        console.log('🌟 يدعم العربية والإنجليزية بدقة عالية');
        console.log('📄 يدعم PDF, DOC/DOCX, TXT');
        console.log('⚡ سرعة فائقة وتحليل مستقل');
    } else {
        console.log('\n⚠️ النظام يحتاج تحسينات إضافية في بعض المجالات');
    }
    
    console.log('\n' + '=' .repeat(80));
    console.log('✅ تم الانتهاء من الاختبار الشامل للنظام الكامل');
}

// تشغيل الاختبار الشامل
async function main() {
    try {
        await testCompleteSystem();
    } catch (error) {
        console.error('❌ خطأ في الاختبار الشامل:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testCompleteSystem };
