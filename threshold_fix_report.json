{"timestamp": "2025-07-05T00:10:30.445Z", "test_type": "threshold_fix_test", "old_thresholds": {"high": 80, "medium": 65, "low": 45, "dynamic": "40-90%"}, "new_thresholds": {"high": 35, "medium": 25, "low": 20, "dynamic": "20-40%"}, "results": {"avgAccuracy": 52.679738562091494, "successRate": 33.33333333333333, "avgTime": 335, "avgThreshold": 25.666666666666664, "improvement": {"accuracy": 14.179738562091494, "successRate": 33.33333333333333}}, "detailed_results": [{"file": "test_large_10_percent.txt", "expected": 15, "actual": 14, "accuracy": 93.33333333333333, "processingTime": 365, "threshold": 0.26, "suspiciousCount": 1, "passed": true}, {"file": "test_large_50_percent.txt", "expected": 55, "actual": 22, "accuracy": 40, "processingTime": 298, "threshold": 0.25, "suspiciousCount": 15, "passed": false}, {"file": "test_large_90_percent.txt", "expected": 85, "actual": 21, "accuracy": 24.705882352941174, "processingTime": 342, "threshold": 0.26, "suspiciousCount": 11, "passed": false}]}