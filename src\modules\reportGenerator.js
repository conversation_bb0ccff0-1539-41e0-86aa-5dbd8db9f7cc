const PDFDocument = require('pdfkit');
const fs = require('fs');
const path = require('path');

/**
 * وحدة إنتاج تقارير PDF
 */
class ReportGenerator {
    constructor() {
        this.colors = {
            primary: '#667eea',
            secondary: '#764ba2',
            success: '#27ae60',
            warning: '#f39c12',
            danger: '#e74c3c',
            dark: '#2c3e50',
            light: '#ecf0f1'
        };
        
        this.fonts = {
            arabic: 'assets/fonts/Cairo-Regular.ttf',
            arabicBold: 'assets/fonts/Cairo-Bold.ttf'
        };
    }

    /**
     * إنتاج تقرير PDF شامل
     * @param {Object} results - نتائج الفحص
     * @param {string} outputPath - مسار الحفظ
     * @returns {Promise<string>} مسار الملف المحفوظ
     */
    async generateReport(results, outputPath) {
        try {
            const doc = new PDFDocument({
                size: 'A4',
                margins: { top: 50, bottom: 50, left: 50, right: 50 },
                info: {
                    Title: 'تقرير فحص الاستلال',
                    Author: 'Plagiarism Checker Pro',
                    Subject: 'تحليل الاستلال الأكاديمي',
                    Keywords: 'plagiarism, academic, analysis'
                }
            });

            // إعداد الخط العربي (إذا كان متوفراً)
            this.setupFonts(doc);

            // إنشاء التقرير
            await this.createHeader(doc, results);
            await this.createSummary(doc, results);
            await this.createDetailedAnalysis(doc, results);
            await this.createSuspiciousSegments(doc, results);
            await this.createAIAnalysis(doc, results);
            await this.createRecommendations(doc, results);
            await this.createFooter(doc);

            // حفظ الملف
            doc.pipe(fs.createWriteStream(outputPath));
            doc.end();

            return new Promise((resolve, reject) => {
                doc.on('end', () => resolve(outputPath));
                doc.on('error', reject);
            });

        } catch (error) {
            console.error('خطأ في إنتاج التقرير:', error);
            throw new Error(`فشل في إنتاج التقرير: ${error.message}`);
        }
    }

    /**
     * إعداد الخطوط
     * @param {PDFDocument} doc - مستند PDF
     */
    setupFonts(doc) {
        try {
            // محاولة تحميل الخط العربي
            if (fs.existsSync(this.fonts.arabic)) {
                doc.registerFont('Arabic', this.fonts.arabic);
                doc.registerFont('ArabicBold', this.fonts.arabicBold);
            }
        } catch (error) {
            console.warn('تعذر تحميل الخط العربي، سيتم استخدام الخط الافتراضي');
        }
    }

    /**
     * إنشاء رأس التقرير
     * @param {PDFDocument} doc - مستند PDF
     * @param {Object} results - نتائج الفحص
     */
    async createHeader(doc, results) {
        // خلفية الرأس
        doc.rect(0, 0, doc.page.width, 120)
           .fill(this.colors.primary);

        // العنوان الرئيسي
        doc.fillColor('white')
           .fontSize(24)
           .font('Helvetica-Bold')
           .text('تقرير فحص الاستلال الأكاديمي', 50, 30, { align: 'center' });

        doc.fontSize(16)
           .text('Plagiarism Detection Report', 50, 60, { align: 'center' });

        // معلومات الملف
        doc.fillColor(this.colors.dark)
           .fontSize(12)
           .font('Helvetica')
           .text(`اسم الملف: ${results.fileName || 'غير محدد'}`, 50, 140);

        doc.text(`تاريخ الفحص: ${new Date().toLocaleDateString('ar-SA')}`, 50, 160);
        doc.text(`وقت الفحص: ${new Date().toLocaleTimeString('ar-SA')}`, 50, 180);

        doc.moveDown(2);
    }

    /**
     * إنشاء ملخص النتائج
     * @param {PDFDocument} doc - مستند PDF
     * @param {Object} results - نتائج الفحص
     */
    async createSummary(doc, results) {
        const y = doc.y + 20;
        
        // عنوان القسم
        this.addSectionTitle(doc, 'ملخص النتائج', y);

        // نسبة الاستلال الرئيسية
        const plagiarismScore = results.plagiarismPercentage || 0;
        const scoreColor = this.getScoreColor(plagiarismScore);

        // مربع النتيجة
        doc.rect(50, doc.y + 10, 200, 80)
           .stroke(scoreColor)
           .lineWidth(2);

        doc.fillColor(scoreColor)
           .fontSize(36)
           .font('Helvetica-Bold')
           .text(`${plagiarismScore}%`, 60, doc.y + 30);

        doc.fillColor(this.colors.dark)
           .fontSize(12)
           .font('Helvetica')
           .text('نسبة الاستلال المكتشفة', 60, doc.y + 10);

        // الإحصائيات
        const stats = results.statistics || {};
        const statsY = doc.y - 60;

        doc.fillColor(this.colors.dark)
           .fontSize(11)
           .text(`إجمالي الجمل: ${stats.totalSentences || 0}`, 300, statsY);
        
        doc.text(`الجمل المفحوصة: ${stats.checkedSentences || 0}`, 300, statsY + 20);
        doc.text(`الأجزاء المشكوك بها: ${stats.suspiciousCount || 0}`, 300, statsY + 40);
        doc.text(`تشابه عالي: ${stats.highSimilarityCount || 0}`, 300, statsY + 60);

        doc.moveDown(3);
    }

    /**
     * إنشاء التحليل التفصيلي
     * @param {PDFDocument} doc - مستند PDF
     * @param {Object} results - نتائج الفحص
     */
    async createDetailedAnalysis(doc, results) {
        this.addSectionTitle(doc, 'التحليل التفصيلي');

        const analysis = results.analysis || {};
        
        // رسم بياني للنتائج
        this.drawScoreChart(doc, results.plagiarismPercentage || 0);

        doc.moveDown(1);

        // تفاصيل التحليل
        doc.fillColor(this.colors.dark)
           .fontSize(11)
           .font('Helvetica');

        if (analysis.overallScore) {
            doc.text(`النتيجة الإجمالية: ${(analysis.overallScore * 100).toFixed(1)}%`);
        }

        if (analysis.statistics) {
            doc.text(`معدل التشابه: ${(analysis.statistics.averageSimilarity || 0).toFixed(2)}`);
        }

        doc.moveDown(2);
    }

    /**
     * إنشاء قسم الأجزاء المشكوك بها
     * @param {PDFDocument} doc - مستند PDF
     * @param {Object} results - نتائج الفحص
     */
    async createSuspiciousSegments(doc, results) {
        this.addSectionTitle(doc, 'الأجزاء المشكوك بها');

        const suspiciousSegments = results.suspiciousSegments || [];

        if (suspiciousSegments.length === 0) {
            doc.fillColor(this.colors.success)
               .fontSize(12)
               .text('لم يتم العثور على أجزاء مشكوك بها');
            doc.moveDown(2);
            return;
        }

        suspiciousSegments.slice(0, 10).forEach((segment, index) => {
            // التحقق من المساحة المتبقية
            if (doc.y > 700) {
                doc.addPage();
            }

            const similarity = Math.round(segment.similarity * 100);
            const typeColor = this.getSimilarityColor(segment.type);

            // رقم الجزء
            doc.fillColor(typeColor)
               .fontSize(10)
               .font('Helvetica-Bold')
               .text(`${index + 1}. تشابه ${similarity}%`, 50, doc.y + 5);

            // النص المشكوك به
            doc.fillColor(this.colors.dark)
               .fontSize(10)
               .font('Helvetica')
               .text(segment.text.substring(0, 200) + (segment.text.length > 200 ? '...' : ''), 
                     50, doc.y + 5, { width: 500, align: 'right' });

            if (segment.matchedText) {
                doc.fillColor('#666')
                   .fontSize(9)
                   .text(`مطابق مع: ${segment.matchedText.substring(0, 100)}...`, 
                         50, doc.y + 5, { width: 500 });
            }

            doc.moveDown(1);
        });

        if (suspiciousSegments.length > 10) {
            doc.fillColor('#666')
               .fontSize(10)
               .text(`... و ${suspiciousSegments.length - 10} جزء آخر`);
        }

        doc.moveDown(2);
    }

    /**
     * إنشاء قسم تحليل الذكاء الاصطناعي
     * @param {PDFDocument} doc - مستند PDF
     * @param {Object} results - نتائج الفحص
     */
    async createAIAnalysis(doc, results) {
        if (!results.hasAIAnalysis || !results.aiAnalysis) {
            return;
        }

        this.addSectionTitle(doc, 'تحليل الذكاء الاصطناعي');

        const aiAnalysis = results.aiAnalysis;

        doc.fillColor(this.colors.dark)
           .fontSize(11)
           .font('Helvetica');

        if (aiAnalysis.originalityScore !== null) {
            doc.text(`نتيجة الأصالة: ${aiAnalysis.originalityScore}%`);
        }

        doc.text(`مصدر التحليل: ${aiAnalysis.source}`);
        doc.text(`مستوى الثقة: ${aiAnalysis.confidence}%`);

        if (aiAnalysis.paraphrasingIndicators.length > 0) {
            doc.moveDown(0.5);
            doc.text('علامات إعادة الصياغة:');
            aiAnalysis.paraphrasingIndicators.forEach(indicator => {
                doc.text(`• ${indicator}`, 70);
            });
        }

        if (aiAnalysis.writingPatterns.length > 0) {
            doc.moveDown(0.5);
            doc.text('أنماط الكتابة المكتشفة:');
            aiAnalysis.writingPatterns.forEach(pattern => {
                doc.text(`• ${pattern}`, 70);
            });
        }

        doc.moveDown(2);
    }

    /**
     * إنشاء قسم التوصيات
     * @param {PDFDocument} doc - مستند PDF
     * @param {Object} results - نتائج الفحص
     */
    async createRecommendations(doc, results) {
        this.addSectionTitle(doc, 'التوصيات');

        const recommendations = results.recommendations || [];

        if (recommendations.length === 0) {
            doc.fillColor(this.colors.dark)
               .fontSize(11)
               .text('لا توجد توصيات محددة');
            return;
        }

        doc.fillColor(this.colors.dark)
           .fontSize(11)
           .font('Helvetica');

        recommendations.forEach((recommendation, index) => {
            doc.text(`${index + 1}. ${recommendation}`, 50, doc.y + 5, { width: 500 });
            doc.moveDown(0.5);
        });

        doc.moveDown(2);
    }

    /**
     * إنشاء تذييل التقرير
     * @param {PDFDocument} doc - مستند PDF
     */
    async createFooter(doc) {
        try {
            const pageCount = doc.bufferedPageRange().count;

            for (let i = 0; i < pageCount; i++) {
                // التحقق من صحة رقم الصفحة
                if (i >= 0 && i < pageCount) {
                    doc.switchToPage(i);

                    // خط فاصل
                    doc.moveTo(50, doc.page.height - 80)
                       .lineTo(doc.page.width - 50, doc.page.height - 80)
                       .stroke('#ccc');

                    // معلومات التذييل
                    doc.fillColor('#666')
                       .fontSize(9)
                       .font('Helvetica')
                       .text('تم إنتاج هذا التقرير بواسطة Plagiarism Checker Pro',
                             50, doc.page.height - 60);

                    doc.text(`صفحة ${i + 1} من ${pageCount}`,
                             doc.page.width - 100, doc.page.height - 60);
                }
            }
        } catch (error) {
            console.warn('تعذر إنشاء التذييل:', error);
            // إضافة تذييل بسيط في الصفحة الحالية
            doc.fillColor('#666')
               .fontSize(9)
               .font('Helvetica')
               .text('تم إنتاج هذا التقرير بواسطة Plagiarism Checker Pro',
                     50, doc.page.height - 60);
        }
    }

    /**
     * إضافة عنوان قسم
     * @param {PDFDocument} doc - مستند PDF
     * @param {string} title - العنوان
     * @param {number} y - الموضع العمودي (اختياري)
     */
    addSectionTitle(doc, title, y = null) {
        if (y !== null) {
            doc.y = y;
        }

        doc.fillColor(this.colors.primary)
           .fontSize(14)
           .font('Helvetica-Bold')
           .text(title, 50, doc.y + 10);

        // خط تحت العنوان
        doc.moveTo(50, doc.y + 5)
           .lineTo(200, doc.y + 5)
           .stroke(this.colors.primary);

        doc.moveDown(1);
    }

    /**
     * رسم مخطط النتيجة
     * @param {PDFDocument} doc - مستند PDF
     * @param {number} score - النتيجة
     */
    drawScoreChart(doc, score) {
        const centerX = 400;
        const centerY = doc.y + 50;
        const radius = 40;

        // دائرة الخلفية
        doc.circle(centerX, centerY, radius)
           .stroke('#ddd')
           .lineWidth(8);

        // دائرة النتيجة
        const angle = (score / 100) * 360;
        const color = this.getScoreColor(score);
        
        doc.circle(centerX, centerY, radius)
           .stroke(color)
           .lineWidth(8);

        // النص في المنتصف
        doc.fillColor(color)
           .fontSize(16)
           .font('Helvetica-Bold')
           .text(`${score}%`, centerX - 15, centerY - 8);

        doc.moveDown(3);
    }

    /**
     * الحصول على لون النتيجة
     * @param {number} score - النتيجة
     * @returns {string} اللون
     */
    getScoreColor(score) {
        if (score >= 70) return this.colors.danger;
        if (score >= 40) return this.colors.warning;
        return this.colors.success;
    }

    /**
     * الحصول على لون التشابه
     * @param {string} type - نوع التشابه
     * @returns {string} اللون
     */
    getSimilarityColor(type) {
        switch (type) {
            case 'high': return this.colors.danger;
            case 'medium': return this.colors.warning;
            case 'low': return this.colors.success;
            default: return this.colors.dark;
        }
    }

    /**
     * إنتاج تقرير PDF مبسط (بدون Puppeteer)
     * @param {Object} results - نتائج الفحص
     * @param {string} outputPath - مسار الحفظ
     * @returns {Promise<string>} مسار الملف المحفوظ
     */
    async generateSimplePDF(results, outputPath) {
        try {
            // حفظ HTML كملف مؤقت
            const htmlContent = this.generateHTMLReport(results);
            const htmlPath = outputPath.replace('.pdf', '.html');

            fs.writeFileSync(htmlPath, htmlContent, 'utf8');

            console.log(`تم حفظ التقرير كـ HTML: ${htmlPath}`);
            console.log('يمكنك فتح الملف في المتصفح وطباعته كـ PDF');

            return htmlPath;

        } catch (error) {
            console.error('خطأ في إنتاج التقرير:', error);
            throw new Error(`فشل في إنتاج التقرير: ${error.message}`);
        }
    }

    /**
     * إنتاج تقرير مبسط (HTML)
     * @param {Object} results - نتائج الفحص
     * @returns {string} HTML للتقرير
     */
    generateHTMLReport(results) {
        const plagiarismScore = results.plagiarismPercentage || 0;
        const scoreColor = this.getScoreColor(plagiarismScore);

        return `
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <title>تقرير فحص الاستلال</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .header {
            background: linear-gradient(135deg, ${this.colors.primary}, ${this.colors.secondary});
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 { margin: 0; font-size: 28px; }
        .header p { margin: 10px 0 0 0; opacity: 0.9; }
        .summary {
            background: #f8f9fa;
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #dee2e6;
        }
        .score {
            font-size: 64px;
            color: ${scoreColor};
            font-weight: bold;
            margin: 10px 0;
        }
        .score-label {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 20px;
        }
        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 20px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: ${this.colors.primary};
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
        }
        .details {
            padding: 30px;
        }
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: ${this.colors.dark};
            margin-bottom: 20px;
            border-bottom: 2px solid ${this.colors.primary};
            padding-bottom: 10px;
        }
        .suspicious {
            background: #fff5f5;
            border: 1px solid #fed7d7;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .similarity-badge {
            background: ${scoreColor};
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 10px;
            display: inline-block;
        }
        .recommendations {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }
        .recommendations ul {
            margin: 10px 0;
            padding-right: 20px;
        }
        .recommendations li {
            margin-bottom: 8px;
            line-height: 1.5;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            color: #6c757d;
            font-size: 12px;
            border-top: 1px solid #dee2e6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>تقرير فحص الاستلال الأكاديمي</h1>
            <p>تاريخ الفحص: ${new Date().toLocaleDateString('ar-SA')} - ${new Date().toLocaleTimeString('ar-SA')}</p>
            <p>اسم الملف: ${results.fileName || 'غير محدد'}</p>
        </div>

        <div class="summary">
            <div class="score">${plagiarismScore}%</div>
            <div class="score-label">نسبة الاستلال المكتشفة</div>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-value">${results.statistics?.totalWords || 0}</div>
                    <div class="stat-label">إجمالي الكلمات</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${results.statistics?.suspiciousCount || 0}</div>
                    <div class="stat-label">أجزاء مشكوك بها</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">${results.statistics?.highSimilarityCount || 0}</div>
                    <div class="stat-label">تشابه عالي</div>
                </div>
            </div>
        </div>

        <div class="details">
            <div class="section-title">الأجزاء المشكوك بها</div>
            ${(results.suspiciousSegments || []).length === 0 ?
                '<p style="color: #28a745; font-weight: bold;">✅ لم يتم العثور على أجزاء مشكوك بها</p>' :
                (results.suspiciousSegments || []).slice(0, 10).map((segment, index) => `
                    <div class="suspicious">
                        <div class="similarity-badge">تشابه ${Math.round((segment.similarity || 0) * 100)}%</div>
                        <p><strong>الجزء ${index + 1}:</strong></p>
                        <p>${segment.text}</p>
                        ${segment.matchedText ? `<p><small><strong>مطابق مع:</strong> ${segment.matchedText.substring(0, 150)}...</small></p>` : ''}
                    </div>
                `).join('')
            }

            ${results.recommendations && results.recommendations.length > 0 ? `
                <div class="recommendations">
                    <div class="section-title">التوصيات</div>
                    <ul>
                        ${results.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
        </div>

        <div class="footer">
            <p>تم إنتاج هذا التقرير بواسطة Plagiarism Checker Pro</p>
            <p>© 2024 جميع الحقوق محفوظة</p>
        </div>
    </div>
</body>
</html>`;
    }
}

module.exports = ReportGenerator;
