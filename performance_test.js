const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبارات الأداء والضغط
 */
class PerformanceTestSuite {
    constructor() {
        this.performanceResults = [];
        this.memoryUsage = [];
        this.stressTestResults = [];
    }

    /**
     * تشغيل جميع اختبارات الأداء
     */
    async runPerformanceTests() {
        console.log('⚡ بدء اختبارات الأداء والضغط');
        console.log('=' .repeat(50));

        // اختبار الأداء مع أحجام مختلفة
        await this.testDifferentFileSizes();
        
        // اختبار الذاكرة
        await this.testMemoryUsage();
        
        // اختبار الضغط
        await this.testStressLoad();
        
        // اختبار التزامن
        await this.testConcurrency();
        
        // إنتاج تقرير الأداء
        this.generatePerformanceReport();
    }

    /**
     * اختبار أحجام ملفات مختلفة
     */
    async testDifferentFileSizes() {
        console.log('\n📏 اختبار أحجام ملفات مختلفة:');
        
        const testSizes = [
            { words: 100, name: 'صغير' },
            { words: 500, name: 'متوسط' },
            { words: 1000, name: 'كبير' },
            { words: 2000, name: 'كبير جداً' }
        ];

        for (const size of testSizes) {
            const testFile = this.generateTestFile(size.words);
            const filePath = path.join(__dirname, `temp_${size.words}_words.txt`);
            
            fs.writeFileSync(filePath, testFile, 'utf8');
            
            console.log(`\n   📄 اختبار ملف ${size.name} (${size.words} كلمة):`);
            
            const startTime = Date.now();
            const startMemory = process.memoryUsage();
            
            try {
                const checker = new PlagiarismChecker();
                const results = await checker.checkFile(filePath);
                
                const endTime = Date.now();
                const endMemory = process.memoryUsage();
                
                const processingTime = endTime - startTime;
                const memoryDelta = endMemory.heapUsed - startMemory.heapUsed;
                const wordsPerSecond = size.words / (processingTime / 1000);
                
                this.performanceResults.push({
                    fileSize: size.name,
                    wordCount: size.words,
                    processingTime: processingTime,
                    wordsPerSecond: wordsPerSecond,
                    memoryUsed: memoryDelta,
                    plagiarismScore: results.plagiarismPercentage
                });
                
                console.log(`      ⏱️ وقت المعالجة: ${processingTime}ms`);
                console.log(`      🚀 السرعة: ${wordsPerSecond.toFixed(1)} كلمة/ثانية`);
                console.log(`      💾 استخدام الذاكرة: ${(memoryDelta / 1024 / 1024).toFixed(2)} MB`);
                console.log(`      📊 نسبة الاستلال: ${results.plagiarismPercentage}%`);
                
            } catch (error) {
                console.log(`      ❌ خطأ: ${error.message}`);
            } finally {
                // تنظيف الملف المؤقت
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                }
            }
        }
    }

    /**
     * اختبار استخدام الذاكرة
     */
    async testMemoryUsage() {
        console.log('\n💾 اختبار استخدام الذاكرة:');
        
        const checker = new PlagiarismChecker();
        const testFile = this.generateTestFile(1000);
        const filePath = path.join(__dirname, 'memory_test.txt');
        
        fs.writeFileSync(filePath, testFile, 'utf8');
        
        // مراقبة الذاكرة أثناء المعالجة
        const memoryMonitor = setInterval(() => {
            const usage = process.memoryUsage();
            this.memoryUsage.push({
                timestamp: Date.now(),
                heapUsed: usage.heapUsed,
                heapTotal: usage.heapTotal,
                external: usage.external,
                rss: usage.rss
            });
        }, 100);
        
        try {
            const startMemory = process.memoryUsage();
            console.log(`   📊 ذاكرة البداية: ${(startMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
            
            const results = await checker.checkFile(filePath);
            
            const endMemory = process.memoryUsage();
            console.log(`   📊 ذاكرة النهاية: ${(endMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
            console.log(`   📈 الزيادة: ${((endMemory.heapUsed - startMemory.heapUsed) / 1024 / 1024).toFixed(2)} MB`);
            
            // حساب الذروة
            const peakMemory = Math.max(...this.memoryUsage.map(m => m.heapUsed));
            console.log(`   🔝 ذروة الاستخدام: ${(peakMemory / 1024 / 1024).toFixed(2)} MB`);
            
        } catch (error) {
            console.log(`   ❌ خطأ في اختبار الذاكرة: ${error.message}`);
        } finally {
            clearInterval(memoryMonitor);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        }
    }

    /**
     * اختبار الضغط
     */
    async testStressLoad() {
        console.log('\n🔥 اختبار الضغط (معالجة متتالية):');
        
        const iterations = 5;
        const testFile = this.generateTestFile(500);
        const filePath = path.join(__dirname, 'stress_test.txt');
        
        fs.writeFileSync(filePath, testFile, 'utf8');
        
        const checker = new PlagiarismChecker();
        const results = [];
        
        for (let i = 1; i <= iterations; i++) {
            console.log(`   🔄 التكرار ${i}/${iterations}:`);
            
            const startTime = Date.now();
            const startMemory = process.memoryUsage();
            
            try {
                const result = await checker.checkFile(filePath);
                const endTime = Date.now();
                const endMemory = process.memoryUsage();
                
                const iterationResult = {
                    iteration: i,
                    processingTime: endTime - startTime,
                    memoryUsed: endMemory.heapUsed - startMemory.heapUsed,
                    plagiarismScore: result.plagiarismPercentage
                };
                
                results.push(iterationResult);
                
                console.log(`      ⏱️ الوقت: ${iterationResult.processingTime}ms`);
                console.log(`      💾 الذاكرة: ${(iterationResult.memoryUsed / 1024 / 1024).toFixed(2)} MB`);
                
            } catch (error) {
                console.log(`      ❌ خطأ في التكرار ${i}: ${error.message}`);
            }
        }
        
        // تحليل نتائج الضغط
        if (results.length > 0) {
            const avgTime = results.reduce((sum, r) => sum + r.processingTime, 0) / results.length;
            const avgMemory = results.reduce((sum, r) => sum + r.memoryUsed, 0) / results.length;
            
            console.log(`\n   📊 متوسط الوقت: ${avgTime.toFixed(0)}ms`);
            console.log(`   📊 متوسط الذاكرة: ${(avgMemory / 1024 / 1024).toFixed(2)} MB`);
            
            this.stressTestResults = results;
        }
        
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
        }
    }

    /**
     * اختبار التزامن
     */
    async testConcurrency() {
        console.log('\n🔀 اختبار التزامن (معالجة متوازية):');
        
        const concurrentTests = 3;
        const testFile = this.generateTestFile(300);
        const promises = [];
        
        for (let i = 1; i <= concurrentTests; i++) {
            const filePath = path.join(__dirname, `concurrent_test_${i}.txt`);
            fs.writeFileSync(filePath, testFile, 'utf8');
            
            const checker = new PlagiarismChecker();
            const promise = this.runConcurrentTest(checker, filePath, i);
            promises.push(promise);
        }
        
        try {
            const startTime = Date.now();
            const results = await Promise.all(promises);
            const endTime = Date.now();
            
            console.log(`   ⏱️ إجمالي الوقت: ${endTime - startTime}ms`);
            console.log(`   📊 عدد العمليات المتزامنة: ${concurrentTests}`);
            
            results.forEach((result, index) => {
                console.log(`   🔄 العملية ${index + 1}: ${result.processingTime}ms`);
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ في اختبار التزامن: ${error.message}`);
        } finally {
            // تنظيف الملفات المؤقتة
            for (let i = 1; i <= concurrentTests; i++) {
                const filePath = path.join(__dirname, `concurrent_test_${i}.txt`);
                if (fs.existsSync(filePath)) {
                    fs.unlinkSync(filePath);
                }
            }
        }
    }

    /**
     * تشغيل اختبار متزامن واحد
     */
    async runConcurrentTest(checker, filePath, testId) {
        const startTime = Date.now();
        
        try {
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            return {
                testId: testId,
                processingTime: endTime - startTime,
                plagiarismScore: result.plagiarismPercentage,
                status: 'success'
            };
        } catch (error) {
            return {
                testId: testId,
                processingTime: 0,
                error: error.message,
                status: 'failed'
            };
        }
    }

    /**
     * إنتاج ملف اختبار بحجم محدد
     */
    generateTestFile(wordCount) {
        const baseText = `
        هذا نص تجريبي لاختبار الأداء. يحتوي على كلمات متنوعة ومختلفة.
        تهدف هذه الدراسة إلى تحليل وفهم الظاهرة المدروسة.
        في الختام، يمكن القول أن هذا البحث قد توصل إلى نتائج مهمة.
        من خلال هذا البحث، تم التوصل إلى عدة استنتاجات مهمة.
        يوصي الباحث بإجراء المزيد من الدراسات في هذا المجال.
        `;
        
        const words = baseText.split(/\s+/).filter(word => word.length > 0);
        const result = [];
        
        for (let i = 0; i < wordCount; i++) {
            result.push(words[i % words.length]);
        }
        
        return result.join(' ');
    }

    /**
     * إنتاج تقرير الأداء
     */
    generatePerformanceReport() {
        console.log('\n' + '=' .repeat(50));
        console.log('📊 تقرير الأداء النهائي');
        console.log('=' .repeat(50));
        
        // تحليل الأداء حسب حجم الملف
        if (this.performanceResults.length > 0) {
            console.log('\n📏 الأداء حسب حجم الملف:');
            this.performanceResults.forEach(result => {
                console.log(`   ${result.fileSize}: ${result.wordsPerSecond.toFixed(1)} كلمة/ثانية`);
            });
            
            const avgSpeed = this.performanceResults.reduce((sum, r) => sum + r.wordsPerSecond, 0) / this.performanceResults.length;
            console.log(`   📊 متوسط السرعة: ${avgSpeed.toFixed(1)} كلمة/ثانية`);
        }
        
        // تحليل استخدام الذاكرة
        if (this.memoryUsage.length > 0) {
            const maxMemory = Math.max(...this.memoryUsage.map(m => m.heapUsed));
            const avgMemory = this.memoryUsage.reduce((sum, m) => sum + m.heapUsed, 0) / this.memoryUsage.length;
            
            console.log('\n💾 استخدام الذاكرة:');
            console.log(`   🔝 الحد الأقصى: ${(maxMemory / 1024 / 1024).toFixed(2)} MB`);
            console.log(`   📊 المتوسط: ${(avgMemory / 1024 / 1024).toFixed(2)} MB`);
        }
        
        // تحليل اختبار الضغط
        if (this.stressTestResults.length > 0) {
            const times = this.stressTestResults.map(r => r.processingTime);
            const minTime = Math.min(...times);
            const maxTime = Math.max(...times);
            const avgTime = times.reduce((sum, t) => sum + t, 0) / times.length;
            
            console.log('\n🔥 نتائج اختبار الضغط:');
            console.log(`   ⚡ أسرع وقت: ${minTime}ms`);
            console.log(`   🐌 أبطأ وقت: ${maxTime}ms`);
            console.log(`   📊 متوسط الوقت: ${avgTime.toFixed(0)}ms`);
            console.log(`   📈 التباين: ${((maxTime - minTime) / avgTime * 100).toFixed(1)}%`);
        }
        
        // تقييم الأداء العام
        const overallGrade = this.calculatePerformanceGrade();
        console.log(`\n🏆 تقييم الأداء العام: ${overallGrade}`);
        
        // حفظ تقرير الأداء
        this.savePerformanceReport();
    }

    /**
     * حساب تقييم الأداء
     */
    calculatePerformanceGrade() {
        let score = 100;
        
        // تقييم السرعة
        if (this.performanceResults.length > 0) {
            const avgSpeed = this.performanceResults.reduce((sum, r) => sum + r.wordsPerSecond, 0) / this.performanceResults.length;
            if (avgSpeed < 10) score -= 20;
            else if (avgSpeed < 20) score -= 10;
        }
        
        // تقييم استخدام الذاكرة
        if (this.memoryUsage.length > 0) {
            const maxMemory = Math.max(...this.memoryUsage.map(m => m.heapUsed)) / 1024 / 1024;
            if (maxMemory > 500) score -= 20;
            else if (maxMemory > 200) score -= 10;
        }
        
        // تقييم الاستقرار
        if (this.stressTestResults.length > 0) {
            const times = this.stressTestResults.map(r => r.processingTime);
            const avgTime = times.reduce((sum, t) => sum + t, 0) / times.length;
            const variance = times.reduce((sum, t) => sum + Math.pow(t - avgTime, 2), 0) / times.length;
            const stdDev = Math.sqrt(variance);
            const coefficient = stdDev / avgTime;
            
            if (coefficient > 0.3) score -= 15;
            else if (coefficient > 0.2) score -= 10;
        }
        
        if (score >= 90) return 'ممتاز';
        if (score >= 80) return 'جيد جداً';
        if (score >= 70) return 'جيد';
        if (score >= 60) return 'مقبول';
        return 'يحتاج تحسين';
    }

    /**
     * حفظ تقرير الأداء
     */
    savePerformanceReport() {
        const reportData = {
            timestamp: new Date().toISOString(),
            performanceResults: this.performanceResults,
            memoryUsage: this.memoryUsage,
            stressTestResults: this.stressTestResults,
            summary: {
                avgSpeed: this.performanceResults.length > 0 ? 
                    this.performanceResults.reduce((sum, r) => sum + r.wordsPerSecond, 0) / this.performanceResults.length : 0,
                maxMemory: this.memoryUsage.length > 0 ? 
                    Math.max(...this.memoryUsage.map(m => m.heapUsed)) / 1024 / 1024 : 0,
                overallGrade: this.calculatePerformanceGrade()
            }
        };
        
        const reportPath = path.join(__dirname, 'performance_report.json');
        fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2), 'utf8');
        console.log(`\n💾 تم حفظ تقرير الأداء: ${reportPath}`);
    }
}

// تشغيل اختبارات الأداء
async function main() {
    try {
        const performanceTest = new PerformanceTestSuite();
        await performanceTest.runPerformanceTests();
    } catch (error) {
        console.error('❌ خطأ في اختبارات الأداء:', error);
        process.exit(1);
    }
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    main();
}

module.exports = PerformanceTestSuite;
