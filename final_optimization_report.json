{"timestamp": "2025-07-05T00:22:38.197Z", "test_type": "final_optimization_test", "description": "اختبار التحسينات النهائية الشاملة للوصول لدقة 95%+", "final_settings": {"thresholds": {"high": 60, "medium": 45, "low": 30}, "dynamic_thresholds": "35-65%", "weights": {"jaccard": 40, "cosine": 40, "levenshtein": 15, "semantic": 5}}, "results": {"avgAccuracy": 51.666666666666664, "successRate": 33.33333333333333, "avgTime": 465, "avgThreshold": 44.333333333333336, "targetsAchieved": 1, "finalGrade": "C (يحتاج تطوير) - تحسن محدود", "improvement": {"accuracy": -0.3333333333333357, "successRate": 0.03333333333333144}}, "detailed_results": [{"file": "test_calibrated_20_percent.txt", "expected": 20, "actual": 66, "accuracy": 0, "processingTime": 322, "threshold": 0.45, "suspiciousCount": 15, "passed": false}, {"file": "test_calibrated_60_percent.txt", "expected": 60, "actual": 69, "accuracy": 85, "processingTime": 500, "threshold": 0.45, "suspiciousCount": 15, "passed": true}, {"file": "test_calibrated_90_percent.txt", "expected": 90, "actual": 63, "accuracy": 70, "processingTime": 573, "threshold": 0.43, "suspiciousCount": 15, "passed": false}]}