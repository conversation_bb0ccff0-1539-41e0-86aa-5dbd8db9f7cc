<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام كشف الاستلال ومحول PDF إلى DOCX المتقدم</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
            direction: rtl;
        }

        .app-container {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            background: white;
            margin: 10px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        /* شريط العنوان */
        .app-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 20px;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .app-logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            font-size: 2em;
        }

        .app-title {
            font-size: 1.5em;
            font-weight: 600;
        }

        .app-version {
            font-size: 0.9em;
            opacity: 0.9;
        }

        /* المحتوى الرئيسي */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        /* علامات التبويب */
        .tabs-container {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .tabs {
            display: flex;
            padding: 0 20px;
        }

        .tab-button {
            background: none;
            border: none;
            padding: 15px 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 1em;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .tab-button.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: white;
        }

        .tab-icon {
            font-size: 1.2em;
        }

        /* محتوى التبويب */
        .tab-content {
            flex: 1;
            padding: 30px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        /* رؤوس الأقسام */
        .section-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .section-header h2 {
            font-size: 2em;
            color: #333;
            margin-bottom: 10px;
        }

        .section-header p {
            color: #666;
            font-size: 1.1em;
        }

        /* منطقة الرفع */
        .upload-area {
            border: 2px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            margin-bottom: 30px;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f0f0;
        }

        .upload-area.dragover {
            border-color: #28a745;
            background: #e8f5e8;
        }

        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .upload-content h3 {
            color: #333;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .upload-content p {
            color: #666;
            margin-bottom: 20px;
        }

        .supported-formats {
            margin: 20px 0;
        }

        .format-tag {
            background: #667eea;
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            margin: 0 5px;
        }

        /* الأزرار */
        .upload-button, .action-button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .upload-button:hover, .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .action-button.secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
        }

        .action-button.primary {
            background: linear-gradient(45deg, #28a745, #20c997);
        }

        /* معلومات الملف */
        .file-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .file-info h4 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background: white;
            border-radius: 8px;
        }

        .info-label {
            font-weight: 600;
            color: #333;
        }

        .info-value {
            color: #666;
        }

        /* شريط التقدم */
        .progress-section {
            margin: 20px 0;
            text-align: center;
        }

        .progress-bar {
            background: #e0e0e0;
            border-radius: 10px;
            height: 20px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            background: linear-gradient(45deg, #28a745, #20c997);
            height: 100%;
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            color: #666;
            font-size: 0.9em;
        }

        /* النتائج */
        .results-section {
            margin: 30px 0;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
        }

        .plagiarism-score {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .score-value {
            font-size: 2em;
            font-weight: bold;
            color: #dc3545;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .result-card {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .result-card h4 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        /* مؤشر الخطر */
        .risk-indicator {
            text-align: center;
        }

        .risk-level {
            font-size: 1.5em;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .risk-level.low { color: #28a745; }
        .risk-level.medium { color: #ffc107; }
        .risk-level.high { color: #dc3545; }

        /* الأجزاء المشبوهة */
        .suspicious-count {
            text-align: center;
            font-size: 1.2em;
        }

        .suspicious-count span:first-child {
            font-size: 2em;
            font-weight: bold;
            color: #dc3545;
            display: block;
        }

        /* شريط الحالة */
        .status-bar {
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            padding: 10px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            color: #666;
        }

        .status-right {
            display: flex;
            gap: 20px;
        }

        /* النوافذ المنبثقة */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            border-radius: 10px;
            min-width: 400px;
            max-width: 600px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5em;
            cursor: pointer;
            color: #666;
        }

        .modal-body {
            padding: 20px;
        }

        .modal-footer {
            padding: 20px;
            border-top: 1px solid #e9ecef;
            text-align: left;
        }

        /* تصميم متجاوب */
        @media (max-width: 768px) {
            .app-container {
                margin: 5px;
            }

            .header-content {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .app-title {
                font-size: 1.2em;
            }

            .tabs {
                flex-wrap: wrap;
                padding: 0 10px;
            }

            .tab-button {
                padding: 10px 15px;
                font-size: 0.9em;
            }

            .tab-content {
                padding: 20px 15px;
            }

            .results-grid {
                grid-template-columns: 1fr;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }
        }

        /* إضافات للمعالجة المتعددة */
        .batch-options {
            margin-bottom: 30px;
        }

        .batch-type-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            justify-content: center;
        }

        .batch-type-btn {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            padding: 15px 25px;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .batch-type-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .batch-settings {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }

        .settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .setting-item {
            margin-bottom: 15px;
        }

        .setting-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .path-input {
            display: flex;
            gap: 10px;
        }

        .path-input input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .browse-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 5px;
            cursor: pointer;
        }

        /* إضافات للإعدادات */
        .settings-sections {
            max-width: 800px;
            margin: 0 auto;
        }

        .settings-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .settings-section h3 {
            color: #333;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .setting-item input[type="checkbox"] {
            margin-left: 10px;
        }

        .setting-item select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin-top: 5px;
        }

        .system-info {
            background: white;
            border-radius: 8px;
            padding: 15px;
            border: 1px solid #e9ecef;
        }

        .settings-actions {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- شريط العنوان -->
        <header class="app-header">
            <div class="header-content">
                <div class="app-logo">
                    <span class="logo-icon">🔍</span>
                    <h1 class="app-title">نظام كشف الاستلال ومحول PDF إلى DOCX المتقدم</h1>
                </div>
                <div class="app-version">
                    <span id="app-version">الإصدار 1.0.0</span>
                </div>
            </div>
        </header>

        <!-- المحتوى الرئيسي -->
        <main class="main-content">
            <!-- علامات التبويب -->
            <div class="tabs-container">
                <div class="tabs">
                    <button class="tab-button active" data-tab="plagiarism">
                        <span class="tab-icon">🔍</span>
                        كشف الاستلال
                    </button>
                    <button class="tab-button" data-tab="conversion">
                        <span class="tab-icon">🔄</span>
                        تحويل PDF إلى DOCX
                    </button>
                    <button class="tab-button" data-tab="batch">
                        <span class="tab-icon">📁</span>
                        معالجة متعددة
                    </button>
                    <button class="tab-button" data-tab="settings">
                        <span class="tab-icon">⚙️</span>
                        الإعدادات
                    </button>
                </div>
            </div>

            <!-- محتوى علامات التبويب -->
            <div class="tab-content">
                <!-- تبويب كشف الاستلال -->
                <div id="plagiarism-tab" class="tab-pane active">
                    <div class="section-header">
                        <h2>🔍 كشف الاستلال المتقدم</h2>
                        <p>فحص دقيق للاستلال بدعم شامل للعربية والإنجليزية</p>
                    </div>

                    <div class="upload-area" id="plagiarism-upload">
                        <div class="upload-content">
                            <div class="upload-icon">📄</div>
                            <h3>اختر ملفاً للفحص</h3>
                            <p>اسحب وأفلت الملف هنا أو انقر لاختيار الملف</p>
                            <div class="supported-formats">
                                <span>الصيغ المدعومة:</span>
                                <span class="format-tag">TXT</span>
                                <span class="format-tag">PDF</span>
                                <span class="format-tag">DOC</span>
                                <span class="format-tag">DOCX</span>
                            </div>
                            <button class="upload-button" id="select-file-btn">
                                📁 اختيار ملف
                            </button>
                        </div>
                    </div>

                    <div class="file-info" id="file-info" style="display: none;">
                        <h4>📋 معلومات الملف:</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">الاسم:</span>
                                <span class="info-value" id="file-name"></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">الحجم:</span>
                                <span class="info-value" id="file-size"></span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">النوع:</span>
                                <span class="info-value" id="file-type"></span>
                            </div>
                        </div>
                        <button class="action-button primary" id="check-plagiarism-btn">
                            🔍 فحص الاستلال
                        </button>
                    </div>

                    <div class="progress-section" id="progress-section" style="display: none;">
                        <h4>⏳ جاري الفحص...</h4>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-fill"></div>
                        </div>
                        <p class="progress-text" id="progress-text">بدء الفحص...</p>
                    </div>

                    <div class="results-section" id="results-section" style="display: none;">
                        <div class="results-header">
                            <h3>📊 نتائج الفحص</h3>
                            <div class="plagiarism-score">
                                <span class="score-label">نسبة الاستلال:</span>
                                <span class="score-value" id="plagiarism-percentage">0%</span>
                            </div>
                        </div>

                        <div class="results-grid">
                            <div class="result-card">
                                <h4>🌐 معلومات اللغة</h4>
                                <div class="result-item">
                                    <span>اللغة المكتشفة:</span>
                                    <span id="detected-language"></span>
                                </div>
                                <div class="result-item">
                                    <span>عدد الكلمات:</span>
                                    <span id="word-count"></span>
                                </div>
                            </div>

                            <div class="result-card">
                                <h4>📈 مستوى الخطر</h4>
                                <div class="risk-indicator">
                                    <div class="risk-level" id="risk-level"></div>
                                    <div class="risk-description" id="risk-description"></div>
                                </div>
                            </div>

                            <div class="result-card">
                                <h4>🔍 الأجزاء المشبوهة</h4>
                                <div class="suspicious-count">
                                    <span id="suspicious-segments-count">0</span>
                                    <span>جزء مشبوه</span>
                                </div>
                            </div>
                        </div>

                        <div class="suspicious-segments" id="suspicious-segments">
                            <!-- سيتم ملؤها ديناميكياً -->
                        </div>

                        <div class="results-actions">
                            <button class="action-button secondary" id="save-report-btn">
                                💾 حفظ التقرير
                            </button>
                            <button class="action-button primary" id="check-another-btn">
                                🔍 فحص ملف آخر
                            </button>
                        </div>
                    </div>
                </div>

                <!-- تبويب تحويل PDF -->
                <div id="conversion-tab" class="tab-pane">
                    <div class="section-header">
                        <h2>🔄 تحويل PDF إلى DOCX</h2>
                        <p>تحويل متقدم مع الحل الجذري للنصوص العربية</p>
                    </div>

                    <div class="upload-area" id="pdf-upload">
                        <div class="upload-content">
                            <div class="upload-icon">📄</div>
                            <h3>اختر ملف PDF للتحويل</h3>
                            <p>اسحب وأفلت ملف PDF هنا أو انقر لاختيار الملف</p>
                            <div class="supported-formats">
                                <span>الصيغة المدعومة:</span>
                                <span class="format-tag">PDF</span>
                            </div>
                            <button class="upload-button" id="select-pdf-btn">
                                📁 اختيار ملف PDF
                            </button>
                        </div>
                    </div>

                    <div class="conversion-options" id="conversion-options" style="display: none;">
                        <h4>⚙️ خيارات التحويل:</h4>
                        <div class="options-grid">
                            <div class="option-item">
                                <label for="document-title">عنوان المستند:</label>
                                <input type="text" id="document-title" placeholder="عنوان المستند (اختياري)">
                            </div>
                            <div class="option-item">
                                <label for="output-path">مسار الحفظ:</label>
                                <div class="path-input">
                                    <input type="text" id="output-path" readonly placeholder="سيتم اختياره تلقائياً">
                                    <button class="browse-button" id="browse-output-btn">📁</button>
                                </div>
                            </div>
                        </div>
                        <button class="action-button primary" id="convert-pdf-btn">
                            🔄 بدء التحويل
                        </button>
                    </div>

                    <div class="conversion-progress" id="conversion-progress" style="display: none;">
                        <h4>⏳ جاري التحويل...</h4>
                        <div class="progress-bar">
                            <div class="progress-fill" id="conversion-progress-fill"></div>
                        </div>
                        <p class="progress-text" id="conversion-progress-text">بدء التحويل...</p>
                    </div>

                    <div class="conversion-results" id="conversion-results" style="display: none;">
                        <div class="success-message">
                            <h3>✅ تم التحويل بنجاح!</h3>
                        </div>

                        <div class="conversion-info">
                            <div class="info-card">
                                <h4>📊 معلومات التحويل</h4>
                                <div class="info-item">
                                    <span>اللغة المكتشفة:</span>
                                    <span id="conversion-language"></span>
                                </div>
                                <div class="info-item">
                                    <span>طول النص:</span>
                                    <span id="conversion-text-length"></span>
                                </div>
                                <div class="info-item">
                                    <span>وقت المعالجة:</span>
                                    <span id="conversion-time"></span>
                                </div>
                            </div>
                        </div>

                        <div class="conversion-actions">
                            <button class="action-button secondary" id="open-output-folder-btn">
                                📁 فتح مجلد الملف
                            </button>
                            <button class="action-button primary" id="convert-another-btn">
                                🔄 تحويل ملف آخر
                            </button>
                        </div>
                    </div>
                </div>

                <!-- تبويب المعالجة المتعددة -->
                <div id="batch-tab" class="tab-pane">
                    <div class="section-header">
                        <h2>📁 المعالجة المتعددة</h2>
                        <p>فحص أو تحويل عدة ملفات دفعة واحدة</p>
                    </div>

                    <div class="batch-options">
                        <div class="batch-type-selector">
                            <button class="batch-type-btn active" data-type="plagiarism">
                                🔍 فحص متعدد للاستلال
                            </button>
                            <button class="batch-type-btn" data-type="conversion">
                                🔄 تحويل متعدد PDF إلى DOCX
                            </button>
                        </div>

                        <div class="batch-settings" id="batch-plagiarism-settings">
                            <h4>⚙️ إعدادات الفحص المتعدد:</h4>
                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label for="input-folder">مجلد الملفات:</label>
                                    <div class="path-input">
                                        <input type="text" id="input-folder" readonly placeholder="اختر مجلد الملفات">
                                        <button class="browse-button" id="browse-input-folder-btn">📁</button>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <label for="report-output">ملف التقرير:</label>
                                    <div class="path-input">
                                        <input type="text" id="report-output" readonly placeholder="مسار حفظ التقرير">
                                        <button class="browse-button" id="browse-report-btn">📁</button>
                                    </div>
                                </div>
                            </div>
                            <button class="action-button primary" id="start-batch-check-btn">
                                🚀 بدء الفحص المتعدد
                            </button>
                        </div>

                        <div class="batch-settings" id="batch-conversion-settings" style="display: none;">
                            <h4>⚙️ إعدادات التحويل المتعدد:</h4>
                            <div class="settings-grid">
                                <div class="setting-item">
                                    <label for="pdf-input-folder">مجلد ملفات PDF:</label>
                                    <div class="path-input">
                                        <input type="text" id="pdf-input-folder" readonly placeholder="اختر مجلد ملفات PDF">
                                        <button class="browse-button" id="browse-pdf-folder-btn">📁</button>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <label for="docx-output-folder">مجلد الإخراج:</label>
                                    <div class="path-input">
                                        <input type="text" id="docx-output-folder" readonly placeholder="مجلد حفظ ملفات DOCX">
                                        <button class="browse-button" id="browse-docx-output-btn">📁</button>
                                    </div>
                                </div>
                            </div>
                            <button class="action-button primary" id="start-batch-convert-btn">
                                🚀 بدء التحويل المتعدد
                            </button>
                        </div>
                    </div>

                    <div class="batch-progress" id="batch-progress" style="display: none;">
                        <h4>⏳ جاري المعالجة...</h4>
                        <div class="progress-info">
                            <span id="batch-current">0</span> من <span id="batch-total">0</span> ملف
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="batch-progress-fill"></div>
                        </div>
                        <p class="progress-text" id="batch-progress-text">بدء المعالجة...</p>
                    </div>

                    <div class="batch-results" id="batch-results" style="display: none;">
                        <div class="results-summary">
                            <h3>📊 ملخص النتائج</h3>
                            <div class="summary-stats">
                                <div class="stat-item">
                                    <span class="stat-value" id="batch-total-files">0</span>
                                    <span class="stat-label">إجمالي الملفات</span>
                                </div>
                                <div class="stat-item success">
                                    <span class="stat-value" id="batch-successful">0</span>
                                    <span class="stat-label">نجح</span>
                                </div>
                                <div class="stat-item error">
                                    <span class="stat-value" id="batch-failed">0</span>
                                    <span class="stat-label">فشل</span>
                                </div>
                            </div>
                        </div>

                        <div class="batch-actions">
                            <button class="action-button secondary" id="view-batch-report-btn">
                                📋 عرض التقرير التفصيلي
                            </button>
                            <button class="action-button primary" id="start-new-batch-btn">
                                🔄 معالجة جديدة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- تبويب الإعدادات -->
                <div id="settings-tab" class="tab-pane">
                    <div class="section-header">
                        <h2>⚙️ الإعدادات</h2>
                        <p>تخصيص إعدادات النظام والتفضيلات</p>
                    </div>

                    <div class="settings-sections">
                        <div class="settings-section">
                            <h3>🔍 إعدادات كشف الاستلال</h3>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="use-multilingual" checked>
                                    استخدام المحلل متعدد اللغات
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="use-enhanced-extractor" checked>
                                    استخدام مستخرج النصوص المحسن
                                </label>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" id="ignore-gemini" checked>
                                    تجاهل Gemini API (استخدام التحليل المحلي)
                                </label>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h3>🔄 إعدادات التحويل</h3>
                            <div class="setting-item">
                                <label for="default-font-arabic">الخط الافتراضي للعربية:</label>
                                <select id="default-font-arabic">
                                    <option value="Arial Unicode MS">Arial Unicode MS</option>
                                    <option value="Tahoma">Tahoma</option>
                                    <option value="Times New Roman">Times New Roman</option>
                                </select>
                            </div>
                            <div class="setting-item">
                                <label for="default-font-english">الخط الافتراضي للإنجليزية:</label>
                                <select id="default-font-english">
                                    <option value="Calibri">Calibri</option>
                                    <option value="Arial">Arial</option>
                                    <option value="Times New Roman">Times New Roman</option>
                                </select>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h3>📊 معلومات النظام</h3>
                            <div class="system-info" id="system-info">
                                <!-- سيتم ملؤها ديناميكياً -->
                            </div>
                        </div>

                        <div class="settings-actions">
                            <button class="action-button secondary" id="reset-settings-btn">
                                🔄 إعادة تعيين الإعدادات
                            </button>
                            <button class="action-button primary" id="save-settings-btn">
                                💾 حفظ الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- شريط الحالة -->
        <footer class="status-bar">
            <div class="status-left">
                <span id="status-text">جاهز</span>
            </div>
            <div class="status-right">
                <span id="language-indicator">🌐 العربية/English</span>
                <span id="connection-status">🔒 محلي</span>
            </div>
        </footer>
    </div>

    <!-- نوافذ منبثقة -->
    <div class="modal" id="error-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>❌ خطأ</h3>
                <button class="modal-close" id="error-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p id="error-message"></p>
            </div>
            <div class="modal-footer">
                <button class="action-button primary" id="error-ok-btn">موافق</button>
            </div>
        </div>
    </div>

    <div class="modal" id="success-modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>✅ نجح</h3>
                <button class="modal-close" id="success-modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p id="success-message"></p>
            </div>
            <div class="modal-footer">
                <button class="action-button primary" id="success-ok-btn">موافق</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
