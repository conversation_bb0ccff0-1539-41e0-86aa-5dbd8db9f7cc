# 🧪 دليل الاختبارات الشاملة - Plagiarism Checker Pro

## 📋 نظرة عامة

تم تطوير مجموعة شاملة من الاختبارات لتقييم دقة وأداء تطبيق كشف الاستلال. تشمل الاختبارات:

- **اختبارات الدقة**: 7 ملفات بنسب استلال متدرجة (10% - 100%)
- **اختبارات الأداء**: قياس السرعة والذاكرة والاستقرار
- **اختبارات الضغط**: معالجة متتالية ومتزامنة
- **تقارير شاملة**: تحليل مفصل للنتائج

---

## 📁 هيكل ملفات الاختبار

```
📦 test-files/
├── 📄 test_10_percent.txt      # نص أصلي (10% استلال متوقع)
├── 📄 test_30_percent.txt      # استلال منخفض (30%)
├── 📄 test_50_percent.txt      # استلال متوسط (50%)
├── 📄 test_70_percent.txt      # استلال عالي (70%)
├── 📄 test_90_percent.txt      # استلال عالي جداً (90%)
├── 📄 test_100_percent.txt     # نسخ حرفي كامل (95%)
├── 📄 test_mixed.txt           # مزيج من الأنواع (60%)
└── 📄 expected_results.json    # النتائج المتوقعة

📦 سكريبتات الاختبار/
├── 🔧 comprehensive_test.js    # اختبارات الدقة والوظائف
├── ⚡ performance_test.js      # اختبارات الأداء والضغط
├── 🚀 run_all_tests.js         # سكريبت شامل لجميع الاختبارات
└── 📊 test_improvements.js     # اختبار التحسينات (مقترح)
```

---

## 🚀 كيفية تشغيل الاختبارات

### 1️⃣ الاختبارات الشاملة (موصى به)

```bash
# تشغيل جميع الاختبارات مع تقرير شامل
node run_all_tests.js
```

**المخرجات**:
- تقرير مباشر في الكونسول
- `test_report.json` - تقرير اختبارات الدقة
- `performance_report.json` - تقرير اختبارات الأداء
- `master_report.json` - التقرير الشامل النهائي

### 2️⃣ اختبارات الدقة فقط

```bash
# اختبار دقة كشف الاستلال
node comprehensive_test.js
```

### 3️⃣ اختبارات الأداء فقط

```bash
# اختبار السرعة والذاكرة والضغط
node performance_test.js
```

### 4️⃣ اختبار ملف واحد

```bash
# اختبار سريع لملف واحد
node test.js
```

---

## 📊 فهم النتائج

### 🎯 مؤشرات الدقة

| المؤشر | الوصف | الهدف |
|---------|--------|--------|
| **معدل النجاح** | نسبة الاختبارات الناجحة | ≥ 80% |
| **متوسط الدقة** | متوسط دقة جميع الاختبارات | ≥ 85% |
| **دقة النسخ الحرفي** | دقة كشف النسخ المباشر | ≥ 95% |
| **دقة الاستلال العالي** | دقة كشف الاستلال 70%+ | ≥ 80% |

### ⚡ مؤشرات الأداء

| المؤشر | الوصف | الهدف |
|---------|--------|--------|
| **سرعة المعالجة** | كلمة/ثانية | ≥ 20,000 |
| **استقرار الأداء** | ثبات الأوقات | ≥ 90% |
| **استخدام الذاكرة** | الحد الأقصى للذاكرة | ≤ 200 MB |
| **دعم التزامن** | معالجة متوازية | ✅ مدعوم |

### 🏆 التقييمات

| الدرجة | النطاق | الوصف |
|---------|---------|--------|
| **A+** | 90-100% | ممتاز - جاهز للإنتاج |
| **A** | 80-89% | جيد جداً - تحسينات طفيفة |
| **B** | 70-79% | جيد - يحتاج تحسينات |
| **C** | 60-69% | مقبول - تحسينات كبيرة |
| **D** | <60% | يحتاج عمل - غير جاهز |

---

## 🔍 تحليل أنواع الاستلال

### 📝 أنواع الملفات المختبرة:

1. **النص الأصلي** (`test_10_percent.txt`):
   - محتوى أصلي مع عبارات أكاديمية قليلة
   - **التحدي**: عدم الإفراط في التحذير من المحتوى الأصلي

2. **الاستلال المنخفض** (`test_30_percent.txt`):
   - بعض العبارات الشائعة مع محتوى أصلي
   - **التحدي**: التمييز بين الشائع والمستل

3. **الاستلال المتوسط** (`test_50_percent.txt`):
   - مزيج من العبارات المستلة وإعادة الصياغة
   - **التحدي**: كشف إعادة الصياغة البسيطة

4. **الاستلال العالي** (`test_70_percent.txt`):
   - نسخ كبير للعبارات المرجعية
   - **التحدي**: كشف الاستلال المقنع

5. **الاستلال العالي جداً** (`test_90_percent.txt`):
   - تكرار مكثف للعبارات الشائعة
   - **التحدي**: التعامل مع النصوص شديدة التشابه

6. **النسخ الحرفي** (`test_100_percent.txt`):
   - نسخ مباشر للعبارات المرجعية
   - **التحدي**: الكشف المؤكد للنسخ المباشر

7. **المحتوى المختلط** (`test_mixed.txt`):
   - مزيج من جميع الأنواع السابقة
   - **التحدي**: التعامل مع التعقيد الواقعي

---

## 📈 تتبع التحسن

### 📊 النتائج الحالية (خط الأساس):

```
📈 إحصائيات عامة:
   إجمالي الاختبارات: 7
   اختبارات ناجحة: 2 (28.6%)
   متوسط الدقة: 61.3%

🎯 الدقة حسب نوع الاستلال:
   النسخ الحرفي: 94.7% ✅
   الاستلال المنخفض: 83.3% ✅
   الاستلال المتوسط: 64.0% ⚠️
   الاستلال العالي: 47.1% ❌
   الاستلال العالي جداً: 56.7% ❌
   المحتوى المختلط: 53.3% ❌
   المحتوى الأصلي: 30.0% ❌

⚡ الأداء:
   سرعة المعالجة: 31,627 كلمة/ثانية ✅
   استقرار الأداء: 91.5% ✅
```

### 🎯 الأهداف المطلوبة:

```
📈 الأهداف:
   معدل النجاح: ≥ 80% (حالياً: 28.6%)
   متوسط الدقة: ≥ 85% (حالياً: 61.3%)

🎯 دقة مطلوبة لكل نوع:
   النسخ الحرفي: ≥ 95% ✅
   الاستلال العالي: ≥ 80% ❌
   الاستلال المتوسط: ≥ 75% ❌
   المحتوى الأصلي: ≥ 80% ❌
```

---

## 🔧 استكشاف الأخطاء

### ❌ مشاكل شائعة:

1. **"خطأ في تحميل النتائج المتوقعة"**:
   ```bash
   # تأكد من وجود الملف
   ls test-files/expected_results.json
   ```

2. **"الملف غير موجود"**:
   ```bash
   # تأكد من وجود ملفات الاختبار
   ls test-files/
   ```

3. **"فشل في استخراج النص"**:
   ```bash
   # تحقق من تثبيت المكتبات
   npm install
   ```

4. **"بطء في المعالجة"**:
   ```bash
   # أغلق التطبيقات الأخرى
   # تأكد من توفر ذاكرة كافية
   ```

### 🔍 تشخيص المشاكل:

```bash
# تشغيل اختبار بسيط
node test.js

# فحص حالة النظام
node -e "console.log(process.memoryUsage())"

# اختبار ملف واحد فقط
node -e "
const checker = require('./src/modules/plagiarismChecker');
new checker().checkFile('test-files/test_30_percent.txt')
  .then(r => console.log('نجح:', r.plagiarismPercentage))
  .catch(e => console.error('فشل:', e.message));
"
```

---

## 📚 مراجع إضافية

### 📖 الملفات ذات الصلة:
- `COMPREHENSIVE_TEST_REPORT.md` - تقرير مفصل للنتائج
- `IMPROVEMENT_GUIDE.md` - دليل تحسين التطبيق
- `README.md` - دليل التطبيق الرئيسي

### 🔗 روابط مفيدة:
- [دليل استخدام التطبيق](README.md)
- [دليل البناء والتغليف](build.md)
- [تقرير الاختبارات الشامل](COMPREHENSIVE_TEST_REPORT.md)

---

## 💡 نصائح للمطورين

### 🧪 إضافة اختبارات جديدة:

1. **إنشاء ملف اختبار جديد**:
   ```bash
   echo "نص الاختبار الجديد" > test-files/test_custom.txt
   ```

2. **تحديث النتائج المتوقعة**:
   ```json
   // في test-files/expected_results.json
   {
     "filename": "test_custom.txt",
     "expected_plagiarism": 40,
     "tolerance": 10,
     "description": "وصف الاختبار"
   }
   ```

3. **تشغيل الاختبار**:
   ```bash
   node comprehensive_test.js
   ```

### 📊 تخصيص التقارير:

```javascript
// تعديل comprehensive_test.js لإضافة مؤشرات جديدة
const customMetrics = {
    processingSpeed: result.performance.wordsPerSecond,
    memoryEfficiency: result.performance.memoryUsed,
    accuracyGrade: this.calculateAccuracyGrade(result.accuracy)
};
```

---

**🎯 الهدف**: ضمان جودة وموثوقية تطبيق كشف الاستلال قبل الإطلاق النهائي
