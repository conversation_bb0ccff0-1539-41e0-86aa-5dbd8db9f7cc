const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار النظام على البحوث الحقيقية
 */
async function testRealResearch() {
    console.log('🔬 اختبار النظام على البحوث الحقيقية');
    console.log('=' .repeat(60));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // البحوث الحقيقية مع نسب الاستلال المتوقعة
    const realResearch = [
        { 
            file: 'research_low_plagiarism_15percent.txt', 
            expected: 15, 
            description: 'بحث أصلي بنسبة استلال منخفضة' 
        },
        { 
            file: 'research_medium_plagiarism_50percent.txt', 
            expected: 50, 
            description: 'بحث بنسبة استلال متوسطة' 
        },
        { 
            file: 'research_high_plagiarism_85percent.txt', 
            expected: 85, 
            description: 'بحث بنسبة استلال عالية' 
        }
    ];
    
    for (const research of realResearch) {
        console.log(`\n📄 اختبار: ${research.file}`);
        console.log(`📋 ${research.description} - متوقع: ${research.expected}%`);
        
        const filePath = path.join(__dirname, 'real-research-tests', research.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - research.expected);
            const accuracy = Math.max(0, 100 - (difference / research.expected) * 100);
            const passed = accuracy >= 95; // معيار الدقة 95%
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   🎯 المتوقع: ${research.expected}%`);
            console.log(`   📊 الفرق: ${difference.toFixed(1)}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🚨 الخطر: ${result.riskLevel.label}`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments ? result.suspiciousSegments.length : 0}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (معيار الدقة 95%)`);
            
            // عرض أمثلة على الأجزاء المشبوهة
            if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
                console.log(`   📝 أمثلة على الأجزاء المكتشفة:`);
                result.suspiciousSegments.slice(0, 2).forEach((segment, index) => {
                    console.log(`      ${index + 1}. "${segment.text.substring(0, 50)}..." (${(segment.similarity * 100).toFixed(1)}%)`);
                });
            }
            
            results.push({
                file: research.file,
                expected: research.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                suspiciousCount: result.suspiciousSegments ? result.suspiciousSegments.length : 0,
                passed: passed,
                withinTimeLimit: processingTime < 30000 // 30 ثانية
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: research.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج
    console.log('\n' + '=' .repeat(60));
    console.log('📊 تحليل النتائج على البحوث الحقيقية');
    console.log('=' .repeat(60));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    const withinTimeLimit = validResults.filter(r => r.withinTimeLimit);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgSuspiciousCount = validResults.reduce((sum, r) => sum + r.suspiciousCount, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        const timeSuccessRate = (withinTimeLimit.length / validResults.length) * 100;
        
        console.log(`🎯 النتائج على البحوث الحقيقية:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (الهدف: 95%+)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (الهدف: 100%)`);
        console.log(`   متوسط وقت المعالجة: ${(avgTime/1000).toFixed(1)}s (الهدف: <30s)`);
        console.log(`   معدل الأداء السريع: ${timeSuccessRate.toFixed(1)}%`);
        console.log(`   متوسط الأجزاء المشبوهة: ${avgSuspiciousCount.toFixed(1)} جزء`);
        
        // تقييم تحقيق الأهداف
        console.log(`\n🎯 تقييم تحقيق الأهداف:`);
        console.log(`   دقة 95%+: ${avgAccuracy >= 95 ? '✅' : '❌'} (${avgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 100%: ${successRate >= 100 ? '✅' : '❌'} (${successRate.toFixed(1)}%)`);
        console.log(`   أداء < 30s: ${timeSuccessRate >= 100 ? '✅' : '❌'} (${timeSuccessRate.toFixed(1)}%)`);
        
        const targetsAchieved = [
            avgAccuracy >= 95,
            successRate >= 100,
            timeSuccessRate >= 100
        ].filter(Boolean).length;
        
        console.log(`   الأهداف المحققة: ${targetsAchieved}/3`);
        
        // تفاصيل كل بحث
        console.log(`\n📋 تفاصيل النتائج لكل بحث:`);
        validResults.forEach(result => {
            const grade = result.accuracy >= 95 ? '🎯 ممتاز' : 
                         result.accuracy >= 85 ? '✅ جيد جداً' : 
                         result.accuracy >= 70 ? '📈 جيد' : '⚠️ يحتاج عمل';
            const timeGrade = result.withinTimeLimit ? '⚡ سريع' : '🐌 بطيء';
            console.log(`   ${result.file}:`);
            console.log(`      النتيجة: ${result.actual}% (متوقع: ${result.expected}%)`);
            console.log(`      الدقة: ${result.accuracy.toFixed(1)}% ${grade}`);
            console.log(`      الوقت: ${(result.processingTime/1000).toFixed(1)}s ${timeGrade}`);
            console.log(`      الأجزاء المشبوهة: ${result.suspiciousCount}`);
        });
        
        // التقييم النهائي
        let finalAssessment;
        if (targetsAchieved === 3) {
            finalAssessment = '🎉 نجح تماماً! تم تحقيق جميع الأهداف';
        } else if (avgAccuracy >= 95) {
            finalAssessment = '🎯 نجح في الدقة! الهدف الأساسي محقق';
        } else if (avgAccuracy >= 85 && successRate >= 66) {
            finalAssessment = '✅ نجح بشكل جيد! قريب من الأهداف';
        } else if (avgAccuracy >= 70) {
            finalAssessment = '📈 نجح جزئياً! يحتاج تحسينات';
        } else {
            finalAssessment = '⚠️ لم ينجح! يحتاج تطوير كبير';
        }
        
        console.log(`\n🏆 التقييم النهائي: ${finalAssessment}`);
        
        // حفظ التقرير
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'real_research_test',
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                timeSuccessRate: timeSuccessRate,
                avgSuspiciousCount: avgSuspiciousCount,
                targetsAchieved: targetsAchieved,
                finalAssessment: finalAssessment
            },
            detailed_results: results,
            needs_improvement: targetsAchieved < 3
        };
        
        fs.writeFileSync('real_research_test_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير: real_research_test_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime: avgTime / 1000, // بالثواني
            targetsAchieved,
            needsImprovement: targetsAchieved < 3,
            finalAssessment
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testRealResearch();
        
        if (results) {
            console.log('\n🎯 الخلاصة النهائية:');
            console.log(`   الدقة: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(1)}s`);
            console.log(`   الأهداف المحققة: ${results.targetsAchieved}/3`);
            console.log(`   يحتاج تحسين: ${results.needsImprovement ? 'نعم' : 'لا'}`);
            console.log(`   التقييم: ${results.finalAssessment}`);
            
            if (results.needsImprovement) {
                console.log('\n🔧 الخطوة التالية: تطبيق التحسينات المطلوبة');
            } else {
                console.log('\n🎉 النظام جاهز! تم تحقيق جميع الأهداف');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testRealResearch };
