const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار التحسينات الجذرية للخوارزميات
 */
async function testAlgorithmOverhaul() {
    console.log('🔬 اختبار التحسينات الجذرية للخوارزميات');
    console.log('=' .repeat(60));
    console.log('🚀 التحسينات المطبقة:');
    console.log('   ✅ Jaccard محسن: كلمات + عبارات 2-3 + عبارات 4-6');
    console.log('   ✅ Cosine محسن: كلمات + bigrams + trigrams');
    console.log('   ✅ مكافأة التطابق محسنة: عبارات طويلة + substring matching');
    console.log('   ✅ التركيز على العبارات الطويلة للكشف عن الاستلال');
    console.log('=' .repeat(60));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار
    const testFiles = [
        { file: 'test_large_10_percent.txt', expected: 15, description: 'نص أصلي' },
        { file: 'test_large_50_percent.txt', expected: 55, description: 'نص مختلط' },
        { file: 'test_large_90_percent.txt', expected: 85, description: 'نص مستل' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 70; // معيار مؤقت
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   ⚙️ العتبة: ${(result.analysis.adaptiveThreshold * 100).toFixed(1)}%`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            // عرض تفاصيل التشابه
            if (result.analysis && result.analysis.similarities) {
                console.log(`   🔬 تفاصيل التشابه:`);
                console.log(`      Jaccard: ${(result.analysis.similarities.jaccard * 100).toFixed(1)}%`);
                console.log(`      Cosine: ${(result.analysis.similarities.cosine * 100).toFixed(1)}%`);
                console.log(`      Levenshtein: ${(result.analysis.similarities.levenshtein * 100).toFixed(1)}%`);
                console.log(`      Semantic: ${(result.analysis.similarities.semantic * 100).toFixed(1)}%`);
                console.log(`      Exact Match Bonus: ${(result.analysis.similarities.exactMatch * 100).toFixed(1)}%`);
            }
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                threshold: result.analysis.adaptiveThreshold,
                suspiciousCount: result.suspiciousSegments.length,
                similarities: result.analysis.similarities,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج
    console.log('\n' + '=' .repeat(60));
    console.log('📊 تحليل تأثير التحسينات الجذرية');
    console.log('=' .repeat(60));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgThreshold = validResults.reduce((sum, r) => sum + r.threshold, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`📈 النتائج بعد التحسينات الجذرية:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (كان 42.4%)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (كان 33.3%)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
        console.log(`   متوسط العتبة: ${(avgThreshold * 100).toFixed(1)}%`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 42.4;
        const successImprovement = successRate - 33.3;
        
        console.log(`\n📊 التحسن المحقق:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تحليل متوسط التشابه
        if (validResults[0] && validResults[0].similarities) {
            console.log(`\n🔬 متوسط قيم التشابه:`);
            const avgSimilarities = {
                jaccard: validResults.reduce((sum, r) => sum + (r.similarities?.jaccard || 0), 0) / validResults.length,
                cosine: validResults.reduce((sum, r) => sum + (r.similarities?.cosine || 0), 0) / validResults.length,
                levenshtein: validResults.reduce((sum, r) => sum + (r.similarities?.levenshtein || 0), 0) / validResults.length,
                semantic: validResults.reduce((sum, r) => sum + (r.similarities?.semantic || 0), 0) / validResults.length,
                exactMatch: validResults.reduce((sum, r) => sum + (r.similarities?.exactMatch || 0), 0) / validResults.length
            };
            
            console.log(`   Jaccard: ${(avgSimilarities.jaccard * 100).toFixed(1)}%`);
            console.log(`   Cosine: ${(avgSimilarities.cosine * 100).toFixed(1)}%`);
            console.log(`   Levenshtein: ${(avgSimilarities.levenshtein * 100).toFixed(1)}%`);
            console.log(`   Semantic: ${(avgSimilarities.semantic * 100).toFixed(1)}%`);
            console.log(`   Exact Match Bonus: ${(avgSimilarities.exactMatch * 100).toFixed(1)}%`);
        }
        
        // تقييم التحسن
        if (avgAccuracy >= 85 && successRate >= 80) {
            console.log(`\n🎉 تحسن ممتاز! الخوارزميات الجديدة فعالة جداً`);
        } else if (avgAccuracy >= 70 && successRate >= 60) {
            console.log(`\n✅ تحسن كبير! الخوارزميات في الاتجاه الصحيح`);
        } else if (avgAccuracy > 42.4) {
            console.log(`\n📈 تحسن ملحوظ! يحتاج مزيد من التحسين`);
        } else {
            console.log(`\n⚠️ تحسن محدود! قد نحتاج مراجعة أعمق`);
        }
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج:`);
        validResults.forEach(result => {
            const improvement = result.accuracy > 70 ? '✅ ممتاز' : result.accuracy > 50 ? '📈 تحسن' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${improvement}`);
        });
        
        // تحليل الأداء
        console.log(`\n⚡ تحليل الأداء:`);
        console.log(`   متوسط الوقت: ${avgTime.toFixed(0)}ms`);
        console.log(`   الهدف: < 1000ms ${avgTime < 1000 ? '✅' : '❌'}`);
        
        // حفظ النتائج
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'algorithm_overhaul_test',
            improvements: [
                'Jaccard محسن للعبارات متعددة الأطوال',
                'Cosine محسن مع N-grams',
                'مكافأة التطابق محسنة للعبارات الطويلة',
                'التركيز على كشف العبارات المستلة'
            ],
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgThreshold: avgThreshold * 100,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results
        };
        
        fs.writeFileSync('algorithm_overhaul_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير: algorithm_overhaul_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            improvement: accuracyImprovement
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testAlgorithmOverhaul();
        
        if (results) {
            console.log('\n🎯 الخلاصة:');
            console.log(`   الدقة الجديدة: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(0)}ms`);
            
            if (results.avgAccuracy >= 85) {
                console.log('🎉 هدف الدقة 85%+ تحقق! ننتقل للخطوة التالية');
            } else if (results.improvement > 20) {
                console.log('📈 تحسن كبير! نحتاج مزيد من التحسينات');
            } else {
                console.log('🔧 تحسن محدود، نحتاج تحسينات إضافية');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testAlgorithmOverhaul };
