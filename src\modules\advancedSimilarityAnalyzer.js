/**
 * محلل التشابه المتقدم - خوارزمية hybrid جديدة للوصول لدقة 95%+
 */
class AdvancedSimilarityAnalyzer {
    constructor() {
        // عتبات محسنة للدقة العالية - أكثر حساسية
        this.thresholds = {
            exact_match: 0.85,      // تطابق حرفي عالي
            high_similarity: 0.60,  // تشابه عالي
            medium_similarity: 0.40, // تشابه متوسط
            low_similarity: 0.20    // تشابه منخفض
        };

        // أوزان محسنة للخوارزمية الجديدة - تركيز أكبر على التطابق المباشر
        this.weights = {
            exact_substring: 0.50,   // البحث عن العبارات المطابقة تماماً
            fuzzy_matching: 0.30,    // المطابقة الضبابية
            semantic_similarity: 0.15, // التشابه الدلالي
            structural_similarity: 0.05 // التشابه الهيكلي
        };
        
        // تحميل قاعدة البيانات المرجعية
        this.loadReferenceDatabase();
        
        console.log('🚀 تم تهيئة محلل التشابه المتقدم');
    }
    
    /**
     * تحميل قاعدة البيانات المرجعية
     */
    loadReferenceDatabase() {
        try {
            const fs = require('fs');
            const path = require('path');
            const dataPath = path.join(__dirname, '../data/reference_phrases.json');
            const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
            this.referenceTexts = data.academic_phrases || [];

            // تصفية العبارات الفارغة أو القصيرة
            this.referenceTexts = this.referenceTexts.filter(text =>
                text && typeof text === 'string' && text.trim().length > 10
            );

            // معالجة مسبقة للنصوص المرجعية
            this.preprocessedReferences = this.referenceTexts.map(text => ({
                original: text,
                processed: this.preprocessText(text),
                words: this.extractWords(text),
                phrases: this.extractPhrases(text, 3, 6),
                fingerprint: this.generateFingerprint(text)
            }));

            console.log(`✅ تم تحميل ${this.referenceTexts.length} عبارة مرجعية مع معالجة متقدمة`);

            // تحقق من أن البيانات تم تحميلها بشكل صحيح
            if (this.referenceTexts.length < 100) {
                console.warn(`⚠️ تحذير: عدد العبارات المرجعية قليل (${this.referenceTexts.length})`);
            }

        } catch (error) {
            console.error('❌ خطأ في تحميل قاعدة البيانات:', error.message);
            this.referenceTexts = [];
            this.preprocessedReferences = [];
        }
    }
    
    /**
     * تحليل التشابه المتقدم
     */
    async analyzeAdvancedSimilarity(inputText) {
        console.log('🔬 بدء التحليل المتقدم للتشابه...');
        
        // معالجة مسبقة للنص المدخل
        const processedInput = {
            original: inputText,
            processed: this.preprocessText(inputText),
            words: this.extractWords(inputText),
            phrases: this.extractPhrases(inputText, 3, 6),
            sentences: this.extractSentences(inputText),
            fingerprint: this.generateFingerprint(inputText)
        };
        
        const similarities = [];
        let maxSimilarity = 0;
        let totalSimilarity = 0;
        let matchedSegments = [];
        
        // مقارنة مع كل نص مرجعي
        for (let i = 0; i < this.preprocessedReferences.length; i++) {
            const reference = this.preprocessedReferences[i];
            
            // حساب التشابه بالطرق المختلفة
            const exactMatch = this.calculateExactSubstringMatch(processedInput, reference);
            const fuzzyMatch = this.calculateFuzzyMatch(processedInput, reference);
            const semanticMatch = this.calculateSemanticMatch(processedInput, reference);
            const structuralMatch = this.calculateStructuralMatch(processedInput, reference);
            
            // حساب النتيجة المركبة
            const combinedSimilarity = (
                exactMatch * this.weights.exact_substring +
                fuzzyMatch * this.weights.fuzzy_matching +
                semanticMatch * this.weights.semantic_similarity +
                structuralMatch * this.weights.structural_similarity
            );
            
            similarities.push({
                referenceIndex: i,
                exactMatch,
                fuzzyMatch,
                semanticMatch,
                structuralMatch,
                combinedSimilarity,
                referenceText: reference.original
            });
            
            totalSimilarity += combinedSimilarity;
            maxSimilarity = Math.max(maxSimilarity, combinedSimilarity);
            
            // إذا كان التشابه عالي، ابحث عن الأجزاء المطابقة
            if (combinedSimilarity > this.thresholds.medium_similarity) {
                const segments = this.findMatchingSegments(processedInput, reference, combinedSimilarity);
                matchedSegments.push(...segments);
            }
        }
        
        // حساب النتيجة النهائية
        const avgSimilarity = totalSimilarity / this.preprocessedReferences.length;
        const finalScore = this.calculateFinalScore(maxSimilarity, avgSimilarity, matchedSegments);
        
        // تصنيف مستوى الخطر
        const riskLevel = this.classifyRiskLevel(finalScore);
        
        console.log(`📊 انتهى التحليل: ${(finalScore * 100).toFixed(1)}% تشابه`);
        
        return {
            plagiarismPercentage: Math.round(finalScore * 100),
            maxSimilarity,
            avgSimilarity,
            matchedSegments: matchedSegments.slice(0, 15), // أعلى 15 جزء
            riskLevel,
            detailedAnalysis: {
                totalReferences: this.preprocessedReferences.length,
                highSimilarityCount: similarities.filter(s => s.combinedSimilarity > this.thresholds.high_similarity).length,
                topMatches: similarities
                    .sort((a, b) => b.combinedSimilarity - a.combinedSimilarity)
                    .slice(0, 5)
            }
        };
    }
    
    /**
     * حساب التطابق الحرفي للعبارات الفرعية - محسن للحساسية
     */
    calculateExactSubstringMatch(input, reference) {
        let matchScore = 0;
        const inputText = input.processed.toLowerCase();
        const refText = reference.processed.toLowerCase();

        // البحث عن عبارات طويلة متطابقة (8+ أحرف) - أكثر حساسية
        for (let len = 30; len >= 8; len--) {
            for (let i = 0; i <= inputText.length - len; i++) {
                const substring = inputText.substring(i, i + len);
                if (refText.includes(substring)) {
                    // مكافأة أكبر للعبارات الطويلة
                    const bonus = len > 15 ? 2.0 : 1.0;
                    matchScore += (len / inputText.length) * bonus;
                }
            }
        }

        // البحث عن عبارات كاملة متطابقة - مكافأة أكبر
        reference.phrases.forEach(phrase => {
            if (inputText.includes(phrase.toLowerCase())) {
                matchScore += 0.3; // مكافأة أكبر للعبارات الكاملة
            }
        });

        // البحث عن كلمات متتالية متطابقة
        const inputWords = input.words;
        const refWords = reference.words;

        for (let i = 0; i < inputWords.length - 2; i++) {
            const phrase = inputWords.slice(i, i + 3).join(' ').toLowerCase();
            const refPhrase = refWords.join(' ').toLowerCase();
            if (refPhrase.includes(phrase)) {
                matchScore += 0.2;
            }
        }

        return Math.min(1.0, matchScore);
    }
    
    /**
     * حساب المطابقة الضبابية
     */
    calculateFuzzyMatch(input, reference) {
        // مقارنة الكلمات المشتركة
        const inputWords = new Set(input.words);
        const refWords = new Set(reference.words);
        const intersection = new Set([...inputWords].filter(x => refWords.has(x)));
        const union = new Set([...inputWords, ...refWords]);
        
        const jaccardSimilarity = intersection.size / union.size;
        
        // مقارنة العبارات المشتركة
        const inputPhrases = new Set(input.phrases);
        const refPhrases = new Set(reference.phrases);
        const phraseIntersection = new Set([...inputPhrases].filter(x => refPhrases.has(x)));
        const phraseUnion = new Set([...inputPhrases, ...refPhrases]);
        
        const phraseSimilarity = phraseUnion.size > 0 ? phraseIntersection.size / phraseUnion.size : 0;
        
        return (jaccardSimilarity * 0.6 + phraseSimilarity * 0.4);
    }
    
    /**
     * حساب التشابه الدلالي
     */
    calculateSemanticMatch(input, reference) {
        // تشابه بسيط بناءً على الكلمات المفتاحية
        const inputKeywords = this.extractKeywords(input.processed);
        const refKeywords = this.extractKeywords(reference.processed);
        
        let semanticScore = 0;
        inputKeywords.forEach(keyword => {
            if (refKeywords.includes(keyword)) {
                semanticScore += 1;
            }
        });
        
        return Math.min(1.0, semanticScore / Math.max(inputKeywords.length, 1));
    }
    
    /**
     * حساب التشابه الهيكلي
     */
    calculateStructuralMatch(input, reference) {
        // مقارنة بصمة النص
        const inputFingerprint = input.fingerprint;
        const refFingerprint = reference.fingerprint;
        
        let matchingFeatures = 0;
        const totalFeatures = Math.max(inputFingerprint.length, refFingerprint.length);
        
        for (let i = 0; i < Math.min(inputFingerprint.length, refFingerprint.length); i++) {
            if (Math.abs(inputFingerprint[i] - refFingerprint[i]) < 0.1) {
                matchingFeatures++;
            }
        }
        
        return totalFeatures > 0 ? matchingFeatures / totalFeatures : 0;
    }
    
    /**
     * البحث عن الأجزاء المطابقة
     */
    findMatchingSegments(input, reference, similarity) {
        const segments = [];
        const sentences = input.sentences;
        
        sentences.forEach((sentence, index) => {
            const sentenceProcessed = this.preprocessText(sentence);
            
            // البحث عن تطابق في النص المرجعي
            if (reference.processed.toLowerCase().includes(sentenceProcessed.toLowerCase()) ||
                this.calculateFuzzyMatch(
                    { words: this.extractWords(sentence), phrases: this.extractPhrases(sentence, 2, 4) },
                    reference
                ) > 0.7) {
                
                segments.push({
                    text: sentence,
                    similarity: similarity,
                    startIndex: index,
                    matchedReference: reference.original,
                    type: 'sentence_match'
                });
            }
        });
        
        return segments;
    }
    
    /**
     * حساب النتيجة النهائية - محسن للتمييز بين مستويات الاستلال
     */
    calculateFinalScore(maxSimilarity, avgSimilarity, matchedSegments) {
        // وزن أكبر للتشابه الأقصى مع تضخيم للقيم العالية
        let finalScore = Math.pow(maxSimilarity, 0.8) * 0.8 + avgSimilarity * 0.2;

        // مكافأة كبيرة للأجزاء المطابقة
        const segmentBonus = Math.min(0.4, matchedSegments.length * 0.05);
        finalScore += segmentBonus;

        // مكافأة تدريجية للتشابه العالي
        if (maxSimilarity > this.thresholds.high_similarity) {
            finalScore += 0.2;
        } else if (maxSimilarity > this.thresholds.medium_similarity) {
            finalScore += 0.1;
        }

        // تضخيم النتائج العالية وتقليل المنخفضة
        if (finalScore > 0.5) {
            finalScore = Math.min(1.0, finalScore * 1.3);
        } else if (finalScore < 0.3) {
            finalScore = finalScore * 0.7;
        }

        return Math.min(1.0, finalScore);
    }
    
    /**
     * تصنيف مستوى الخطر
     */
    classifyRiskLevel(score) {
        if (score >= 0.9) return { level: 5, label: 'عالي جداً' };
        if (score >= 0.7) return { level: 4, label: 'عالي' };
        if (score >= 0.5) return { level: 3, label: 'متوسط' };
        if (score >= 0.3) return { level: 2, label: 'منخفض' };
        return { level: 1, label: 'ضئيل' };
    }
    
    /**
     * معالجة النص
     */
    preprocessText(text) {
        return text
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();
    }
    
    /**
     * استخراج الكلمات
     */
    extractWords(text) {
        return this.preprocessText(text)
            .split(/\s+/)
            .filter(word => word.length > 2);
    }
    
    /**
     * استخراج العبارات
     */
    extractPhrases(text, minLength, maxLength) {
        const words = this.extractWords(text);
        const phrases = [];
        
        for (let len = minLength; len <= maxLength; len++) {
            for (let i = 0; i <= words.length - len; i++) {
                const phrase = words.slice(i, i + len).join(' ');
                if (phrase.length > 10) {
                    phrases.push(phrase);
                }
            }
        }
        
        return phrases;
    }
    
    /**
     * استخراج الجمل
     */
    extractSentences(text) {
        return text
            .split(/[.!?؟]/)
            .map(s => s.trim())
            .filter(s => s.length > 20);
    }
    
    /**
     * استخراج الكلمات المفتاحية
     */
    extractKeywords(text) {
        const stopWords = new Set(['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'التي', 'الذي']);
        return this.extractWords(text)
            .filter(word => word.length > 3 && !stopWords.has(word))
            .slice(0, 20); // أهم 20 كلمة
    }
    
    /**
     * إنتاج بصمة النص
     */
    generateFingerprint(text) {
        const words = this.extractWords(text);
        const features = [
            words.length / 100,                    // طول النص
            (text.match(/[.!?؟]/g) || []).length / 10, // عدد الجمل
            (text.match(/[،,]/g) || []).length / 20,   // عدد الفواصل
            words.filter(w => w.length > 5).length / words.length, // نسبة الكلمات الطويلة
            words.filter(w => /^[A-Za-z]/.test(w)).length / words.length // نسبة الكلمات الإنجليزية
        ];
        
        return features;
    }
}

module.exports = AdvancedSimilarityAnalyzer;
