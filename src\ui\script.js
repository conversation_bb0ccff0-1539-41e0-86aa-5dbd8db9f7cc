const { ipc<PERSON>enderer } = require('electron');

/**
 * ملف JavaScript الرئيسي للواجهة
 * يتعامل مع جميع التفاعلات والأحداث
 */

// متغيرات عامة
let currentFile = null;
let currentPDFFile = null;
let isProcessing = false;

// تهيئة التطبيق
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 تهيئة واجهة المستخدم...');
    
    // تهيئة علامات التبويب
    initializeTabs();
    
    // تهيئة أحداث كشف الاستلال
    initializePlagiarismEvents();
    
    // تهيئة أحداث تحويل PDF
    initializePDFConversionEvents();
    
    // تهيئة أحداث المعالجة المتعددة
    initializeBatchProcessingEvents();
    
    // تهيئة أحداث الإعدادات
    initializeSettingsEvents();
    
    // تهيئة النوافذ المنبثقة
    initializeModals();
    
    // تحميل معلومات النظام
    loadSystemInfo();
    
    console.log('✅ تم تهيئة الواجهة بنجاح');
});

// تهيئة علامات التبويب
function initializeTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');
    
    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const tabId = button.getAttribute('data-tab');
            
            // إزالة الفئة النشطة من جميع الأزرار والألواح
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));
            
            // إضافة الفئة النشطة للزر واللوح المحدد
            button.classList.add('active');
            document.getElementById(`${tabId}-tab`).classList.add('active');
            
            updateStatusText(`تم التبديل إلى تبويب ${button.textContent.trim()}`);
        });
    });
}

// تهيئة أحداث كشف الاستلال
function initializePlagiarismEvents() {
    const uploadArea = document.getElementById('plagiarism-upload');
    const selectFileBtn = document.getElementById('select-file-btn');
    const checkBtn = document.getElementById('check-plagiarism-btn');
    const checkAnotherBtn = document.getElementById('check-another-btn');
    
    // السحب والإفلات
    setupDragAndDrop(uploadArea, handleFileSelection);
    
    // اختيار ملف
    selectFileBtn.addEventListener('click', async () => {
        const result = await ipcRenderer.invoke('open-file-dialog', {
            title: 'اختيار ملف للفحص',
            filters: [
                { name: 'جميع الملفات المدعومة', extensions: ['txt', 'pdf', 'doc', 'docx'] },
                { name: 'ملفات نصية', extensions: ['txt'] },
                { name: 'ملفات PDF', extensions: ['pdf'] },
                { name: 'ملفات Word', extensions: ['doc', 'docx'] }
            ]
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
            handleFileSelection(result.filePaths[0]);
        }
    });
    
    // فحص الاستلال
    checkBtn.addEventListener('click', async () => {
        if (currentFile && !isProcessing) {
            await checkPlagiarism(currentFile);
        }
    });
    
    // فحص ملف آخر
    checkAnotherBtn.addEventListener('click', () => {
        resetPlagiarismInterface();
    });
}

// تهيئة أحداث تحويل PDF
function initializePDFConversionEvents() {
    const pdfUploadArea = document.getElementById('pdf-upload');
    const selectPDFBtn = document.getElementById('select-pdf-btn');
    const browseOutputBtn = document.getElementById('browse-output-btn');
    const convertBtn = document.getElementById('convert-pdf-btn');
    const convertAnotherBtn = document.getElementById('convert-another-btn');
    
    // السحب والإفلات للـ PDF
    setupDragAndDrop(pdfUploadArea, handlePDFSelection, ['.pdf']);
    
    // اختيار ملف PDF
    selectPDFBtn.addEventListener('click', async () => {
        const result = await ipcRenderer.invoke('open-file-dialog', {
            title: 'اختيار ملف PDF للتحويل',
            filters: [{ name: 'ملفات PDF', extensions: ['pdf'] }]
        });
        
        if (!result.canceled && result.filePaths.length > 0) {
            handlePDFSelection(result.filePaths[0]);
        }
    });
    
    // اختيار مسار الإخراج
    browseOutputBtn.addEventListener('click', async () => {
        const result = await ipcRenderer.invoke('save-file-dialog', {
            title: 'حفظ ملف DOCX',
            defaultPath: currentPDFFile ? currentPDFFile.replace('.pdf', '.docx') : 'converted_document.docx',
            filters: [{ name: 'ملفات Word', extensions: ['docx'] }]
        });
        
        if (!result.canceled) {
            document.getElementById('output-path').value = result.filePath;
        }
    });
    
    // تحويل PDF
    convertBtn.addEventListener('click', async () => {
        if (currentPDFFile && !isProcessing) {
            await convertPDFToDocx();
        }
    });
    
    // تحويل ملف آخر
    convertAnotherBtn.addEventListener('click', () => {
        resetPDFConversionInterface();
    });
}

// تهيئة أحداث المعالجة المتعددة
function initializeBatchProcessingEvents() {
    const batchTypeButtons = document.querySelectorAll('.batch-type-btn');
    const plagiarismSettings = document.getElementById('batch-plagiarism-settings');
    const conversionSettings = document.getElementById('batch-conversion-settings');
    
    // تبديل نوع المعالجة المتعددة
    batchTypeButtons.forEach(button => {
        button.addEventListener('click', () => {
            const type = button.getAttribute('data-type');
            
            batchTypeButtons.forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            if (type === 'plagiarism') {
                plagiarismSettings.style.display = 'block';
                conversionSettings.style.display = 'none';
            } else {
                plagiarismSettings.style.display = 'none';
                conversionSettings.style.display = 'block';
            }
        });
    });
    
    // أزرار تصفح المجلدات
    setupFolderBrowsing();
    
    // أزرار بدء المعالجة
    document.getElementById('start-batch-check-btn').addEventListener('click', startBatchPlagiarismCheck);
    document.getElementById('start-batch-convert-btn').addEventListener('click', startBatchPDFConversion);
}

// تهيئة أحداث الإعدادات
function initializeSettingsEvents() {
    const saveSettingsBtn = document.getElementById('save-settings-btn');
    const resetSettingsBtn = document.getElementById('reset-settings-btn');
    
    saveSettingsBtn.addEventListener('click', saveSettings);
    resetSettingsBtn.addEventListener('click', resetSettings);
}

// تهيئة النوافذ المنبثقة
function initializeModals() {
    const modals = document.querySelectorAll('.modal');
    
    modals.forEach(modal => {
        const closeBtn = modal.querySelector('.modal-close');
        const okBtn = modal.querySelector('.action-button');
        
        if (closeBtn) {
            closeBtn.addEventListener('click', () => hideModal(modal.id));
        }
        
        if (okBtn) {
            okBtn.addEventListener('click', () => hideModal(modal.id));
        }
        
        // إغلاق عند النقر خارج النافذة
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                hideModal(modal.id);
            }
        });
    });
}

// إعداد السحب والإفلات
function setupDragAndDrop(element, handler, allowedExtensions = ['.txt', '.pdf', '.doc', '.docx']) {
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        element.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    ['dragenter', 'dragover'].forEach(eventName => {
        element.addEventListener(eventName, () => element.classList.add('dragover'), false);
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        element.addEventListener(eventName, () => element.classList.remove('dragover'), false);
    });
    
    element.addEventListener('drop', (e) => {
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const file = files[0];
            const extension = '.' + file.name.split('.').pop().toLowerCase();
            
            if (allowedExtensions.includes(extension)) {
                handler(file.path);
            } else {
                showError(`نوع الملف غير مدعوم. الأنواع المدعومة: ${allowedExtensions.join(', ')}`);
            }
        }
    });
}

// معالجة اختيار ملف للفحص
function handleFileSelection(filePath) {
    currentFile = filePath;
    const fileName = filePath.split('\\').pop().split('/').pop();
    const fileExtension = fileName.split('.').pop().toLowerCase();
    
    // عرض معلومات الملف
    document.getElementById('file-name').textContent = fileName;
    document.getElementById('file-type').textContent = fileExtension.toUpperCase();
    
    // محاولة الحصول على حجم الملف
    try {
        const fs = require('fs');
        const stats = fs.statSync(filePath);
        document.getElementById('file-size').textContent = formatFileSize(stats.size);
    } catch (error) {
        document.getElementById('file-size').textContent = 'غير معروف';
    }
    
    // إظهار معلومات الملف وإخفاء منطقة الرفع
    document.getElementById('file-info').style.display = 'block';
    document.getElementById('plagiarism-upload').style.display = 'none';
    
    updateStatusText(`تم اختيار الملف: ${fileName}`);
}

// معالجة اختيار ملف PDF
function handlePDFSelection(filePath) {
    currentPDFFile = filePath;
    const fileName = filePath.split('\\').pop().split('/').pop();
    
    // تعيين مسار الإخراج الافتراضي
    const outputPath = filePath.replace('.pdf', '.docx');
    document.getElementById('output-path').value = outputPath;
    
    // إظهار خيارات التحويل
    document.getElementById('conversion-options').style.display = 'block';
    document.getElementById('pdf-upload').style.display = 'none';
    
    updateStatusText(`تم اختيار ملف PDF: ${fileName}`);
}

// فحص الاستلال
async function checkPlagiarism(filePath) {
    if (isProcessing) return;
    
    isProcessing = true;
    showProgress('progress-section', 'جاري فحص الاستلال...');
    updateStatusText('جاري فحص الاستلال...');
    
    try {
        // محاكاة التقدم
        updateProgress('progress-fill', 'progress-text', 20, 'تحليل الملف...');
        
        const result = await ipcRenderer.invoke('check-plagiarism', filePath);
        
        updateProgress('progress-fill', 'progress-text', 60, 'فحص قاعدة البيانات...');
        
        if (result.success) {
            updateProgress('progress-fill', 'progress-text', 100, 'تم الفحص بنجاح!');
            
            setTimeout(() => {
                hideProgress('progress-section');
                displayPlagiarismResults(result.result);
            }, 500);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        hideProgress('progress-section');
        showError(`خطأ في فحص الاستلال: ${error.message}`);
    } finally {
        isProcessing = false;
        updateStatusText('جاهز');
    }
}

// تحويل PDF إلى DOCX
async function convertPDFToDocx() {
    if (isProcessing) return;
    
    const outputPath = document.getElementById('output-path').value;
    const title = document.getElementById('document-title').value;
    
    if (!outputPath) {
        showError('يرجى تحديد مسار حفظ الملف');
        return;
    }
    
    isProcessing = true;
    showProgress('conversion-progress', 'جاري تحويل PDF إلى DOCX...');
    updateStatusText('جاري التحويل...');
    
    try {
        updateProgress('conversion-progress-fill', 'conversion-progress-text', 30, 'استخراج النص من PDF...');
        
        const result = await ipcRenderer.invoke('convert-pdf-to-docx', currentPDFFile, outputPath);
        
        updateProgress('conversion-progress-fill', 'conversion-progress-text', 80, 'إنشاء ملف DOCX...');
        
        if (result.success) {
            updateProgress('conversion-progress-fill', 'conversion-progress-text', 100, 'تم التحويل بنجاح!');
            
            setTimeout(() => {
                hideProgress('conversion-progress');
                displayConversionResults(result.result);
            }, 500);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        hideProgress('conversion-progress');
        showError(`خطأ في التحويل: ${error.message}`);
    } finally {
        isProcessing = false;
        updateStatusText('جاهز');
    }
}

// عرض نتائج فحص الاستلال
function displayPlagiarismResults(result) {
    // عرض النسبة المئوية
    document.getElementById('plagiarism-percentage').textContent = `${result.plagiarismPercentage}%`;
    
    // عرض معلومات اللغة
    document.getElementById('detected-language').textContent = result.extractionInfo.languageName;
    document.getElementById('word-count').textContent = result.extractionInfo.statistics.words.toLocaleString();
    
    // عرض مستوى الخطر
    const riskLevel = document.getElementById('risk-level');
    const riskDescription = document.getElementById('risk-description');
    
    riskLevel.textContent = result.riskLevel.label;
    riskLevel.className = `risk-level ${result.riskLevel.level <= 2 ? 'low' : result.riskLevel.level <= 3 ? 'medium' : 'high'}`;
    riskDescription.textContent = result.riskLevel.description;
    
    // عرض الأجزاء المشبوهة
    document.getElementById('suspicious-segments-count').textContent = result.suspiciousSegments.length;
    
    // إظهار قسم النتائج
    document.getElementById('results-section').style.display = 'block';
    
    updateStatusText(`تم الفحص - نسبة الاستلال: ${result.plagiarismPercentage}%`);
}

// عرض نتائج التحويل
function displayConversionResults(result) {
    document.getElementById('conversion-language').textContent = result.languageName;
    document.getElementById('conversion-text-length').textContent = `${result.textLength.toLocaleString()} حرف`;
    document.getElementById('conversion-time').textContent = `${result.processingTime}ms`;
    
    // إظهار قسم النتائج
    document.getElementById('conversion-results').style.display = 'block';
    
    updateStatusText(`تم التحويل بنجاح - ${result.textLength.toLocaleString()} حرف`);
}

// إعداد تصفح المجلدات
function setupFolderBrowsing() {
    const folderInputs = [
        { buttonId: 'browse-input-folder-btn', inputId: 'input-folder' },
        { buttonId: 'browse-report-btn', inputId: 'report-output' },
        { buttonId: 'browse-pdf-folder-btn', inputId: 'pdf-input-folder' },
        { buttonId: 'browse-docx-output-btn', inputId: 'docx-output-folder' }
    ];
    
    folderInputs.forEach(({ buttonId, inputId }) => {
        const button = document.getElementById(buttonId);
        const input = document.getElementById(inputId);
        
        if (button && input) {
            button.addEventListener('click', async () => {
                const isFolder = !inputId.includes('report');
                
                if (isFolder) {
                    const result = await ipcRenderer.invoke('open-folder-dialog', {
                        title: 'اختيار مجلد'
                    });
                    
                    if (!result.canceled && result.filePaths.length > 0) {
                        input.value = result.filePaths[0];
                    }
                } else {
                    const result = await ipcRenderer.invoke('save-file-dialog', {
                        title: 'حفظ تقرير',
                        defaultPath: 'plagiarism_report.json',
                        filters: [{ name: 'ملفات JSON', extensions: ['json'] }]
                    });
                    
                    if (!result.canceled) {
                        input.value = result.filePath;
                    }
                }
            });
        }
    });
}

// بدء فحص متعدد للاستلال
async function startBatchPlagiarismCheck() {
    const inputFolder = document.getElementById('input-folder').value;
    const reportOutput = document.getElementById('report-output').value;
    
    if (!inputFolder) {
        showError('يرجى اختيار مجلد الملفات');
        return;
    }
    
    if (isProcessing) return;
    
    isProcessing = true;
    showProgress('batch-progress', 'جاري الفحص المتعدد...');
    updateStatusText('جاري الفحص المتعدد...');
    
    try {
        const result = await ipcRenderer.invoke('batch-check-plagiarism', inputFolder, reportOutput);
        
        if (result.success) {
            displayBatchResults(result);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        showError(`خطأ في الفحص المتعدد: ${error.message}`);
    } finally {
        isProcessing = false;
        hideProgress('batch-progress');
        updateStatusText('جاهز');
    }
}

// بدء تحويل متعدد PDF
async function startBatchPDFConversion() {
    const inputFolder = document.getElementById('pdf-input-folder').value;
    const outputFolder = document.getElementById('docx-output-folder').value;
    
    if (!inputFolder || !outputFolder) {
        showError('يرجى اختيار مجلدي المدخل والإخراج');
        return;
    }
    
    if (isProcessing) return;
    
    isProcessing = true;
    showProgress('batch-progress', 'جاري التحويل المتعدد...');
    updateStatusText('جاري التحويل المتعدد...');
    
    try {
        const result = await ipcRenderer.invoke('batch-convert-pdf', inputFolder, outputFolder);
        
        if (result.success) {
            displayBatchResults(result.result);
        } else {
            throw new Error(result.error);
        }
    } catch (error) {
        showError(`خطأ في التحويل المتعدد: ${error.message}`);
    } finally {
        isProcessing = false;
        hideProgress('batch-progress');
        updateStatusText('جاهز');
    }
}

// عرض نتائج المعالجة المتعددة
function displayBatchResults(result) {
    document.getElementById('batch-total-files').textContent = result.totalFiles || result.summary?.total || 0;
    document.getElementById('batch-successful').textContent = result.successCount || result.summary?.successful || 0;
    document.getElementById('batch-failed').textContent = result.failCount || result.summary?.failed || 0;
    
    document.getElementById('batch-results').style.display = 'block';
}

// تحميل معلومات النظام
async function loadSystemInfo() {
    try {
        const result = await ipcRenderer.invoke('get-system-info');
        
        if (result.success) {
            const systemInfo = document.getElementById('system-info');
            systemInfo.innerHTML = `
                <div><strong>إصدار التطبيق:</strong> ${result.info.appVersion}</div>
                <div><strong>إصدار Node.js:</strong> ${result.info.nodeVersion}</div>
                <div><strong>النظام:</strong> ${result.info.platform} (${result.info.arch})</div>
                <div><strong>الميزات المدعومة:</strong></div>
                <ul>
                    <li>كشف الاستلال: ${result.info.features.plagiarismDetection ? '✅' : '❌'}</li>
                    <li>تحويل PDF: ${result.info.features.pdfConversion ? '✅' : '❌'}</li>
                    <li>متعدد اللغات: ${result.info.features.multiLanguage ? '✅' : '❌'}</li>
                    <li>معالجة متعددة: ${result.info.features.batchProcessing ? '✅' : '❌'}</li>
                </ul>
            `;
        }
    } catch (error) {
        console.error('خطأ في تحميل معلومات النظام:', error);
    }
}

// حفظ الإعدادات
function saveSettings() {
    const settings = {
        useMultilingual: document.getElementById('use-multilingual').checked,
        useEnhancedExtractor: document.getElementById('use-enhanced-extractor').checked,
        ignoreGemini: document.getElementById('ignore-gemini').checked,
        defaultFontArabic: document.getElementById('default-font-arabic').value,
        defaultFontEnglish: document.getElementById('default-font-english').value
    };
    
    localStorage.setItem('app-settings', JSON.stringify(settings));
    showSuccess('تم حفظ الإعدادات بنجاح');
}

// إعادة تعيين الإعدادات
function resetSettings() {
    document.getElementById('use-multilingual').checked = true;
    document.getElementById('use-enhanced-extractor').checked = true;
    document.getElementById('ignore-gemini').checked = true;
    document.getElementById('default-font-arabic').value = 'Arial Unicode MS';
    document.getElementById('default-font-english').value = 'Calibri';
    
    localStorage.removeItem('app-settings');
    showSuccess('تم إعادة تعيين الإعدادات');
}

// إعادة تعيين واجهة فحص الاستلال
function resetPlagiarismInterface() {
    currentFile = null;
    document.getElementById('file-info').style.display = 'none';
    document.getElementById('results-section').style.display = 'none';
    document.getElementById('plagiarism-upload').style.display = 'block';
    updateStatusText('جاهز');
}

// إعادة تعيين واجهة تحويل PDF
function resetPDFConversionInterface() {
    currentPDFFile = null;
    document.getElementById('conversion-options').style.display = 'none';
    document.getElementById('conversion-results').style.display = 'none';
    document.getElementById('pdf-upload').style.display = 'block';
    document.getElementById('document-title').value = '';
    document.getElementById('output-path').value = '';
    updateStatusText('جاهز');
}

// دوال مساعدة
function showProgress(sectionId, message) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.style.display = 'block';
        const textElement = section.querySelector('.progress-text');
        if (textElement) textElement.textContent = message;
    }
}

function hideProgress(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) section.style.display = 'none';
}

function updateProgress(fillId, textId, percentage, text) {
    const fill = document.getElementById(fillId);
    const textElement = document.getElementById(textId);
    
    if (fill) fill.style.width = `${percentage}%`;
    if (textElement) textElement.textContent = text;
}

function showError(message) {
    document.getElementById('error-message').textContent = message;
    showModal('error-modal');
}

function showSuccess(message) {
    document.getElementById('success-message').textContent = message;
    showModal('success-modal');
}

function showModal(modalId) {
    document.getElementById(modalId).style.display = 'flex';
}

function hideModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

function updateStatusText(text) {
    document.getElementById('status-text').textContent = text;
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 بايت';
    const k = 1024;
    const sizes = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// معالجة الأحداث من العملية الرئيسية
ipcRenderer.on('app-ready', (event, data) => {
    console.log('✅ التطبيق جاهز:', data.title);
    updateStatusText('التطبيق جاهز للاستخدام');
});

ipcRenderer.on('file-selected', (event, filePath) => {
    handleFileSelection(filePath);
});

ipcRenderer.on('pdf-selected-for-conversion', (event, filePath) => {
    // التبديل إلى تبويب التحويل
    document.querySelector('[data-tab="conversion"]').click();
    handlePDFSelection(filePath);
});

ipcRenderer.on('batch-progress', (event, data) => {
    document.getElementById('batch-current').textContent = data.processed;
    document.getElementById('batch-total').textContent = data.total;
    updateProgress('batch-progress-fill', 'batch-progress-text', data.percentage, `معالجة ${data.processed} من ${data.total} ملف...`);
});

console.log('📱 تم تحميل ملف JavaScript للواجهة');
