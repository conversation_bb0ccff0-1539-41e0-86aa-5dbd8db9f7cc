{"timestamp": "2025-07-05T01:00:26.733Z", "test_type": "advanced_algorithm_test", "algorithm_features": {"exact_substring_weight": 35, "fuzzy_matching_weight": 25, "semantic_similarity_weight": 20, "structural_similarity_weight": 20, "advanced_preprocessing": true, "phrase_extraction": true, "fingerprint_analysis": true}, "results": {"avgAccuracy": 51.2962962962963, "successRate": 33.33333333333333, "avgTime": 827.6666666666666, "avgMaxSimilarity": 0, "avgMatchedCount": 0, "targetsAchieved": 1, "improvement": {"accuracy": -0.40370370370370523, "successRate": 0.03333333333333144}}, "detailed_results": [{"file": "test_calibrated_20_percent.txt", "expected": 20, "actual": 84, "accuracy": 0, "processingTime": 501, "matchedCount": 0, "highSimilarityCount": 0, "passed": false}, {"file": "test_calibrated_60_percent.txt", "expected": 60, "actual": 85, "accuracy": 58.33333333333333, "processingTime": 740, "matchedCount": 0, "highSimilarityCount": 0, "passed": false}, {"file": "test_calibrated_90_percent.txt", "expected": 90, "actual": 86, "accuracy": 95.55555555555556, "processingTime": 1242, "matchedCount": 0, "highSimilarityCount": 0, "passed": true}]}