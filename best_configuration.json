{"timestamp": "2025-07-05T01:06:58.193Z", "bestConfiguration": {"name": "ArabicOptimized", "useAdvanced": false, "thresholds": {"high": 45, "medium": 30, "low": 18}, "weights": {"jaccard": 45, "cosine": 40, "levenshtein": 12, "semantic": 3}}, "performance": {"avgAccuracy": 49.**************, "successRate": 33.33333333333333, "avgTime": 391, "score": 38.136005555555556}, "allResults": [{"file": "test_calibrated_20_percent.txt", "expected": 20, "actual": 19, "accuracy": 95, "processingTime": 229, "passed": true}, {"file": "test_calibrated_60_percent.txt", "expected": 60, "actual": 20, "accuracy": 33.33333333333334, "processingTime": 393, "passed": false}, {"file": "test_calibrated_90_percent.txt", "expected": 90, "actual": 18, "accuracy": 20, "processingTime": 551, "passed": false}]}