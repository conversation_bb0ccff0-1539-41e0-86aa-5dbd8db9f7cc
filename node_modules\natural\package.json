{"name": "natural", "description": "General natural language (tokenizing, stemming (English, Russian, Spanish), part-of-speech tagging, sentiment analysis, classification, inflection, phonetics, tfidf, WordNet, jaro-winkler, <PERSON><PERSON><PERSON>ein distance, Dice's Coefficient) facilities for node.", "version": "6.12.0", "homepage": "https://github.com/NaturalNode/natural", "repository": {"type": "git", "url": "git://github.com/NaturalNode/natural.git"}, "engines": {"node": ">=0.4.10"}, "dependencies": {"afinn-165": "^1.0.2", "afinn-165-financialmarketnews": "^3.0.0", "apparatus": "^0.0.10", "dotenv": "^16.4.5", "memjs": "^1.3.2", "mongoose": "^8.2.0", "pg": "^8.11.3", "redis": "^4.6.13", "safe-stable-stringify": "^2.2.0", "stopwords-iso": "^1.1.0", "sylvester": "^0.0.12", "underscore": "^1.9.1", "uuid": "^9.0.1", "wordnet-db": "^3.1.11"}, "devDependencies": {"@types/node": "^18.11.18", "browserfs": "^1.4.3", "gulp": "^4.0.2", "gulp-jasmine": "^4.0.0", "gulp-jasmine-browser": "^4.1.0", "jasmine": "^3.3.1", "jasmine-core": "^3.3.0", "nyc": "^15.1.0", "pegjs": "^0.10.0", "proxyquire": "^1.8.0", "rimraf": "^2.6.3", "sinon": "^1.12.2", "standard": "^16.0.4", "ts-standard": "^12.0.2", "typescript": "^4.9.3", "uubench": "^0.0.1", "webpack": "^4.29.0", "webpack-stream": "^5.2.1"}, "standard": {"ignore": ["/lib/natural/brill_pos_tagger/lib/TF_Parser.js", "/lib/natural/tokenizers/parser_sentence_tokenizer.js"], "env": {"jasmine": true}}, "jscpd": {"ignore": ["lib/natural/brill_pos_tagger/lib/TF_Parser.js", "lib/natural/tokenizers/parser_sentence_tokenizer.js"]}, "scripts": {"benchmark": "node benchmarks", "clean": "rimraf *~ #* *classifier.json", "test": "NODE_PATH=. node ./node_modules/jasmine/bin/jasmine.js --random=false spec/*_spec.js", "coverage": "nyc --reporter=lcov npm run test && nyc npm run test_io", "test_io": "NODE_PATH=. node ./node_modules/jasmine/bin/jasmine.js --random=false io_spec/*_spec.js", "test_browser": "NODE_PATH=. node ./node_modules/gulp/bin/gulp.js", "lint": "eslint . --ext .ts", "lint-ts": "ts-standard **/*.ts"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "keywords": ["natural language processing", "artifical intelligence", "statistics", "<PERSON> stemmer", "Lancaster stemmer", "tokenizer", "bigram", "trigram", "quadgram", "ngram", "stemmer", "bayes", "classifier", "phonetic", "metaphone", "inflector", "Wordnet", "tf-idf", "logistic regression", "doublemetaphone", "double", "jaro-winkler distance", "levenshtein distance", "string distance", "part-of-speech tagger", "<PERSON>", "Brill tagger", "sentiment analysis", "maximum entropy modelling"], "main": "./lib/natural/index.js", "types": "./lib/natural/index.d.ts", "maintainers": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "http://www.chrisumbel.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}]}