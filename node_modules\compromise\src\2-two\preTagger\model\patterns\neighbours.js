const vb = 'Verb'
const nn = 'Noun'

export default {
  // looking at the previous word's tags:
  leftTags: [
    ['Adjective', nn],
    ['Possessive', nn],
    ['Determiner', nn],
    ['Adverb', vb],
    ['Pronoun', vb],
    ['Value', nn],
    ['Ordinal', nn],
    ['Modal', vb],
    ['Superlative', nn],
    ['Demonym', nn],
    ['Honorific', 'Person'], //dr. <PERSON>
  ],
  // looking at the previous word:
  leftWords: [
    ['i', vb],
    ['first', nn],
    ['it', vb],
    ['there', vb],
    ['not', vb],
    ['because', nn],
    ['if', nn],
    ['but', nn],
    ['who', vb],
    ['this', nn],
    ['his', nn],
    ['when', nn],
    ['you', vb],
    ['very', 'Adjective'],
    ['old', nn],
    ['never', vb],
    ['before', nn],
    ['a', nn],
    ['the', nn],
    ['been', vb],
  ],

  // looking at the next word's tags:
  rightTags: [
    ['Copula', nn],
    ['PastTense', nn],
    ['Conjunction', nn],
    ['Modal', nn],
  ],
  // looking at the next word:
  rightWords: [
    ['there', vb],
    ['me', vb],
    ['man', 'Adjective'],
    // ['only', vb],
    ['him', vb],
    ['it', vb],//relaunch it
    ['were', nn],
    ['took', nn],
    ['himself', vb],
    ['went', nn],
    ['who', nn],
    ['jr', 'Person'],
  ],
}
