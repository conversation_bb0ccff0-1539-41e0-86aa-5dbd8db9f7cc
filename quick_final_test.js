const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار سريع نهائي
 */
async function quickFinalTest() {
    console.log('🎯 اختبار سريع نهائي لـ Plagiarism Checker Pro');
    console.log('=' .repeat(60));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار المتاحة
    const testFiles = [
        { file: 'test_large_10_percent.txt', expected: 15, description: 'نص كبير أصلي' },
        { file: 'test_large_50_percent.txt', expected: 55, description: 'نص كبير مختلط' },
        { file: 'test_large_90_percent.txt', expected: 85, description: 'نص كبير مستل' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const accuracy = Math.max(0, 100 - Math.abs(result.plagiarismPercentage - testCase.expected) / testCase.expected * 100);
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                passed: accuracy >= 70
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج
    console.log('\n' + '=' .repeat(60));
    console.log('📊 النتائج النهائية');
    console.log('=' .repeat(60));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`📈 الإحصائيات:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}%`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (${passedTests.length}/${validResults.length})`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
        
        console.log(`\n🎯 تقييم الأهداف:`);
        console.log(`   دقة 85%+: ${avgAccuracy >= 85 ? '✅' : '❌'} (${avgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 80%+: ${successRate >= 80 ? '✅' : '❌'} (${successRate.toFixed(1)}%)`);
        console.log(`   أداء < 5000ms: ${avgTime <= 5000 ? '✅' : '❌'} (${avgTime.toFixed(0)}ms)`);
        
        // تقييم نهائي
        const targetsAchieved = [
            avgAccuracy >= 85,
            successRate >= 80,
            avgTime <= 5000
        ].filter(Boolean).length;
        
        let finalGrade;
        if (targetsAchieved === 3) {
            finalGrade = 'A+ (ممتاز) - تم تحقيق جميع الأهداف';
        } else if (targetsAchieved === 2) {
            finalGrade = 'A (جيد جداً) - تم تحقيق معظم الأهداف';
        } else if (targetsAchieved === 1) {
            finalGrade = 'B (جيد) - تحسن ملحوظ';
        } else {
            finalGrade = 'C (مقبول) - يحتاج مزيد من التطوير';
        }
        
        console.log(`\n🏆 التقييم النهائي: ${finalGrade}`);
        console.log(`📊 الأهداف المحققة: ${targetsAchieved}/3`);
        
        // التوصيات
        console.log(`\n💡 التوصيات:`);
        if (avgAccuracy < 85) {
            console.log(`   🔧 تحسين دقة الكشف (حالياً ${avgAccuracy.toFixed(1)}%)`);
        }
        if (successRate < 80) {
            console.log(`   ⚙️ تحسين معدل النجاح (حالياً ${successRate.toFixed(1)}%)`);
        }
        if (avgTime > 5000) {
            console.log(`   ⚡ تحسين الأداء (حالياً ${avgTime.toFixed(0)}ms)`);
        }
        if (targetsAchieved === 3) {
            console.log(`   🎉 التطبيق جاهز للإنتاج!`);
        }
        
        // حفظ النتائج
        const reportData = {
            timestamp: new Date().toISOString(),
            summary: {
                avgAccuracy,
                successRate,
                avgTime,
                targetsAchieved,
                finalGrade
            },
            results: results
        };
        
        fs.writeFileSync('quick_final_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير: quick_final_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            finalGrade,
            targetsAchieved
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await quickFinalTest();
        
        if (results) {
            console.log('\n🎯 الخلاصة:');
            console.log(`   الدقة: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(0)}ms`);
            console.log(`   التقييم: ${results.finalGrade}`);
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { quickFinalTest };
