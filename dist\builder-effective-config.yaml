directories:
  output: dist
  buildResources: build
appId: com.plagiarismchecker.pdfconverter.desktop
productName: نظام كشف الاستلال ومحول PDF إلى DOCX المتقدم
files:
  - filter:
      - main-app.js
      - src/**/*
      - assets/**/*
      - node_modules/**/*
      - package.json
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
  icon: assets/icon.ico
  requestedExecutionLevel: asInvoker
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: Plagiarism Checker Pro
extraResources:
  - from: assets/
    to: assets/
    filter:
      - '**/*'
electronVersion: 28.3.3
