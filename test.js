const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

// إنشاء ملف نصي للاختبار
const testText = `
هذا نص تجريبي لاختبار تطبيق كشف الاستلال. 
يحتوي هذا النص على عدة جمل مختلفة لاختبار قدرة التطبيق على تحليل المحتوى.
في الختام، يمكن القول أن هذا البحث قد توصل إلى نتائج مهمة.
تهدف هذه الدراسة إلى تحليل وفهم الظاهرة المدروسة.
من خلال هذا البحث، تم التوصل إلى عدة استنتاجات مهمة.
`;

const testFilePath = path.join(__dirname, 'test.txt');
fs.writeFileSync(testFilePath, testText, 'utf8');

async function testPlagiarismChecker() {
    console.log('🚀 بدء اختبار كاشف الاستلال...');
    
    try {
        const checker = new PlagiarismChecker();
        
        // تعيين دالة تحديث التقدم
        checker.setProgressCallback((percentage, message) => {
            console.log(`📊 ${percentage}% - ${message}`);
        });
        
        console.log('📄 فحص الملف النصي...');
        const results = await checker.checkFile(testFilePath);
        
        console.log('\n✅ نتائج الفحص:');
        console.log(`📈 نسبة الاستلال: ${results.plagiarismPercentage}%`);
        console.log(`🎯 مستوى الخطر: ${results.riskLevel.label}`);
        console.log(`📝 عدد الكلمات: ${results.statistics.totalWords}`);
        console.log(`⚠️ أجزاء مشكوك بها: ${results.statistics.suspiciousCount}`);
        
        if (results.suspiciousSegments.length > 0) {
            console.log('\n🔍 الأجزاء المشكوك بها:');
            results.suspiciousSegments.forEach((segment, index) => {
                console.log(`${index + 1}. تشابه ${Math.round(segment.similarity * 100)}%: ${segment.text.substring(0, 100)}...`);
            });
        }
        
        if (results.hasAIAnalysis) {
            console.log('\n🤖 تحليل الذكاء الاصطناعي:');
            console.log(`🎯 نتيجة الأصالة: ${results.aiAnalysis.originalityScore}%`);
            console.log(`🔧 المصدر: ${results.aiAnalysis.source}`);
            console.log(`📊 مستوى الثقة: ${results.aiAnalysis.confidence}%`);
        }
        
        console.log('\n💡 التوصيات:');
        results.recommendations.forEach((rec, index) => {
            console.log(`${index + 1}. ${rec}`);
        });
        
        // اختبار حفظ التقرير
        console.log('\n💾 اختبار حفظ التقرير...');
        const reportPath = path.join(__dirname, 'test_report.html');
        const savedPath = await checker.saveReport(results, reportPath);
        console.log(`✅ تم حفظ التقرير: ${savedPath}`);
        
        // تنظيف الملفات المؤقتة
        fs.unlinkSync(testFilePath);
        
        console.log('\n🎉 تم الانتهاء من الاختبار بنجاح!');
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error(error.stack);
    }
}

// تشغيل الاختبار
testPlagiarismChecker();
