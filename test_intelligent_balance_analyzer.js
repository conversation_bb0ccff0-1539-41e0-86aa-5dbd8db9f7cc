const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار محلل التوازن الذكي مع تعلم آلي للوصول لدقة 95%+
 * الهدف النهائي: تحقيق دقة 95%+ على البحوث الثلاثة الحقيقية
 */
async function testIntelligentBalanceAnalyzer() {
    console.log('🧠 اختبار محلل التوازن الذكي مع تعلم آلي للوصول لدقة 95%+');
    console.log('=' .repeat(80));
    console.log('🎯 الهدف النهائي: تحقيق دقة 95%+ على البحوث الثلاثة الحقيقية');
    console.log('🔧 التحسينات المطبقة:');
    console.log('   🧠 نظام تعلم آلي للتنبؤ بنوع النص');
    console.log('   ⚖️ توازن ذكي بين الحساسية والدقة');
    console.log('   📊 تحليل خصائص النص لضبط المعاملات');
    console.log('   🎯 مضاعفات مخصصة لكل نوع نص');
    console.log('   🔍 فحص متوازن مع عينة محدودة لتحسين الأداء');
    console.log('   📈 ضبط تلقائي للمعاملات بناءً على بيانات التدريب');
    console.log('=' .repeat(80));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // البحوث الحقيقية مع النتائج المطلوبة
    const researchData = [
        { 
            file: 'research_low_plagiarism_15percent.txt', 
            expected: 15, 
            previousResult: 95, // من المحلل السابق
            description: 'بحث أصلي منخفض الاستلال',
            targetRange: [12, 18], // نطاق مقبول للدقة 95%+
            difficulty: 'صعب' // صعب لأن النظام السابق أعطى 95%
        },
        { 
            file: 'research_medium_plagiarism_50percent.txt', 
            expected: 50, 
            previousResult: 95, // من المحلل السابق
            description: 'بحث متوسط الاستلال',
            targetRange: [47, 53], // نطاق مقبول للدقة 95%+
            difficulty: 'متوسط'
        },
        { 
            file: 'research_high_plagiarism_85percent.txt', 
            expected: 85, 
            previousResult: 95, // من المحلل السابق
            description: 'بحث عالي الاستلال',
            targetRange: [80, 90], // نطاق مقبول للدقة 95%+
            difficulty: 'سهل' // سهل لأن النتيجة قريبة من المطلوب
        }
    ];
    
    console.log('📊 اختبار محلل التوازن الذكي مع تعلم آلي:');
    
    for (const research of researchData) {
        console.log(`\n📄 اختبار: ${research.file}`);
        console.log(`📋 ${research.description} (صعوبة: ${research.difficulty})`);
        console.log(`🎯 المتوقع: ${research.expected}% | النطاق المقبول: ${research.targetRange[0]}-${research.targetRange[1]}%`);
        console.log(`📊 السابق: ${research.previousResult}% (محلل عالي الدقة)`);
        
        const filePath = path.join(__dirname, 'real-research-tests', research.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص مع محلل التوازن الذكي
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - research.expected);
            const accuracy = Math.max(0, 100 - (difference / research.expected) * 100);
            const improvement = result.plagiarismPercentage - research.previousResult;
            const inTargetRange = result.plagiarismPercentage >= research.targetRange[0] && 
                                result.plagiarismPercentage <= research.targetRange[1];
            const passed = accuracy >= 95; // معيار الدقة 95%+
            
            console.log(`   ✅ النتيجة الذكية المتوازنة: ${result.plagiarismPercentage}%`);
            console.log(`   🎯 في النطاق المقبول: ${inTargetRange ? 'نعم ✅' : 'لا ❌'}`);
            console.log(`   📈 التحسن من المحلل السابق: ${improvement > 0 ? '+' : ''}${improvement}% (من ${research.previousResult}%)`);
            console.log(`   📊 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🚨 الخطر: ${result.riskLevel.label}`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments ? result.suspiciousSegments.length : 0}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (معيار الدقة 95%+)`);
            
            // تحليل تفصيلي لمحلل التوازن الذكي
            if (result.analysis && result.analysis.intelligentBalance) {
                console.log(`   🧠 تحليل محلل التوازن الذكي:`);
                console.log(`      نصوص مفحوصة: ${result.analysis.totalTextsChecked}`);
                console.log(`      النتيجة الخام: ${(result.analysis.rawScore * 100).toFixed(1)}%`);
                console.log(`      النتيجة المتوازنة: ${(result.analysis.balancedScore * 100).toFixed(1)}%`);
                console.log(`      النتيجة النهائية: ${(result.analysis.finalScore * 100).toFixed(1)}%`);
                
                if (result.analysis.predictedType) {
                    const pred = result.analysis.predictedType;
                    console.log(`      التنبؤ الذكي:`);
                    console.log(`        نوع النص: ${pred.type}`);
                    console.log(`        ثقة التنبؤ: ${(pred.confidence * 100).toFixed(1)}%`);
                    console.log(`        استلال متوقع: ${pred.expectedPlagiarism}%`);
                    console.log(`        مضاعف موصى: ${pred.recommendedMultiplier}x`);
                }
                
                if (result.analysis.textCharacteristics) {
                    const chars = result.analysis.textCharacteristics;
                    console.log(`      خصائص النص:`);
                    console.log(`        كثافة أكاديمية: ${(chars.academicDensity * 100).toFixed(1)}%`);
                    console.log(`        نسبة التكرار: ${(chars.repetitionRatio * 100).toFixed(1)}%`);
                    console.log(`        متوسط طول الجملة: ${chars.avgSentenceLength.toFixed(1)} كلمة`);
                    console.log(`        عدد العبارات الأكاديمية: ${chars.academicCount}`);
                }
                
                console.log(`      مطابقات: ${result.analysis.exactMatches}حرفي، ${result.analysis.semanticMatches}دلالي، ${result.analysis.patternMatches}نمط، ${result.analysis.contextMatches}سياق`);
            }
            
            // عرض أمثلة على الأجزاء المكتشفة
            if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
                console.log(`   📝 أمثلة على الأجزاء المكتشفة:`);
                result.suspiciousSegments.slice(0, 2).forEach((segment, index) => {
                    console.log(`      ${index + 1}. "${segment.text.substring(0, 50)}..." (${(segment.similarity * 100).toFixed(1)}%)`);
                });
            }
            
            results.push({
                file: research.file,
                expected: research.expected,
                targetRange: research.targetRange,
                previous: research.previousResult,
                actual: result.plagiarismPercentage,
                improvement: improvement,
                accuracy: accuracy,
                inTargetRange: inTargetRange,
                processingTime: processingTime,
                suspiciousCount: result.suspiciousSegments ? result.suspiciousSegments.length : 0,
                passed: passed,
                difficulty: research.difficulty,
                analysis: result.analysis
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: research.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج النهائية
    console.log('\n' + '=' .repeat(80));
    console.log('📊 تحليل النتائج النهائية - محلل التوازن الذكي مع تعلم آلي');
    console.log('=' .repeat(80));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    const inTargetRangeTests = validResults.filter(r => r.inTargetRange);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgImprovement = validResults.reduce((sum, r) => sum + r.improvement, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        const targetRangeRate = (inTargetRangeTests.length / validResults.length) * 100;
        
        // حساب الدقة النهائية
        const previousAccuracy = 32.7; // من المحلل السابق
        const newAccuracy = avgAccuracy;
        const totalImprovement = newAccuracy - 43.3; // من البداية الأصلية
        
        console.log(`🎯 النتائج النهائية مع محلل التوازن الذكي:`);
        console.log(`   الدقة السابقة: ${previousAccuracy}%`);
        console.log(`   الدقة النهائية: ${newAccuracy.toFixed(1)}%`);
        console.log(`   التحسن من البداية: ${totalImprovement > 0 ? '+' : ''}${totalImprovement.toFixed(1)}%`);
        console.log(`   معدل تحقيق دقة 95%+: ${successRate.toFixed(1)}%`);
        console.log(`   معدل الوصول للنطاق المقبول: ${targetRangeRate.toFixed(1)}%`);
        console.log(`   متوسط وقت المعالجة: ${(avgTime/1000).toFixed(1)}s`);
        
        // تقييم نجاح الهدف النهائي
        const ultimateSuccess = successRate >= 100; // جميع الاختبارات تحقق دقة 95%+
        const partialSuccess = targetRangeRate >= 66.7; // 2/3 على الأقل في النطاق المقبول
        const significantImprovement = newAccuracy >= 80; // تحسن كبير
        
        console.log(`\n🎯 تقييم تحقيق الهدف النهائي (دقة 95%+):`);
        console.log(`   دقة 95%+ لجميع البحوث: ${ultimateSuccess ? '✅' : '❌'} (${passedTests.length}/3)`);
        console.log(`   في النطاق المقبول: ${partialSuccess ? '✅' : '❌'} (${inTargetRangeTests.length}/3)`);
        console.log(`   متوسط الدقة 95%+: ${newAccuracy >= 95 ? '✅' : '❌'} (${newAccuracy.toFixed(1)}%)`);
        console.log(`   تحسن كبير (80%+): ${significantImprovement ? '✅' : '❌'} (${newAccuracy.toFixed(1)}%)`);
        console.log(`   الهدف النهائي محقق: ${ultimateSuccess ? '✅' : '❌'}`);
        
        // تفاصيل كل بحث
        console.log(`\n📋 تفاصيل النتائج النهائية لكل بحث:`);
        validResults.forEach(result => {
            const improvementStatus = result.improvement > 0 ? '📈 تحسن' : 
                                    result.improvement < 0 ? '📉 تحسن كبير' : '➡️ ثابت';
            const accuracyGrade = result.accuracy >= 95 ? '🎯 ممتاز (95%+)' : 
                                result.accuracy >= 90 ? '🌟 ممتاز جداً (90%+)' :
                                result.accuracy >= 80 ? '✅ جيد جداً (80%+)' : 
                                result.accuracy >= 70 ? '📈 جيد (70%+)' : '⚠️ يحتاج عمل';
            const rangeStatus = result.inTargetRange ? '🎯 في النطاق' : '❌ خارج النطاق';
            const difficultyIcon = result.difficulty === 'صعب' ? '🔴' : 
                                  result.difficulty === 'متوسط' ? '🟡' : '🟢';
            
            console.log(`   ${result.file} ${difficultyIcon}:`);
            console.log(`      النتيجة: ${result.actual}% (متوقع: ${result.expected}%) ${rangeStatus}`);
            console.log(`      الدقة: ${result.accuracy.toFixed(1)}% ${accuracyGrade}`);
            console.log(`      التحسن: ${result.improvement > 0 ? '+' : ''}${result.improvement}% ${improvementStatus}`);
            console.log(`      الأجزاء المشبوهة: ${result.suspiciousCount}`);
            console.log(`      صعوبة الاختبار: ${result.difficulty} ${difficultyIcon}`);
        });
        
        // تحليل فعالية التعلم الآلي
        console.log(`\n🧠 تحليل فعالية التعلم الآلي والتوازن الذكي:`);
        
        const avgRawScore = validResults.reduce((sum, r) => sum + (r.analysis?.rawScore || 0), 0) / validResults.length;
        const avgBalancedScore = validResults.reduce((sum, r) => sum + (r.analysis?.balancedScore || 0), 0) / validResults.length;
        const avgFinalScore = validResults.reduce((sum, r) => sum + (r.analysis?.finalScore || 0), 0) / validResults.length;
        
        console.log(`   متوسط النتيجة الخام: ${(avgRawScore * 100).toFixed(1)}%`);
        console.log(`   متوسط النتيجة المتوازنة: ${(avgBalancedScore * 100).toFixed(1)}%`);
        console.log(`   متوسط النتيجة النهائية: ${(avgFinalScore * 100).toFixed(1)}%`);
        console.log(`   فعالية التوازن: ${((avgBalancedScore / avgRawScore - 1) * 100).toFixed(1)}% تحسن`);
        console.log(`   فعالية الضبط النهائي: ${((avgFinalScore / avgBalancedScore - 1) * 100).toFixed(1)}% تحسن`);
        
        // تحليل دقة التنبؤ
        const predictions = validResults.filter(r => r.analysis?.predictedType);
        if (predictions.length > 0) {
            const avgConfidence = predictions.reduce((sum, r) => sum + r.analysis.predictedType.confidence, 0) / predictions.length;
            console.log(`   متوسط ثقة التنبؤ: ${(avgConfidence * 100).toFixed(1)}%`);
            
            predictions.forEach(result => {
                const pred = result.analysis.predictedType;
                const predictionAccuracy = Math.max(0, 100 - Math.abs(pred.expectedPlagiarism - result.expected) / result.expected * 100);
                console.log(`   تنبؤ ${result.file}: ${pred.expectedPlagiarism}% (فعلي: ${result.expected}%) - دقة: ${predictionAccuracy.toFixed(1)}%`);
            });
        }
        
        // التقييم النهائي الشامل
        let finalAssessment;
        let achievementLevel;
        
        if (ultimateSuccess) {
            finalAssessment = '🎉 نجح تماماً! تم تحقيق دقة 95%+ لجميع البحوث!';
            achievementLevel = 'مثالي';
        } else if (partialSuccess && newAccuracy >= 90) {
            finalAssessment = '🎯 نجح بامتياز! معظم البحوث حققت دقة عالية جداً';
            achievementLevel = 'ممتاز';
        } else if (significantImprovement) {
            finalAssessment = '✅ نجح بشكل جيد! دقة عالية لكن تحتاج تحسينات طفيفة';
            achievementLevel = 'جيد جداً';
        } else if (newAccuracy >= 70) {
            finalAssessment = '📈 نجح جزئياً! تحسن كبير لكن يحتاج مزيد من العمل';
            achievementLevel = 'جيد';
        } else {
            finalAssessment = '⚠️ لم ينجح! يحتاج إعادة تقييم النهج بالكامل';
            achievementLevel = 'ضعيف';
        }
        
        console.log(`\n🏆 التقييم النهائي الشامل: ${finalAssessment}`);
        console.log(`📊 مستوى الإنجاز: ${achievementLevel}`);
        
        // حفظ التقرير النهائي
        const intelligentBalanceReport = {
            timestamp: new Date().toISOString(),
            test_type: 'intelligent_balance_analyzer_test',
            goal: 'تحقيق دقة 95%+ على البحوث الثلاثة الحقيقية باستخدام التعلم الآلي والتوازن الذكي',
            results: {
                avgAccuracy: newAccuracy,
                successRate: successRate,
                targetRangeRate: targetRangeRate,
                avgTime: avgTime,
                totalImprovement: totalImprovement,
                ultimateSuccess: ultimateSuccess,
                partialSuccess: partialSuccess,
                significantImprovement: significantImprovement
            },
            detailed_results: results,
            ml_analysis: {
                avgRawScore: avgRawScore,
                avgBalancedScore: avgBalancedScore,
                avgFinalScore: avgFinalScore,
                balanceEffectiveness: (avgBalancedScore / avgRawScore - 1) * 100,
                finalAdjustmentEffectiveness: (avgFinalScore / avgBalancedScore - 1) * 100,
                avgPredictionConfidence: predictions.length > 0 ? predictions.reduce((sum, r) => sum + r.analysis.predictedType.confidence, 0) / predictions.length : 0
            },
            final_assessment: finalAssessment,
            achievement_level: achievementLevel,
            goal_achieved: ultimateSuccess
        };
        
        fs.writeFileSync('intelligent_balance_analyzer_report.json', JSON.stringify(intelligentBalanceReport, null, 2));
        console.log(`\n💾 تم حفظ التقرير النهائي: intelligent_balance_analyzer_report.json`);
        
        return {
            success: ultimateSuccess,
            partialSuccess: partialSuccess,
            significantImprovement: significantImprovement,
            avgAccuracy: newAccuracy,
            successRate: successRate,
            targetRangeRate: targetRangeRate,
            totalImprovement: totalImprovement,
            finalAssessment: finalAssessment,
            achievementLevel: achievementLevel,
            detailedResults: results
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار النهائي
async function main() {
    try {
        const results = await testIntelligentBalanceAnalyzer();
        
        if (results) {
            console.log('\n🎯 الخلاصة النهائية الشاملة:');
            console.log(`   تحقيق الهدف النهائي: ${results.success ? 'نعم ✅' : 'لا ❌'}`);
            console.log(`   نجاح جزئي: ${results.partialSuccess ? 'نعم ✅' : 'لا ❌'}`);
            console.log(`   تحسن كبير: ${results.significantImprovement ? 'نعم ✅' : 'لا ❌'}`);
            console.log(`   الدقة النهائية: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل تحقيق دقة 95%+: ${results.successRate.toFixed(1)}%`);
            console.log(`   معدل النطاق المقبول: ${results.targetRangeRate.toFixed(1)}%`);
            console.log(`   التحسن الإجمالي: ${results.totalImprovement > 0 ? '+' : ''}${results.totalImprovement.toFixed(1)}%`);
            console.log(`   مستوى الإنجاز: ${results.achievementLevel}`);
            console.log(`   التقييم النهائي: ${results.finalAssessment}`);
            
            if (results.success) {
                console.log('\n🎉 مبروك! تم تحقيق الهدف النهائي - دقة 95%+ لجميع البحوث!');
            } else if (results.partialSuccess) {
                console.log('\n🎯 نجاح جزئي ممتاز! معظم البحوث حققت دقة عالية');
            } else if (results.significantImprovement) {
                console.log('\n✅ تحسن كبير! النظام يعمل بشكل جيد');
            } else {
                console.log('\n🔧 يحتاج مزيد من التطوير لتحقيق الهدف النهائي');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار النهائي:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testIntelligentBalanceAnalyzer };
