const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار النظام النهائي البسيط والدقيق للوصول لدقة 95%+
 */
async function testFinalPreciseSystem() {
    console.log('🎯 اختبار النظام النهائي البسيط والدقيق للوصول لدقة 95%+');
    console.log('=' .repeat(80));
    console.log('🔧 النهج النهائي:');
    console.log('   🎯 محلل بسيط ودقيق يركز على الأساسيات');
    console.log('   ⚖️ أوزان محسنة: Exact 60%, Fuzzy 30%, Context 10%');
    console.log('   🚀 تضخيم للحساسية العالية (1.8x) للنتائج فوق 40%');
    console.log('   💎 مكافأة للتطابق الحرفي (+30%) والعبارات الطويلة');
    console.log('   📊 قاعدة بيانات محسنة: 198 عبارة مرجعية');
    console.log('   🎯 الهدف: دقة 95%+ ومعدل نجاح 90%+ وأداء < 1000ms');
    console.log('=' .repeat(80));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار المعايرة
    const testFiles = [
        { file: 'test_calibrated_20_percent.txt', expected: 20, description: 'نص مع 20% عبارات مرجعية' },
        { file: 'test_calibrated_60_percent.txt', expected: 60, description: 'نص مع 60% عبارات مرجعية' },
        { file: 'test_calibrated_90_percent.txt', expected: 90, description: 'نص مع 90% عبارات مرجعية' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 95; // معيار عالي جداً للنظام النهائي
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments ? result.suspiciousSegments.length : 0}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            // عرض تفاصيل التحليل البسيط والدقيق
            if (result.analysis && result.analysis.simplePrecise) {
                console.log(`   🎯 تحليل بسيط ودقيق:`);
                console.log(`      أقصى تشابه: ${(result.analysis.maxSimilarity * 100).toFixed(1)}%`);
                console.log(`      متوسط التشابه: ${(result.analysis.avgSimilarity * 100).toFixed(1)}%`);
                console.log(`      تطابق حرفي: ${result.analysis.exactMatches} مطابقة`);
                console.log(`      النتيجة النهائية: ${(result.analysis.finalScore * 100).toFixed(1)}%`);
                console.log(`      إجمالي المراجع: ${result.analysis.totalReferences}`);
            }
            
            // عرض أمثلة على الأجزاء المشبوهة
            if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
                console.log(`   📝 أمثلة على الأجزاء المكتشفة:`);
                result.suspiciousSegments.slice(0, 2).forEach((segment, index) => {
                    console.log(`      ${index + 1}. [${segment.type}] "${segment.text.substring(0, 60)}..." (${(segment.similarity * 100).toFixed(1)}%)`);
                });
            }
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                maxSimilarity: result.analysis ? result.analysis.maxSimilarity : 0,
                avgSimilarity: result.analysis ? result.analysis.avgSimilarity : 0,
                exactMatches: result.analysis ? result.analysis.exactMatches : 0,
                finalScore: result.analysis ? result.analysis.finalScore : 0,
                suspiciousCount: result.suspiciousSegments ? result.suspiciousSegments.length : 0,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            console.error(error.stack);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج النهائية
    console.log('\n' + '=' .repeat(80));
    console.log('📊 تحليل النتائج النهائية للنظام البسيط والدقيق');
    console.log('=' .repeat(80));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgMaxSimilarity = validResults.reduce((sum, r) => sum + r.maxSimilarity, 0) / validResults.length;
        const avgExactMatches = validResults.reduce((sum, r) => sum + r.exactMatches, 0) / validResults.length;
        const avgFinalScore = validResults.reduce((sum, r) => sum + r.finalScore, 0) / validResults.length;
        const avgSuspiciousCount = validResults.reduce((sum, r) => sum + r.suspiciousCount, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`🎯 النتائج النهائية مع النظام البسيط والدقيق:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (الهدف: 95%+)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (الهدف: 90%+)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms (الهدف: <1000ms)`);
        console.log(`   متوسط أقصى تشابه: ${(avgMaxSimilarity * 100).toFixed(1)}%`);
        console.log(`   متوسط التطابق الحرفي: ${avgExactMatches.toFixed(1)} مطابقة`);
        console.log(`   متوسط النتيجة النهائية: ${(avgFinalScore * 100).toFixed(1)}%`);
        console.log(`   متوسط الأجزاء المشبوهة: ${avgSuspiciousCount.toFixed(1)} جزء`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 51.7;
        const successImprovement = successRate - 33.3;
        
        console.log(`\n📊 التحسن الإجمالي المحقق:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تقييم تحقيق الأهداف النهائية
        console.log(`\n🎯 تقييم تحقيق الأهداف النهائية:`);
        console.log(`   دقة 95%+: ${avgAccuracy >= 95 ? '✅' : '❌'} (${avgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 90%+: ${successRate >= 90 ? '✅' : '❌'} (${successRate.toFixed(1)}%)`);
        console.log(`   أداء < 1000ms: ${avgTime < 1000 ? '✅' : '❌'} (${avgTime.toFixed(0)}ms)`);
        
        const targetsAchieved = [
            avgAccuracy >= 95,
            successRate >= 90,
            avgTime < 1000
        ].filter(Boolean).length;
        
        console.log(`   الأهداف المحققة: ${targetsAchieved}/3`);
        
        // تقييم نهائي شامل
        let finalAssessment;
        if (targetsAchieved === 3) {
            finalAssessment = '🎉 نجح تماماً! تم تحقيق جميع الأهداف المطلوبة';
        } else if (avgAccuracy >= 95) {
            finalAssessment = '🎯 نجح في الدقة! الهدف الأساسي محقق';
        } else if (avgAccuracy >= 90 && successRate >= 80) {
            finalAssessment = '✅ نجح بامتياز! قريب جداً من جميع الأهداف';
        } else if (avgAccuracy >= 80 && successRate >= 60) {
            finalAssessment = '📈 نجح بشكل جيد! تحسن كبير ومقبول';
        } else if (avgAccuracy >= 70 && successRate >= 40) {
            finalAssessment = '🔧 نجح جزئياً! تحسن ملحوظ لكن يحتاج عمل';
        } else {
            finalAssessment = '⚠️ لم ينجح! يحتاج إعادة تقييم النهج';
        }
        
        console.log(`\n🏆 التقييم النهائي الشامل: ${finalAssessment}`);
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج النهائية:`);
        validResults.forEach(result => {
            const grade = result.accuracy >= 95 ? '🎯 ممتاز (95%+)' : 
                         result.accuracy >= 90 ? '🌟 ممتاز جداً (90%+)' :
                         result.accuracy >= 85 ? '✅ جيد جداً (85%+)' : 
                         result.accuracy >= 70 ? '📈 جيد (70%+)' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${grade}`);
            console.log(`      🎯 تفاصيل: ${result.exactMatches} تطابق حرفي، ${result.suspiciousCount} جزء مشبوه`);
        });
        
        // تحليل فعالية النهج البسيط
        console.log(`\n🎯 تحليل فعالية النهج البسيط والدقيق:`);
        console.log(`   متوسط التطابق الحرفي: ${avgExactMatches.toFixed(1)} مطابقة/ملف`);
        console.log(`   متوسط الأجزاء المكتشفة: ${avgSuspiciousCount.toFixed(1)} جزء/ملف`);
        console.log(`   فعالية الكشف: ${((avgExactMatches + avgSuspiciousCount) / 2).toFixed(1)}`);
        console.log(`   كفاءة الأداء: ${avgTime < 500 ? 'ممتازة' : avgTime < 1000 ? 'جيدة' : 'مقبولة'}`);
        
        // حفظ التقرير النهائي الشامل
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'final_precise_system_test',
            final_approach: {
                analyzer: 'SimplePreciseAnalyzer',
                focus: 'simplicity_and_precision',
                weights: { exact: 60, fuzzy: 30, context: 10 },
                sensitivity_boost: 1.8,
                exact_match_bonus: 0.3,
                database_size: 198
            },
            final_results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgMaxSimilarity: avgMaxSimilarity * 100,
                avgExactMatches: avgExactMatches,
                avgFinalScore: avgFinalScore * 100,
                avgSuspiciousCount: avgSuspiciousCount,
                targetsAchieved: targetsAchieved,
                finalAssessment: finalAssessment,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results,
            conclusion: {
                goals_achieved: targetsAchieved === 3,
                primary_goal_achieved: avgAccuracy >= 95,
                ready_for_production: avgAccuracy >= 90 && successRate >= 80,
                recommendation: targetsAchieved >= 2 ? 'نظام ناجح وجاهز للاستخدام' : 'يحتاج مزيد من التطوير'
            }
        };
        
        fs.writeFileSync('final_precise_system_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير النهائي الشامل: final_precise_system_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            targetsAchieved,
            finalAssessment,
            improvement: accuracyImprovement,
            readyForProduction: avgAccuracy >= 90 && successRate >= 80
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار النهائي
async function main() {
    try {
        const results = await testFinalPreciseSystem();
        
        if (results) {
            console.log('\n🎯 الخلاصة النهائية الشاملة:');
            console.log(`   الدقة النهائية: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن الإجمالي: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(0)}ms`);
            console.log(`   الأهداف المحققة: ${results.targetsAchieved}/3`);
            console.log(`   جاهز للإنتاج: ${results.readyForProduction ? 'نعم ✅' : 'لا ❌'}`);
            console.log(`   التقييم النهائي: ${results.finalAssessment}`);
            
            if (results.targetsAchieved === 3) {
                console.log('\n🎉 مبروك! تم تحقيق جميع الأهداف المطلوبة! النظام جاهز للإنتاج');
            } else if (results.avgAccuracy >= 95) {
                console.log('\n🎯 ممتاز! تم تحقيق الهدف الأساسي (دقة 95%+)');
            } else if (results.readyForProduction) {
                console.log('\n✅ نجح! النظام جاهز للاستخدام العملي');
            } else if (results.improvement > 20) {
                console.log('\n📈 تحسن كبير! النظام في الاتجاه الصحيح');
            } else {
                console.log('\n🔧 يحتاج مزيد من التطوير');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار النهائي:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testFinalPreciseSystem };
