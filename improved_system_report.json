{"timestamp": "2025-07-05T01:08:07.832Z", "test_type": "improved_system_test", "improvements": {"database_size": 198, "configurable_analyzer": true, "optimized_weights": {"jaccard": 0.45, "cosine": 0.4, "levenshtein": 0.12, "semantic": 0.03}, "adaptive_thresholds": {"high": 0.45, "medium": 0.3, "low": 0.18}}, "results": {"avgAccuracy": 49.**************, "successRate": 33.33333333333333, "avgTime": 448.3333333333333, "avgMaxSimilarity": null, "avgReferencesChecked": null, "targetsAchieved": 1, "improvement": {"accuracy": -2.255555555555553, "successRate": 0.03333333333333144}}, "detailed_results": [{"file": "test_calibrated_20_percent.txt", "expected": 20, "actual": 19, "accuracy": 95, "processingTime": 346, "passed": true}, {"file": "test_calibrated_60_percent.txt", "expected": 60, "actual": 20, "accuracy": 33.33333333333334, "processingTime": 373, "passed": false}, {"file": "test_calibrated_90_percent.txt", "expected": 90, "actual": 18, "accuracy": 20, "processingTime": 626, "passed": false}]}