const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * تحليل عميق للنتائج الحالية لتحديد نقاط الضعف
 */
async function deepAnalysisCurrentResults() {
    console.log('🔍 تحليل عميق للنتائج الحالية وتحديد نقاط الضعف');
    console.log('=' .repeat(80));
    
    const checker = new PlagiarismChecker();
    
    // البحوث الحقيقية مع النتائج المتوقعة والفعلية
    const researchData = [
        { 
            file: 'research_low_plagiarism_15percent.txt', 
            expected: 15, 
            actual: 5,  // من النتائج السابقة
            description: 'بحث أصلي منخفض الاستلال' 
        },
        { 
            file: 'research_medium_plagiarism_50percent.txt', 
            expected: 50, 
            actual: 30, // من النتائج السابقة
            description: 'بحث متوسط الاستلال' 
        },
        { 
            file: 'research_high_plagiarism_85percent.txt', 
            expected: 85, 
            actual: 31, // من النتائج السابقة
            description: 'بحث عالي الاستلال' 
        }
    ];
    
    console.log('📊 تحليل الفجوات في الدقة:');
    let totalGap = 0;
    
    for (const research of researchData) {
        const gap = Math.abs(research.actual - research.expected);
        const accuracyGap = (gap / research.expected) * 100;
        totalGap += accuracyGap;
        
        console.log(`\n📄 ${research.file}:`);
        console.log(`   المتوقع: ${research.expected}%`);
        console.log(`   الفعلي: ${research.actual}%`);
        console.log(`   الفجوة: ${gap}% (${accuracyGap.toFixed(1)}% خطأ نسبي)`);
        
        // تحليل نوع المشكلة
        if (research.actual < research.expected) {
            console.log(`   المشكلة: النظام محافظ جداً - لا يكشف الاستلال بشكل كافٍ`);
        } else {
            console.log(`   المشكلة: النظام حساس جداً - يعطي نتائج مرتفعة خاطئة`);
        }
    }
    
    const avgGap = totalGap / researchData.length;
    console.log(`\n📈 متوسط الخطأ النسبي: ${avgGap.toFixed(1)}%`);
    console.log(`📉 الدقة الحالية: ${(100 - avgGap).toFixed(1)}%`);
    console.log(`🎯 الفجوة للوصول لدقة 95%: ${(95 - (100 - avgGap)).toFixed(1)}%`);
    
    // تحليل المشاكل الجذرية
    console.log('\n🔍 تحليل المشاكل الجذرية:');
    
    // 1. مشكلة التوازن
    console.log('\n1️⃣ مشكلة التوازن:');
    console.log('   - النظام الحالي محافظ جداً (يعطي نتائج منخفضة)');
    console.log('   - خاصة للبحوث عالية الاستلال (31% بدلاً من 85%)');
    console.log('   - الحاجة لزيادة الحساسية بشكل ذكي');
    
    // 2. مشكلة العتبات
    console.log('\n2️⃣ مشكلة العتبات:');
    console.log('   - العتبات الحالية منخفضة جداً');
    console.log('   - لا تميز بين مستويات الاستلال المختلفة');
    console.log('   - الحاجة لعتبات تكيفية حسب نوع النص');
    
    // 3. مشكلة قاعدة البيانات
    console.log('\n3️⃣ مشكلة قاعدة البيانات:');
    console.log('   - قاعدة البيانات محدودة (8 عبارات فقط في الاحتياطية)');
    console.log('   - عدم تحميل قاعدة البيانات الأساسية بشكل صحيح');
    console.log('   - الحاجة لقاعدة بيانات أكبر وأكثر تنوعاً');
    
    // 4. مشكلة الأوزان
    console.log('\n4️⃣ مشكلة الأوزان:');
    console.log('   - الأوزان الحالية لا تعكس أهمية كل نوع تطابق');
    console.log('   - التطابق الحرفي له وزن منخفض جداً');
    console.log('   - الحاجة لأوزان ديناميكية حسب السياق');
    
    // 5. مشكلة المعايرة
    console.log('\n5️⃣ مشكلة المعايرة:');
    console.log('   - معاملات التصحيح تقلل النتائج بشكل مفرط');
    console.log('   - لا توجد معايرة مناسبة للنصوص عالية الاستلال');
    console.log('   - الحاجة لنظام معايرة أكثر ذكاءً');
    
    // تحليل تفصيلي لكل بحث
    console.log('\n📋 تحليل تفصيلي لكل بحث:');
    
    for (const research of researchData) {
        console.log(`\n📄 ${research.description}:`);
        
        const filePath = path.join(__dirname, 'real-research-tests', research.file);
        if (fs.existsSync(filePath)) {
            const content = fs.readFileSync(filePath, 'utf8');
            const wordCount = content.split(/\s+/).length;
            const sentences = content.split(/[.!?؟]/).length;
            
            console.log(`   📊 إحصائيات النص:`);
            console.log(`      عدد الكلمات: ${wordCount}`);
            console.log(`      عدد الجمل: ${sentences}`);
            console.log(`      متوسط طول الجملة: ${(wordCount/sentences).toFixed(1)} كلمة`);
            
            // تحليل محتوى النص
            const academicPhrases = [
                'منهجية البحث', 'اعتمدت الدراسة', 'المنهج الوصفي',
                'تشير النتائج', 'يوصي الباحث', 'في ضوء ما تقدم',
                'بناءً على النتائج', 'أظهرت النتائج'
            ];
            
            let foundPhrases = 0;
            academicPhrases.forEach(phrase => {
                if (content.toLowerCase().includes(phrase)) {
                    foundPhrases++;
                }
            });
            
            console.log(`   🔍 العبارات الأكاديمية المكتشفة: ${foundPhrases}/${academicPhrases.length}`);
            console.log(`   📈 كثافة العبارات الأكاديمية: ${((foundPhrases/academicPhrases.length)*100).toFixed(1)}%`);
            
            // تحليل سبب عدم الدقة
            if (research.expected === 15 && research.actual === 5) {
                console.log(`   ❌ المشكلة: النظام لا يكشف العبارات الأكاديمية الطبيعية`);
                console.log(`   💡 الحل المقترح: تقليل العتبات للنصوص منخفضة الاستلال`);
            } else if (research.expected === 50 && research.actual === 30) {
                console.log(`   ❌ المشكلة: النظام لا يقدر التكرار المتوسط بشكل صحيح`);
                console.log(`   💡 الحل المقترح: تحسين كشف الأنماط المتكررة`);
            } else if (research.expected === 85 && research.actual === 31) {
                console.log(`   ❌ المشكلة: النظام يفشل في كشف الاستلال العالي`);
                console.log(`   💡 الحل المقترح: زيادة الحساسية وتحسين التطابق الحرفي`);
            }
        }
    }
    
    // خطة التحسين المقترحة
    console.log('\n🚀 خطة التحسين المقترحة (بالأولوية):');
    console.log('\n1️⃣ إصلاح قاعدة البيانات المرجعية:');
    console.log('   - إصلاح مشكلة تحميل قاعدة البيانات');
    console.log('   - إضافة 500+ عبارة أكاديمية متنوعة');
    console.log('   - تصنيف العبارات حسب مستوى الاستلال');
    
    console.log('\n2️⃣ تطوير نظام عتبات ذكي:');
    console.log('   - عتبات تكيفية حسب نوع النص');
    console.log('   - عتبات مختلفة لكل مستوى استلال');
    console.log('   - نظام تعلم من النتائج');
    
    console.log('\n3️⃣ تحسين الأوزان والمعايرة:');
    console.log('   - زيادة وزن التطابق الحرفي');
    console.log('   - أوزان ديناميكية حسب السياق');
    console.log('   - إلغاء معاملات التصحيح المفرطة');
    
    console.log('\n4️⃣ تطوير خوارزمية هجينة جديدة:');
    console.log('   - دمج أفضل ما في جميع المحللات');
    console.log('   - نظام تصويت ذكي');
    console.log('   - معايرة تلقائية للمعاملات');
    
    console.log('\n5️⃣ تطبيق تقنيات AI متقدمة:');
    console.log('   - استخدام نماذج NLP جاهزة');
    console.log('   - تحليل دلالي متقدم');
    console.log('   - تعلم آلي للتحسين التلقائي');
    
    // حفظ التحليل
    const analysisData = {
        timestamp: new Date().toISOString(),
        currentAccuracy: 100 - avgGap,
        targetAccuracy: 95,
        accuracyGap: 95 - (100 - avgGap),
        researchAnalysis: researchData.map(r => ({
            ...r,
            gap: Math.abs(r.actual - r.expected),
            relativeError: (Math.abs(r.actual - r.expected) / r.expected) * 100
        })),
        rootCauses: [
            'مشكلة التوازن - النظام محافظ جداً',
            'مشكلة العتبات - عتبات منخفضة جداً',
            'مشكلة قاعدة البيانات - محدودة وغير محملة بشكل صحيح',
            'مشكلة الأوزان - لا تعكس الأهمية الحقيقية',
            'مشكلة المعايرة - معاملات تصحيح مفرطة'
        ],
        improvementPlan: [
            'إصلاح قاعدة البيانات المرجعية',
            'تطوير نظام عتبات ذكي',
            'تحسين الأوزان والمعايرة',
            'تطوير خوارزمية هجينة جديدة',
            'تطبيق تقنيات AI متقدمة'
        ]
    };
    
    fs.writeFileSync('deep_analysis_report.json', JSON.stringify(analysisData, null, 2));
    console.log('\n💾 تم حفظ تحليل عميق: deep_analysis_report.json');
    
    return analysisData;
}

// تشغيل التحليل
async function main() {
    try {
        const analysis = await deepAnalysisCurrentResults();
        
        console.log('\n🎯 خلاصة التحليل العميق:');
        console.log(`   الدقة الحالية: ${analysis.currentAccuracy.toFixed(1)}%`);
        console.log(`   الدقة المطلوبة: ${analysis.targetAccuracy}%`);
        console.log(`   الفجوة المطلوب سدها: ${analysis.accuracyGap.toFixed(1)}%`);
        console.log(`   عدد المشاكل الجذرية: ${analysis.rootCauses.length}`);
        console.log(`   عدد التحسينات المقترحة: ${analysis.improvementPlan.length}`);
        
        console.log('\n🚀 الخطوة التالية: تطوير استراتيجية التحسين المتدرج');
        
    } catch (error) {
        console.error('❌ خطأ في التحليل العميق:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { deepAnalysisCurrentResults };
