const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار تأثير توسيع قاعدة البيانات المرجعية
 */
async function testExpandedDatabase() {
    console.log('📚 اختبار تأثير توسيع قاعدة البيانات المرجعية');
    console.log('=' .repeat(70));
    console.log('🔧 التحسين المطبق:');
    console.log('   📈 توسيع قاعدة البيانات من 130 إلى 410+ عبارة مرجعية');
    console.log('   📊 زيادة بنسبة 215% في حجم قاعدة البيانات');
    console.log('   🎯 التحسن المتوقع: +20-30% في الدقة');
    console.log('=' .repeat(70));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار المعايرة
    const testFiles = [
        { file: 'test_calibrated_20_percent.txt', expected: 20, description: 'نص مع 20% عبارات مرجعية' },
        { file: 'test_calibrated_60_percent.txt', expected: 60, description: 'نص مع 60% عبارات مرجعية' },
        { file: 'test_calibrated_90_percent.txt', expected: 90, description: 'نص مع 90% عبارات مرجعية' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 80; // معيار عالي
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   ⚙️ العتبة: ${(result.analysis.adaptiveThreshold * 100).toFixed(1)}%`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            // عرض تفاصيل التشابه إذا كانت متاحة
            if (result.analysis && result.analysis.similarities) {
                console.log(`   🔬 تفاصيل التشابه:`);
                console.log(`      Jaccard: ${(result.analysis.similarities.jaccard * 100).toFixed(1)}%`);
                console.log(`      Cosine: ${(result.analysis.similarities.cosine * 100).toFixed(1)}%`);
                console.log(`      Levenshtein: ${(result.analysis.similarities.levenshtein * 100).toFixed(1)}%`);
                console.log(`      Semantic: ${(result.analysis.similarities.semantic * 100).toFixed(1)}%`);
                console.log(`      Exact Match: ${(result.analysis.similarities.exactMatch * 100).toFixed(1)}%`);
            }
            
            // عرض أمثلة على الأجزاء المشبوهة
            if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
                console.log(`   📝 أمثلة على الأجزاء المشبوهة:`);
                result.suspiciousSegments.slice(0, 3).forEach((segment, index) => {
                    console.log(`      ${index + 1}. "${segment.text.substring(0, 50)}..." (${(segment.similarity * 100).toFixed(1)}%)`);
                });
            }
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                threshold: result.analysis.adaptiveThreshold,
                suspiciousCount: result.suspiciousSegments.length,
                similarities: result.analysis.similarities,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج
    console.log('\n' + '=' .repeat(70));
    console.log('📊 تحليل تأثير توسيع قاعدة البيانات');
    console.log('=' .repeat(70));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgThreshold = validResults.reduce((sum, r) => sum + r.threshold, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`📈 النتائج مع قاعدة البيانات الموسعة:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (كان 51.7%)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (كان 33.3%)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
        console.log(`   متوسط العتبة: ${(avgThreshold * 100).toFixed(1)}%`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 51.7;
        const successImprovement = successRate - 33.3;
        
        console.log(`\n📊 التحسن المحقق:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تحليل متوسط التشابه
        if (validResults[0] && validResults[0].similarities) {
            console.log(`\n🔬 متوسط قيم التشابه:`);
            const avgSimilarities = {
                jaccard: validResults.reduce((sum, r) => sum + (r.similarities?.jaccard || 0), 0) / validResults.length,
                cosine: validResults.reduce((sum, r) => sum + (r.similarities?.cosine || 0), 0) / validResults.length,
                levenshtein: validResults.reduce((sum, r) => sum + (r.similarities?.levenshtein || 0), 0) / validResults.length,
                semantic: validResults.reduce((sum, r) => sum + (r.similarities?.semantic || 0), 0) / validResults.length,
                exactMatch: validResults.reduce((sum, r) => sum + (r.similarities?.exactMatch || 0), 0) / validResults.length
            };
            
            console.log(`   Jaccard: ${(avgSimilarities.jaccard * 100).toFixed(1)}%`);
            console.log(`   Cosine: ${(avgSimilarities.cosine * 100).toFixed(1)}%`);
            console.log(`   Levenshtein: ${(avgSimilarities.levenshtein * 100).toFixed(1)}%`);
            console.log(`   Semantic: ${(avgSimilarities.semantic * 100).toFixed(1)}%`);
            console.log(`   Exact Match: ${(avgSimilarities.exactMatch * 100).toFixed(1)}%`);
        }
        
        // تقييم التحسن
        if (avgAccuracy >= 90 && successRate >= 80) {
            console.log(`\n🎉 تحسن ممتاز! قاعدة البيانات الموسعة فعالة جداً`);
        } else if (avgAccuracy >= 75 && successRate >= 60) {
            console.log(`\n✅ تحسن كبير! قاعدة البيانات الموسعة في الاتجاه الصحيح`);
        } else if (avgAccuracy > 51.7) {
            console.log(`\n📈 تحسن ملحوظ! قاعدة البيانات الموسعة تحسن الدقة`);
        } else {
            console.log(`\n⚠️ تحسن محدود! قد نحتاج تحسينات إضافية`);
        }
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج:`);
        validResults.forEach(result => {
            const grade = result.accuracy >= 90 ? '🎯 ممتاز' : 
                         result.accuracy >= 75 ? '✅ جيد جداً' : 
                         result.accuracy >= 60 ? '📈 جيد' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${grade}`);
        });
        
        // تقييم تحقيق الأهداف
        console.log(`\n🎯 تقييم تحقيق الأهداف:`);
        console.log(`   دقة 95%+: ${avgAccuracy >= 95 ? '✅' : '❌'} (${avgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 90%+: ${successRate >= 90 ? '✅' : '❌'} (${successRate.toFixed(1)}%)`);
        console.log(`   أداء < 1000ms: ${avgTime < 1000 ? '✅' : '❌'} (${avgTime.toFixed(0)}ms)`);
        
        const targetsAchieved = [
            avgAccuracy >= 95,
            successRate >= 90,
            avgTime < 1000
        ].filter(Boolean).length;
        
        console.log(`   الأهداف المحققة: ${targetsAchieved}/3`);
        
        // حفظ النتائج
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'expanded_database_test',
            database_expansion: {
                old_size: 130,
                new_size: '410+',
                increase_percentage: 215
            },
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgThreshold: avgThreshold * 100,
                targetsAchieved: targetsAchieved,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results
        };
        
        fs.writeFileSync('expanded_database_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير: expanded_database_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            targetsAchieved,
            improvement: accuracyImprovement
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testExpandedDatabase();
        
        if (results) {
            console.log('\n🎯 الخلاصة:');
            console.log(`   الدقة الجديدة: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(0)}ms`);
            console.log(`   الأهداف المحققة: ${results.targetsAchieved}/3`);
            
            if (results.targetsAchieved === 3) {
                console.log('🎉 تم تحقيق جميع الأهداف! ننتقل للخطوة التالية');
            } else if (results.avgAccuracy >= 85) {
                console.log('🎯 دقة ممتازة! قريب جداً من الهدف');
            } else if (results.improvement > 20) {
                console.log('📈 تحسن كبير! قاعدة البيانات الموسعة فعالة');
            } else {
                console.log('🔧 تحسن محدود، نحتاج التحسين التالي');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testExpandedDatabase };
