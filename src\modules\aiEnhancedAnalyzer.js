const fs = require('fs');
const path = require('path');

/**
 * محلل معزز بالذكاء الاصطناعي للوصول لدقة 95%+
 */
class AIEnhancedAnalyzer {
    constructor() {
        this.loadReferenceData();
        this.initializeAIModels();
        
        // إعدادات محسنة للدقة العالية
        this.config = {
            // عتبات أكثر حساسية
            thresholds: {
                exact_match: 0.90,      // تطابق حرفي
                high_similarity: 0.75,  // تشابه عالي
                medium_similarity: 0.50, // تشابه متوسط
                low_similarity: 0.25    // تشابه منخفض
            },
            
            // أوزان محسنة للذكاء الاصطناعي
            weights: {
                exact_matching: 0.40,    // التطابق الحرفي
                semantic_ai: 0.30,       // التحليل الدلالي بالذكاء الاصطناعي
                pattern_recognition: 0.20, // التعرف على الأنماط
                context_analysis: 0.10   // تحليل السياق
            },
            
            // معايير الكشف المتقدمة
            detection: {
                min_phrase_length: 8,    // الحد الأدنى لطول العبارة
                similarity_boost: 1.5,   // تضخيم التشابه العالي
                context_window: 50,      // نافذة السياق
                ai_confidence_threshold: 0.7 // عتبة ثقة الذكاء الاصطناعي
            }
        };
        
        console.log('🤖 تم تهيئة المحلل المعزز بالذكاء الاصطناعي');
    }
    
    /**
     * تحميل البيانات المرجعية
     */
    loadReferenceData() {
        try {
            const dataPath = path.join(__dirname, '../data/reference_phrases.json');
            const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
            this.referenceTexts = data.academic_phrases || [];
            
            // معالجة مسبقة متقدمة
            this.processedReferences = this.referenceTexts.map((text, index) => ({
                id: index,
                original: text,
                processed: this.preprocessText(text),
                words: this.extractWords(text),
                phrases: this.extractPhrases(text),
                semanticVector: this.generateSemanticVector(text),
                patterns: this.extractPatterns(text)
            }));
            
            console.log(`🧠 تم تحميل ومعالجة ${this.referenceTexts.length} عبارة مرجعية بالذكاء الاصطناعي`);
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error.message);
            this.referenceTexts = [];
            this.processedReferences = [];
        }
    }
    
    /**
     * تهيئة نماذج الذكاء الاصطناعي
     */
    initializeAIModels() {
        try {
            // نموذج التحليل الدلالي المبسط
            this.semanticModel = {
                // كلمات مفتاحية أكاديمية مع أوزان
                academicKeywords: {
                    'دراسة': 0.9, 'بحث': 0.9, 'تحليل': 0.8, 'نتائج': 0.8,
                    'استنتاج': 0.7, 'فرضية': 0.7, 'منهج': 0.8, 'عينة': 0.6,
                    'إحصائي': 0.8, 'معنوية': 0.7, 'ارتباط': 0.7, 'متغير': 0.6,
                    'توصيات': 0.7, 'مقترحات': 0.6, 'تطبيق': 0.6, 'تطوير': 0.6
                },

                // أنماط أكاديمية شائعة
                academicPatterns: [
                    /تشير\s+النتائج\s+إلى/g,
                    /يوصي\s+الباحث\s+ب/g,
                    /من\s+خلال\s+هذا\s+البحث/g,
                    /تهدف\s+هذه\s+الدراسة\s+إلى/g,
                    /أظهرت\s+النتائج\s+وجود/g,
                    /في\s+ضوء\s+ما\s+تقدم/g,
                    /بناءً\s+على\s+النتائج/g,
                    /تم\s+استخدام\s+المنهج/g
                ]
            };

            console.log('🧠 تم تهيئة نماذج الذكاء الاصطناعي للتحليل الدلالي');
        } catch (error) {
            console.error('❌ خطأ في تهيئة نماذج الذكاء الاصطناعي:', error.message);
            // نموذج افتراضي بسيط
            this.semanticModel = {
                academicKeywords: {},
                academicPatterns: []
            };
        }
    }
    
    /**
     * التحليل الرئيسي المعزز بالذكاء الاصطناعي
     */
    async analyzeWithAI(inputText) {
        console.log('🤖 بدء التحليل المعزز بالذكاء الاصطناعي...');
        
        // معالجة النص المدخل
        const processedInput = {
            original: inputText,
            processed: this.preprocessText(inputText),
            words: this.extractWords(inputText),
            phrases: this.extractPhrases(inputText),
            semanticVector: this.generateSemanticVector(inputText),
            patterns: this.extractPatterns(inputText),
            sentences: this.extractSentences(inputText)
        };
        
        // التحليل متعدد المستويات
        const analysisResults = {
            exactMatches: [],
            semanticMatches: [],
            patternMatches: [],
            contextMatches: [],
            overallSimilarity: 0,
            confidenceScore: 0
        };
        
        // المستوى 1: البحث عن التطابق الحرفي المتقدم
        analysisResults.exactMatches = await this.findExactMatches(processedInput);
        
        // المستوى 2: التحليل الدلالي بالذكاء الاصطناعي
        analysisResults.semanticMatches = await this.findSemanticMatches(processedInput);
        
        // المستوى 3: التعرف على الأنماط
        analysisResults.patternMatches = await this.findPatternMatches(processedInput);
        
        // المستوى 4: تحليل السياق
        analysisResults.contextMatches = await this.findContextMatches(processedInput);
        
        // حساب النتيجة النهائية بالذكاء الاصطناعي
        const finalResult = this.calculateAIEnhancedScore(analysisResults, processedInput);
        
        console.log(`🎯 انتهى التحليل المعزز: ${finalResult.plagiarismPercentage}% استلال`);
        
        return finalResult;
    }
    
    /**
     * البحث عن التطابق الحرفي المتقدم
     */
    async findExactMatches(input) {
        const matches = [];
        const inputText = input.processed;
        
        for (const ref of this.processedReferences) {
            const refText = ref.processed;
            
            // البحث عن عبارات متطابقة بأطوال مختلفة
            for (let length = 50; length >= this.config.detection.min_phrase_length; length--) {
                for (let i = 0; i <= inputText.length - length; i++) {
                    const substring = inputText.substring(i, i + length);
                    
                    if (refText.includes(substring)) {
                        matches.push({
                            type: 'exact',
                            text: substring,
                            similarity: 1.0,
                            length: length,
                            referenceId: ref.id,
                            position: i,
                            weight: length / inputText.length
                        });
                    }
                }
            }
            
            // البحث عن عبارات كاملة متطابقة
            ref.phrases.forEach(phrase => {
                if (inputText.includes(phrase.toLowerCase())) {
                    matches.push({
                        type: 'phrase',
                        text: phrase,
                        similarity: 0.95,
                        referenceId: ref.id,
                        weight: phrase.length / inputText.length
                    });
                }
            });
        }
        
        return matches.sort((a, b) => b.weight - a.weight);
    }
    
    /**
     * التحليل الدلالي بالذكاء الاصطناعي
     */
    async findSemanticMatches(input) {
        const matches = [];
        
        for (const ref of this.processedReferences) {
            // حساب التشابه الدلالي باستخدام الذكاء الاصطناعي
            const semanticSimilarity = this.calculateSemanticSimilarity(
                input.semanticVector, 
                ref.semanticVector
            );
            
            if (semanticSimilarity > this.config.thresholds.low_similarity) {
                // تحليل الجمل للعثور على التطابق الدلالي
                for (const sentence of input.sentences) {
                    const sentenceVector = this.generateSemanticVector(sentence);
                    const sentenceSimilarity = this.calculateSemanticSimilarity(
                        sentenceVector, 
                        ref.semanticVector
                    );
                    
                    if (sentenceSimilarity > this.config.detection.ai_confidence_threshold) {
                        matches.push({
                            type: 'semantic',
                            text: sentence,
                            similarity: sentenceSimilarity,
                            referenceId: ref.id,
                            confidence: sentenceSimilarity,
                            weight: sentence.length / input.original.length
                        });
                    }
                }
            }
        }
        
        return matches.sort((a, b) => b.confidence - a.confidence);
    }
    
    /**
     * التعرف على الأنماط الأكاديمية
     */
    async findPatternMatches(input) {
        const matches = [];
        const inputText = input.processed;
        
        // البحث عن الأنماط الأكاديمية المعروفة
        this.semanticModel.academicPatterns.forEach((pattern, index) => {
            const patternMatches = inputText.match(pattern);
            if (patternMatches) {
                patternMatches.forEach(match => {
                    matches.push({
                        type: 'pattern',
                        text: match,
                        similarity: 0.8,
                        patternId: index,
                        confidence: 0.9,
                        weight: match.length / inputText.length
                    });
                });
            }
        });
        
        return matches;
    }
    
    /**
     * تحليل السياق
     */
    async findContextMatches(input) {
        const matches = [];
        
        // تحليل السياق الأكاديمي
        const academicScore = this.calculateAcademicContextScore(input.processed);
        
        if (academicScore > 0.5) {
            matches.push({
                type: 'context',
                text: 'سياق أكاديمي',
                similarity: academicScore,
                confidence: academicScore,
                weight: 0.1
            });
        }
        
        return matches;
    }
    
    /**
     * حساب النتيجة النهائية بالذكاء الاصطناعي
     */
    calculateAIEnhancedScore(analysisResults, input) {
        let totalScore = 0;
        let confidenceScore = 0;
        let suspiciousSegments = [];
        
        // وزن التطابق الحرفي
        const exactScore = analysisResults.exactMatches.reduce((sum, match) => {
            return sum + (match.weight * match.similarity);
        }, 0);
        totalScore += exactScore * this.config.weights.exact_matching;
        
        // وزن التحليل الدلالي
        const semanticScore = analysisResults.semanticMatches.reduce((sum, match) => {
            suspiciousSegments.push({
                text: match.text,
                similarity: match.similarity,
                type: match.type
            });
            return sum + (match.weight * match.confidence);
        }, 0);
        totalScore += semanticScore * this.config.weights.semantic_ai;
        
        // وزن التعرف على الأنماط
        const patternScore = analysisResults.patternMatches.reduce((sum, match) => {
            return sum + (match.weight * match.confidence);
        }, 0);
        totalScore += patternScore * this.config.weights.pattern_recognition;
        
        // وزن تحليل السياق
        const contextScore = analysisResults.contextMatches.reduce((sum, match) => {
            return sum + (match.weight * match.confidence);
        }, 0);
        totalScore += contextScore * this.config.weights.context_analysis;
        
        // تطبيق تضخيم الذكاء الاصطناعي للنتائج العالية
        if (totalScore > 0.5) {
            totalScore = Math.min(1.0, totalScore * this.config.detection.similarity_boost);
        }
        
        // حساب مستوى الثقة
        confidenceScore = Math.min(1.0, (
            analysisResults.exactMatches.length * 0.3 +
            analysisResults.semanticMatches.length * 0.4 +
            analysisResults.patternMatches.length * 0.2 +
            analysisResults.contextMatches.length * 0.1
        ) / 10);
        
        const plagiarismPercentage = Math.round(totalScore * 100);
        
        return {
            plagiarismPercentage,
            riskLevel: this.classifyRiskLevel(plagiarismPercentage),
            suspiciousSegments: suspiciousSegments.slice(0, 15),
            analysis: {
                exactMatches: analysisResults.exactMatches.length,
                semanticMatches: analysisResults.semanticMatches.length,
                patternMatches: analysisResults.patternMatches.length,
                contextMatches: analysisResults.contextMatches.length,
                confidenceScore: confidenceScore,
                aiEnhanced: true,
                totalReferences: this.processedReferences.length
            }
        };
    }
    
    /**
     * إنتاج متجه دلالي مبسط
     */
    generateSemanticVector(text) {
        const words = this.extractWords(text);
        const vector = {};
        
        // حساب أوزان الكلمات الأكاديمية
        words.forEach(word => {
            const weight = this.semanticModel.academicKeywords[word] || 0.1;
            vector[word] = (vector[word] || 0) + weight;
        });
        
        return vector;
    }
    
    /**
     * حساب التشابه الدلالي
     */
    calculateSemanticSimilarity(vector1, vector2) {
        const keys1 = Object.keys(vector1);
        const keys2 = Object.keys(vector2);
        const allKeys = [...new Set([...keys1, ...keys2])];
        
        let dotProduct = 0;
        let magnitude1 = 0;
        let magnitude2 = 0;
        
        allKeys.forEach(key => {
            const val1 = vector1[key] || 0;
            const val2 = vector2[key] || 0;
            
            dotProduct += val1 * val2;
            magnitude1 += val1 * val1;
            magnitude2 += val2 * val2;
        });
        
        const magnitude = Math.sqrt(magnitude1) * Math.sqrt(magnitude2);
        return magnitude > 0 ? dotProduct / magnitude : 0;
    }
    
    /**
     * حساب نقاط السياق الأكاديمي
     */
    calculateAcademicContextScore(text) {
        let score = 0;
        const words = this.extractWords(text);
        
        words.forEach(word => {
            if (this.semanticModel.academicKeywords[word]) {
                score += this.semanticModel.academicKeywords[word];
            }
        });
        
        return Math.min(1.0, score / words.length);
    }
    
    /**
     * تصنيف مستوى الخطر
     */
    classifyRiskLevel(percentage) {
        if (percentage >= 80) return { level: 5, label: 'عالي جداً' };
        if (percentage >= 60) return { level: 4, label: 'عالي' };
        if (percentage >= 40) return { level: 3, label: 'متوسط' };
        if (percentage >= 20) return { level: 2, label: 'منخفض' };
        return { level: 1, label: 'ضئيل' };
    }
    
    /**
     * معالجة النص
     */
    preprocessText(text) {
        return text
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim()
            .toLowerCase();
    }
    
    /**
     * استخراج الكلمات
     */
    extractWords(text) {
        return this.preprocessText(text)
            .split(/\s+/)
            .filter(word => word.length > 2);
    }
    
    /**
     * استخراج العبارات
     */
    extractPhrases(text) {
        const words = this.extractWords(text);
        const phrases = [];
        
        for (let len = 3; len <= 6; len++) {
            for (let i = 0; i <= words.length - len; i++) {
                const phrase = words.slice(i, i + len).join(' ');
                if (phrase.length > 15) {
                    phrases.push(phrase);
                }
            }
        }
        
        return phrases;
    }
    
    /**
     * استخراج الجمل
     */
    extractSentences(text) {
        return text
            .split(/[.!?؟]/)
            .map(s => s.trim())
            .filter(s => s.length > 20);
    }
    
    /**
     * استخراج الأنماط
     */
    extractPatterns(text) {
        const patterns = [];
        
        this.semanticModel.academicPatterns.forEach((pattern, index) => {
            const matches = text.match(pattern);
            if (matches) {
                patterns.push({
                    patternId: index,
                    matches: matches.length,
                    examples: matches.slice(0, 3)
                });
            }
        });
        
        return patterns;
    }
}

module.exports = AIEnhancedAnalyzer;
