/*
Copyright (c) 2014, <PERSON>

Per<PERSON> is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/

const _ = require('underscore')

exports.ngrams = function (sequence, n, startSymbol, endSymbol) {
  return ngrams(sequence, n, startSymbol, endSymbol)
}

exports.bigrams = function (sequence, startSymbol, endSymbol) {
  return ngrams(sequence, 2, startSymbol, endSymbol)
}

exports.trigrams = function (sequence, startSymbol, endSymbol) {
  return ngrams(sequence, 3, startSymbol, endSymbol)
}

const ngrams = function (sequence, n, startSymbol, endSymbol) {
  const result = []

  if (!_.isArray(sequence)) {
    sequence = sequence.split('')
  }

  const count = _.max([0, sequence.length - n + 1])

  // Check for left padding
  if (typeof startSymbol !== 'undefined' && startSymbol !== null) {
    // Create an array of (n) start symbols
    const blanks = []
    for (let i = 0; i < n; i++) {
      blanks.push(startSymbol)
    }

    // Create the left padding
    for (let p = n - 1; p > 0; p--) {
      // Create a tuple of (p) start symbols and (n - p) words
      result.push(blanks.slice(0, p).concat(sequence.slice(0, n - p)))
    }
  }

  // Build the complete ngrams
  for (let i = 0; i < count; i++) {
    result.push(sequence.slice(i, i + n))
  }

  // Check for right padding
  if (typeof endSymbol !== 'undefined' && endSymbol !== null) {
    // Create an array of (n) end symbols
    const blanks = []
    for (let i = 0; i < n; i++) {
      blanks.push(endSymbol)
    }

    // create the right padding
    for (let p = n - 1; p > 0; p--) {
      // Create a tuple of (p) start symbols and (n - p) words
      result.push(sequence.slice(sequence.length - p, sequence.length).concat(blanks.slice(0, n - p)))
    }
  }

  return result
}
