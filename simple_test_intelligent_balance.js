const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار مبسط لمحلل التوازن الذكي
 */
async function simpleTestIntelligentBalance() {
    console.log('🧠 اختبار مبسط لمحلل التوازن الذكي');
    console.log('=' .repeat(60));
    
    try {
        const checker = new PlagiarismChecker();
        console.log('✅ تم تهيئة النظام بنجاح');
        
        // اختبار البحوث الثلاثة
        const researchFiles = [
            'research_low_plagiarism_15percent.txt',
            'research_medium_plagiarism_50percent.txt', 
            'research_high_plagiarism_85percent.txt'
        ];
        
        const expectedResults = [15, 50, 85];
        const results = [];
        
        for (let i = 0; i < researchFiles.length; i++) {
            const file = researchFiles[i];
            const expected = expectedResults[i];
            
            console.log(`\n📄 اختبار: ${file}`);
            console.log(`🎯 متوقع: ${expected}%`);
            
            const filePath = path.join(__dirname, 'real-research-tests', file);
            
            if (!fs.existsSync(filePath)) {
                console.log(`❌ الملف غير موجود`);
                continue;
            }
            
            try {
                const startTime = Date.now();
                checker.aiDetector.isEnabled = false;
                
                const result = await checker.checkFile(filePath);
                const endTime = Date.now();
                
                const actual = result.plagiarismPercentage;
                const difference = Math.abs(actual - expected);
                const accuracy = Math.max(0, 100 - (difference / expected) * 100);
                const processingTime = endTime - startTime;
                
                console.log(`   ✅ النتيجة: ${actual}%`);
                console.log(`   📊 الدقة: ${accuracy.toFixed(1)}%`);
                console.log(`   ⏱️ الوقت: ${processingTime}ms`);
                console.log(`   🚨 الخطر: ${result.riskLevel.label}`);
                console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments ? result.suspiciousSegments.length : 0}`);
                
                const passed = accuracy >= 95;
                console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (معيار الدقة 95%+)`);
                
                results.push({
                    file,
                    expected,
                    actual,
                    accuracy,
                    passed,
                    processingTime
                });
                
            } catch (error) {
                console.log(`   ❌ خطأ: ${error.message}`);
                results.push({
                    file,
                    expected,
                    error: error.message,
                    passed: false
                });
            }
        }
        
        // تحليل النتائج
        console.log('\n' + '=' .repeat(60));
        console.log('📊 تحليل النتائج');
        console.log('=' .repeat(60));
        
        const validResults = results.filter(r => !r.error);
        const passedTests = validResults.filter(r => r.passed);
        
        if (validResults.length > 0) {
            const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
            const successRate = (passedTests.length / validResults.length) * 100;
            const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
            
            console.log(`🎯 النتائج الإجمالية:`);
            console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (${passedTests.length}/${validResults.length})`);
            console.log(`   متوسط الوقت: ${(avgTime/1000).toFixed(1)}s`);
            
            console.log(`\n📋 تفاصيل كل بحث:`);
            validResults.forEach(result => {
                const grade = result.accuracy >= 95 ? '🎯 ممتاز' : 
                             result.accuracy >= 80 ? '✅ جيد جداً' : 
                             result.accuracy >= 60 ? '📈 جيد' : '⚠️ ضعيف';
                
                console.log(`   ${result.file}:`);
                console.log(`      النتيجة: ${result.actual}% (متوقع: ${result.expected}%)`);
                console.log(`      الدقة: ${result.accuracy.toFixed(1)}% ${grade}`);
                console.log(`      الحالة: ${result.passed ? 'نجح ✅' : 'فشل ❌'}`);
            });
            
            // التقييم النهائي
            let finalAssessment;
            if (successRate >= 100) {
                finalAssessment = '🎉 نجح تماماً! تم تحقيق دقة 95%+ لجميع البحوث!';
            } else if (avgAccuracy >= 90) {
                finalAssessment = '🎯 نجح بامتياز! دقة عالية جداً';
            } else if (avgAccuracy >= 80) {
                finalAssessment = '✅ نجح بشكل جيد! دقة عالية';
            } else if (avgAccuracy >= 70) {
                finalAssessment = '📈 نجح جزئياً! تحسن جيد';
            } else {
                finalAssessment = '⚠️ يحتاج تحسين! دقة منخفضة';
            }
            
            console.log(`\n🏆 التقييم النهائي: ${finalAssessment}`);
            
            // حفظ النتائج
            const reportData = {
                timestamp: new Date().toISOString(),
                avgAccuracy,
                successRate,
                avgTime,
                finalAssessment,
                results: validResults
            };
            
            fs.writeFileSync('simple_intelligent_balance_test_report.json', JSON.stringify(reportData, null, 2));
            console.log(`\n💾 تم حفظ التقرير: simple_intelligent_balance_test_report.json`);
            
            return {
                success: successRate >= 100,
                avgAccuracy,
                successRate,
                finalAssessment
            };
            
        } else {
            console.log('❌ لا توجد نتائج صحيحة');
            return null;
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error(error.stack);
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await simpleTestIntelligentBalance();
        
        if (results) {
            console.log('\n🎯 الخلاصة:');
            console.log(`   تحقيق الهدف: ${results.success ? 'نعم ✅' : 'لا ❌'}`);
            console.log(`   الدقة: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التقييم: ${results.finalAssessment}`);
        }
        
    } catch (error) {
        console.error('❌ خطأ عام:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { simpleTestIntelligentBalance };
