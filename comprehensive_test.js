const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * نظام اختبار شامل لتطبيق كشف الاستلال
 */
class ComprehensiveTestSuite {
    constructor() {
        this.testResults = [];
        this.performanceMetrics = [];
        this.accuracyScores = [];
        this.testStartTime = null;
        this.expectedResults = null;
        
        // تحميل النتائج المتوقعة
        this.loadExpectedResults();
    }

    /**
     * تحميل النتائج المتوقعة من ملف JSON
     */
    loadExpectedResults() {
        try {
            const expectedPath = path.join(__dirname, 'test-files', 'expected_results.json');
            const data = fs.readFileSync(expectedPath, 'utf8');
            this.expectedResults = JSON.parse(data);
            console.log('✅ تم تحميل النتائج المتوقعة بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تحميل النتائج المتوقعة:', error.message);
            process.exit(1);
        }
    }

    /**
     * تشغيل جميع الاختبارات
     */
    async runAllTests() {
        console.log('🚀 بدء الاختبارات الشاملة لتطبيق كشف الاستلال');
        console.log('=' .repeat(60));
        
        this.testStartTime = Date.now();
        
        // اختبار كل ملف
        for (const testFile of this.expectedResults.test_files) {
            await this.runSingleTest(testFile);
        }
        
        // إنتاج التقرير النهائي
        await this.generateFinalReport();
        
        console.log('\n🎉 تم الانتهاء من جميع الاختبارات!');
    }

    /**
     * تشغيل اختبار واحد
     */
    async runSingleTest(testConfig) {
        console.log(`\n📄 اختبار ملف: ${testConfig.filename}`);
        console.log(`📋 الوصف: ${testConfig.description}`);
        console.log(`🎯 النسبة المتوقعة: ${testConfig.expected_plagiarism}%`);
        
        const filePath = path.join(__dirname, 'test-files', testConfig.filename);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود: ${filePath}`);
            return;
        }

        try {
            const checker = new PlagiarismChecker();
            const startTime = Date.now();
            
            // تعيين دالة تحديث التقدم
            let lastProgress = 0;
            checker.setProgressCallback((percentage, message) => {
                if (percentage - lastProgress >= 25) {
                    console.log(`   📊 ${percentage}% - ${message}`);
                    lastProgress = percentage;
                }
            });
            
            // تشغيل الفحص
            const results = await checker.checkFile(filePath);
            const endTime = Date.now();
            const processingTime = endTime - startTime;
            
            // تحليل النتائج
            const analysis = this.analyzeResults(results, testConfig, processingTime);
            this.testResults.push(analysis);
            
            // عرض النتائج
            this.displayTestResults(analysis);
            
        } catch (error) {
            console.log(`❌ خطأ في الاختبار: ${error.message}`);
            this.testResults.push({
                filename: testConfig.filename,
                status: 'failed',
                error: error.message,
                expected: testConfig.expected_plagiarism,
                actual: null,
                accuracy: 0
            });
        }
    }

    /**
     * تحليل نتائج الاختبار
     */
    analyzeResults(results, testConfig, processingTime) {
        const actualPlagiarism = results.plagiarismPercentage;
        const expectedPlagiarism = testConfig.expected_plagiarism;
        const tolerance = testConfig.tolerance;
        
        // حساب الدقة
        const difference = Math.abs(actualPlagiarism - expectedPlagiarism);
        const isAccurate = difference <= tolerance;
        const accuracyScore = Math.max(0, 100 - (difference / expectedPlagiarism) * 100);
        
        // تحليل الأجزاء المشكوك بها
        const actualSuspicious = results.suspiciousSegments.length;
        const expectedSuspicious = testConfig.expected_suspicious_segments;
        const suspiciousAccuracy = Math.max(0, 100 - Math.abs(actualSuspicious - expectedSuspicious) / expectedSuspicious * 100);
        
        // حساب الأداء
        const wordsPerSecond = results.statistics.totalWords / (processingTime / 1000);
        
        return {
            filename: testConfig.filename,
            status: isAccurate ? 'passed' : 'failed',
            expected: expectedPlagiarism,
            actual: actualPlagiarism,
            difference: difference,
            tolerance: tolerance,
            accuracy: accuracyScore,
            suspiciousSegments: {
                expected: expectedSuspicious,
                actual: actualSuspicious,
                accuracy: suspiciousAccuracy
            },
            performance: {
                processingTime: processingTime,
                wordsPerSecond: wordsPerSecond,
                totalWords: results.statistics.totalWords
            },
            plagiarismType: testConfig.plagiarism_type,
            contentType: testConfig.content_type,
            aiAnalysis: results.hasAIAnalysis,
            riskLevel: results.riskLevel
        };
    }

    /**
     * عرض نتائج اختبار واحد
     */
    displayTestResults(analysis) {
        const statusIcon = analysis.status === 'passed' ? '✅' : '❌';
        const accuracyColor = analysis.accuracy >= 80 ? '🟢' : analysis.accuracy >= 60 ? '🟡' : '🔴';
        
        console.log(`   ${statusIcon} النتيجة: ${analysis.status.toUpperCase()}`);
        console.log(`   📈 النسبة الفعلية: ${analysis.actual}% (متوقع: ${analysis.expected}%)`);
        console.log(`   ${accuracyColor} دقة الكشف: ${analysis.accuracy.toFixed(1)}%`);
        console.log(`   ⚠️ أجزاء مشكوك بها: ${analysis.suspiciousSegments.actual} (متوقع: ${analysis.suspiciousSegments.expected})`);
        console.log(`   ⏱️ وقت المعالجة: ${analysis.performance.processingTime}ms`);
        console.log(`   🚀 سرعة المعالجة: ${analysis.performance.wordsPerSecond.toFixed(1)} كلمة/ثانية`);
        console.log(`   🎯 مستوى الخطر: ${analysis.riskLevel.label}`);
        
        if (analysis.status === 'failed') {
            console.log(`   ⚠️ الفرق: ${analysis.difference}% (الحد المسموح: ${analysis.tolerance}%)`);
        }
    }

    /**
     * إنتاج التقرير النهائي
     */
    async generateFinalReport() {
        console.log('\n' + '=' .repeat(60));
        console.log('📊 التقرير النهائي للاختبارات');
        console.log('=' .repeat(60));
        
        const totalTests = this.testResults.length;
        const passedTests = this.testResults.filter(r => r.status === 'passed').length;
        const failedTests = totalTests - passedTests;
        const overallAccuracy = this.testResults.reduce((sum, r) => sum + r.accuracy, 0) / totalTests;
        
        console.log(`\n📈 إحصائيات عامة:`);
        console.log(`   إجمالي الاختبارات: ${totalTests}`);
        console.log(`   اختبارات ناجحة: ${passedTests} (${(passedTests/totalTests*100).toFixed(1)}%)`);
        console.log(`   اختبارات فاشلة: ${failedTests} (${(failedTests/totalTests*100).toFixed(1)}%)`);
        console.log(`   متوسط الدقة: ${overallAccuracy.toFixed(1)}%`);
        
        // تحليل الأداء
        const avgProcessingTime = this.testResults.reduce((sum, r) => sum + r.performance.processingTime, 0) / totalTests;
        const avgWordsPerSecond = this.testResults.reduce((sum, r) => sum + r.performance.wordsPerSecond, 0) / totalTests;
        
        console.log(`\n⚡ أداء النظام:`);
        console.log(`   متوسط وقت المعالجة: ${avgProcessingTime.toFixed(0)}ms`);
        console.log(`   متوسط سرعة المعالجة: ${avgWordsPerSecond.toFixed(1)} كلمة/ثانية`);
        
        // تحليل دقة الكشف حسب نوع الاستلال
        console.log(`\n🎯 دقة الكشف حسب نوع الاستلال:`);
        const typeAccuracy = {};
        this.testResults.forEach(result => {
            if (!typeAccuracy[result.plagiarismType]) {
                typeAccuracy[result.plagiarismType] = [];
            }
            typeAccuracy[result.plagiarismType].push(result.accuracy);
        });
        
        Object.keys(typeAccuracy).forEach(type => {
            const avg = typeAccuracy[type].reduce((sum, acc) => sum + acc, 0) / typeAccuracy[type].length;
            console.log(`   ${type}: ${avg.toFixed(1)}%`);
        });
        
        // تفاصيل الاختبارات الفاشلة
        const failedResults = this.testResults.filter(r => r.status === 'failed');
        if (failedResults.length > 0) {
            console.log(`\n❌ تفاصيل الاختبارات الفاشلة:`);
            failedResults.forEach(result => {
                console.log(`   ${result.filename}: فرق ${result.difference}% (حد مسموح: ${result.tolerance}%)`);
            });
        }
        
        // توصيات التحسين
        console.log(`\n💡 توصيات التحسين:`);
        if (overallAccuracy < 80) {
            console.log(`   - تحسين خوارزميات الكشف (الدقة الحالية: ${overallAccuracy.toFixed(1)}%)`);
        }
        if (avgProcessingTime > 5000) {
            console.log(`   - تحسين سرعة المعالجة (الوقت الحالي: ${avgProcessingTime.toFixed(0)}ms)`);
        }
        if (failedTests > 0) {
            console.log(`   - مراجعة العتبات والمعايير للاختبارات الفاشلة`);
        }
        
        // حفظ التقرير
        await this.saveDetailedReport();
        
        // تقييم عام
        const overallGrade = this.calculateOverallGrade(overallAccuracy, passedTests, totalTests);
        console.log(`\n🏆 التقييم العام: ${overallGrade}`);
    }

    /**
     * حساب التقييم العام
     */
    calculateOverallGrade(accuracy, passed, total) {
        const passRate = (passed / total) * 100;
        
        if (accuracy >= 90 && passRate >= 90) return 'ممتاز (A+)';
        if (accuracy >= 80 && passRate >= 80) return 'جيد جداً (A)';
        if (accuracy >= 70 && passRate >= 70) return 'جيد (B)';
        if (accuracy >= 60 && passRate >= 60) return 'مقبول (C)';
        return 'يحتاج تحسين (D)';
    }

    /**
     * حفظ التقرير المفصل
     */
    async saveDetailedReport() {
        const reportData = {
            timestamp: new Date().toISOString(),
            summary: {
                totalTests: this.testResults.length,
                passedTests: this.testResults.filter(r => r.status === 'passed').length,
                overallAccuracy: this.testResults.reduce((sum, r) => sum + r.accuracy, 0) / this.testResults.length
            },
            detailedResults: this.testResults,
            recommendations: this.generateRecommendations()
        };
        
        const reportPath = path.join(__dirname, 'test_report.json');
        fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2), 'utf8');
        console.log(`\n💾 تم حفظ التقرير المفصل: ${reportPath}`);
    }

    /**
     * إنتاج التوصيات
     */
    generateRecommendations() {
        const recommendations = [];
        const overallAccuracy = this.testResults.reduce((sum, r) => sum + r.accuracy, 0) / this.testResults.length;
        
        if (overallAccuracy < 80) {
            recommendations.push('تحسين خوارزميات كشف التشابه');
            recommendations.push('زيادة قاعدة البيانات المرجعية');
        }
        
        const highErrorTests = this.testResults.filter(r => r.difference > 20);
        if (highErrorTests.length > 0) {
            recommendations.push('مراجعة معايير الكشف للنصوص عالية الاستلال');
        }
        
        const slowTests = this.testResults.filter(r => r.performance.processingTime > 10000);
        if (slowTests.length > 0) {
            recommendations.push('تحسين أداء معالجة الملفات الكبيرة');
        }
        
        return recommendations;
    }
}

// تشغيل الاختبارات
async function main() {
    try {
        const testSuite = new ComprehensiveTestSuite();
        await testSuite.runAllTests();
    } catch (error) {
        console.error('❌ خطأ في تشغيل الاختبارات:', error);
        process.exit(1);
    }
}

// تشغيل الاختبارات إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    main();
}

module.exports = ComprehensiveTestSuite;
