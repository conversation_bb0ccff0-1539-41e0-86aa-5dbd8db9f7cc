@echo off
chcp 65001 >nul
title نظام كشف الاستلال ومحول PDF إلى DOCX المتقدم - الإصدار 2.0

echo.
echo ========================================
echo 🔍 نظام كشف الاستلال المتقدم - الإصدار 2.0
echo ========================================
echo.
echo ✅ النظام المحسن والمطور
echo ✅ حل جميع مشاكل الإصدار السابق
echo ✅ أداء أفضل واستقرار أعلى
echo.

echo 🚀 جاري تشغيل النظام المحسن...
echo.

cd "Advanced-Plagiarism-System-v2"

if exist "dist\نظام كشف الاستلال المتقدم v2-2.0.0-Portable.exe" (
    echo ✅ تم العثور على الملف التنفيذي المحمول
    echo 🔄 تشغيل النسخة المحمولة...
    start "" "dist\نظام كشف الاستلال المتقدم v2-2.0.0-Portable.exe"
) else if exist "dist\نظام كشف الاستلال المتقدم v2-2.0.0-x64.exe" (
    echo ✅ تم العثور على الملف التنفيذي x64
    echo 🔄 تشغيل النسخة x64...
    start "" "dist\نظام كشف الاستلال المتقدم v2-2.0.0-x64.exe"
) else if exist "dist\win-unpacked\نظام كشف الاستلال المتقدم v2.exe" (
    echo ✅ تم العثور على النسخة غير المضغوطة
    echo 🔄 تشغيل النسخة غير المضغوطة...
    start "" "dist\win-unpacked\نظام كشف الاستلال المتقدم v2.exe"
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo 🔧 محاولة تشغيل من المصدر...
    if exist "node_modules" (
        npm start
    ) else (
        echo 📦 تثبيت التبعيات...
        npm install
        echo 🚀 تشغيل النظام...
        npm start
    )
)

echo.
echo ✅ تم تشغيل النظام المحسن بنجاح!
echo 📱 يمكنك الآن استخدام النظام المحدث
echo.
echo ========================================
echo 🎯 الميزات الجديدة في الإصدار 2.0:
echo ========================================
echo ✅ حل مشكلة IPC والتسلسل
echo ✅ معالجة أخطاء محسنة
echo ✅ أداء أفضل واستقرار أعلى
echo ✅ واجهة محسنة وأكثر وضوحاً
echo ✅ دعم أفضل للملفات المعقدة
echo.

pause
