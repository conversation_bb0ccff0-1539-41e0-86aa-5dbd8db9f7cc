{"timestamp": "2025-07-04T22:58:47.892Z", "testDuration": 3395, "systemInfo": {"platform": "win32", "nodeVersion": "v22.14.0", "arch": "x64"}, "results": {"comprehensive": [{"filename": "test_10_percent.txt", "status": "failed", "expected": 10, "actual": 17, "difference": 7, "tolerance": 5, "accuracy": 30, "suspiciousSegments": {"expected": 1, "actual": 1, "accuracy": 100}, "performance": {"processingTime": 38, "wordsPerSecond": 5500, "totalWords": 209}, "plagiarismType": "minimal", "contentType": "original_academic", "aiAnalysis": false, "riskLevel": {"level": "minimal", "label": "ضئيل", "color": "#27ae60", "description": "نسبة استلال ضئيلة - المحتوى أصلي إلى حد كبير"}}, {"filename": "test_30_percent.txt", "status": "passed", "expected": 30, "actual": 25, "difference": 5, "tolerance": 10, "accuracy": 83.33333333333334, "suspiciousSegments": {"expected": 3, "actual": 5, "accuracy": 33.33333333333334}, "performance": {"processingTime": 25, "wordsPerSecond": 8320, "totalWords": 208}, "plagiarismType": "low", "contentType": "mixed_with_common_phrases", "aiAnalysis": false, "riskLevel": {"level": "low", "label": "من<PERSON><PERSON>ض", "color": "#f1c40f", "description": "نسبة استلال منخفضة - مقبولة مع بعض التحسينات"}}, {"filename": "test_50_percent.txt", "status": "failed", "expected": 50, "actual": 32, "difference": 18, "tolerance": 10, "accuracy": 64, "suspiciousSegments": {"expected": 5, "actual": 6, "accuracy": 80}, "performance": {"processingTime": 9, "wordsPerSecond": 25777.77777777778, "totalWords": 232}, "plagiarismType": "medium", "contentType": "paraphrased_content", "aiAnalysis": false, "riskLevel": {"level": "low", "label": "من<PERSON><PERSON>ض", "color": "#f1c40f", "description": "نسبة استلال منخفضة - مقبولة مع بعض التحسينات"}}, {"filename": "test_70_percent.txt", "status": "failed", "expected": 70, "actual": 33, "difference": 37, "tolerance": 10, "accuracy": 47.14285714285714, "suspiciousSegments": {"expected": 7, "actual": 6, "accuracy": 85.71428571428572}, "performance": {"processingTime": 10, "wordsPerSecond": 23600, "totalWords": 236}, "plagiarismType": "high", "contentType": "heavily_borrowed", "aiAnalysis": false, "riskLevel": {"level": "low", "label": "من<PERSON><PERSON>ض", "color": "#f1c40f", "description": "نسبة استلال منخفضة - مقبولة مع بعض التحسينات"}}, {"filename": "test_90_percent.txt", "status": "failed", "expected": 90, "actual": 51, "difference": 39, "tolerance": 5, "accuracy": 56.666666666666664, "suspiciousSegments": {"expected": 10, "actual": 11, "accuracy": 90}, "performance": {"processingTime": 12, "wordsPerSecond": 20833.333333333332, "totalWords": 250}, "plagiarismType": "very_high", "contentType": "mostly_copied", "aiAnalysis": false, "riskLevel": {"level": "medium", "label": "متوسط", "color": "#f39c12", "description": "نسبة استلال متوسطة - يتطلب مراجعة"}}, {"filename": "test_100_percent.txt", "status": "passed", "expected": 95, "actual": 90, "difference": 5, "tolerance": 5, "accuracy": 94.73684210526316, "suspiciousSegments": {"expected": 15, "actual": 19, "accuracy": 73.33333333333333}, "performance": {"processingTime": 15, "wordsPerSecond": 16133.333333333334, "totalWords": 242}, "plagiarismType": "complete_copy", "contentType": "direct_copy", "aiAnalysis": false, "riskLevel": {"level": "high", "label": "عالي", "color": "#e74c3c", "description": "نسبة استلال عالية جداً - يتطلب مراجعة شاملة"}}, {"filename": "test_mixed.txt", "status": "failed", "expected": 60, "actual": 32, "difference": 28, "tolerance": 15, "accuracy": 53.333333333333336, "suspiciousSegments": {"expected": 6, "actual": 5, "accuracy": 83.33333333333334}, "performance": {"processingTime": 23, "wordsPerSecond": 10739.130434782608, "totalWords": 247}, "plagiarismType": "mixed", "contentType": "mixed_plagiarism", "aiAnalysis": false, "riskLevel": {"level": "low", "label": "من<PERSON><PERSON>ض", "color": "#f1c40f", "description": "نسبة استلال منخفضة - مقبولة مع بعض التحسينات"}}], "performance": {"performanceResults": [{"fileSize": "صغير", "wordCount": 100, "processingTime": 7, "wordsPerSecond": 14285.714285714286, "memoryUsed": 1362592, "plagiarismScore": 70}, {"fileSize": "متوسط", "wordCount": 500, "processingTime": 18, "wordsPerSecond": 27777.77777777778, "memoryUsed": 6609056, "plagiarismScore": 68}, {"fileSize": "كبير", "wordCount": 1000, "processingTime": 25, "wordsPerSecond": 40000, "memoryUsed": -3239152, "plagiarismScore": 69}, {"fileSize": "كبير جداً", "wordCount": 2000, "processingTime": 45, "wordsPerSecond": 44444.444444444445, "memoryUsed": -3009056, "plagiarismScore": 70}], "memoryUsage": [], "stressTestResults": [{"iteration": 1, "processingTime": 10, "memoryUsed": 7869392, "plagiarismScore": 68}, {"iteration": 2, "processingTime": 9, "memoryUsed": -8525472, "plagiarismScore": 68}, {"iteration": 3, "processingTime": 9, "memoryUsed": 7870272, "plagiarismScore": 68}, {"iteration": 4, "processingTime": 8, "memoryUsed": -8583096, "plagiarismScore": 68}, {"iteration": 5, "processingTime": 8, "memoryUsed": 7868232, "plagiarismScore": 68}]}}, "analysis": {"overallGrade": {"grade": "جيد (B)", "description": "التطبيق يحتاج بعض التحسينات قبل الإنتاج", "score": 70, "issues": ["دقة الكشف منخفضة"]}, "strengthsAndWeaknesses": {"strengths": ["سرعة معالجة ممتازة", "واجهة مستخدم عربية شاملة", "دعم أنواع ملفات متعددة", "تقارير مفصلة وشاملة", "تكامل مع الذكاء الاصطناعي"], "weaknesses": ["معدل نجاح منخفض في بعض الاختبارات", "دقة الكشف تحتاج تحسين"]}, "recommendations": ["تحسين خوارزميات كشف التشابه لزيادة الدقة", "توسيع قاعدة البيانات المرجعية", "إضافة مفاتيح API حقيقية للذكاء الاصطناعي", "تطوير نظام تحديثات تلقائي", "إضافة دعم لغات أخرى", "تحسين تصميم التقارير المُنتجة"], "productionReadiness": {"status": "⚠️ جاهز مع تحفظات", "details": "يحتاج تحسينات: دقة منخفضة", "score": 60}}}