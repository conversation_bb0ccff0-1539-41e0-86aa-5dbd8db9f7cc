{"timestamp": "2025-07-05T01:13:55.895Z", "test_type": "ai_enhanced_system_test", "ai_features": {"semantic_analysis": true, "pattern_recognition": true, "context_analysis": true, "confidence_scoring": true, "multi_level_detection": true}, "results": {"avgAccuracy": 2.2222222222222237, "successRate": 0, "avgTime": 12.333333333333334, "avgConfidence": null, "avgExactMatches": null, "avgSemanticMatches": null, "avgPatternMatches": null, "avgSuspiciousCount": 0, "targetsAchieved": 1, "improvement": {"accuracy": -49.47777777777778, "successRate": -33.3}}, "detailed_results": [{"file": "test_calibrated_20_percent.txt", "expected": 20, "actual": 1, "accuracy": 5, "processingTime": 11, "suspiciousCount": 0, "passed": false}, {"file": "test_calibrated_60_percent.txt", "expected": 60, "actual": 1, "accuracy": 1.6666666666666714, "processingTime": 19, "suspiciousCount": 0, "passed": false}, {"file": "test_calibrated_90_percent.txt", "expected": 90, "actual": 0, "accuracy": 0, "processingTime": 7, "suspiciousCount": 0, "passed": false}]}