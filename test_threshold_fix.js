const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار سريع لتأثير تقليل العتبات
 */
async function testThresholdFix() {
    console.log('🔧 اختبار تأثير تقليل العتبات التكيفية');
    console.log('=' .repeat(60));
    console.log('📉 العتبات الجديدة:');
    console.log('   High: 35% (كان 80%)');
    console.log('   Medium: 25% (كان 65%)');
    console.log('   Low: 20% (كان 45%)');
    console.log('   Dynamic: 20-40% (كان 40-90%)');
    console.log('=' .repeat(60));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار
    const testFiles = [
        { file: 'test_large_10_percent.txt', expected: 15, description: 'نص أصلي' },
        { file: 'test_large_50_percent.txt', expected: 55, description: 'نص مختلط' },
        { file: 'test_large_90_percent.txt', expected: 85, description: 'نص مستل' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 70; // معيار مؤقت
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   ⚙️ العتبة: ${(result.analysis.adaptiveThreshold * 100).toFixed(1)}%`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                threshold: result.analysis.adaptiveThreshold,
                suspiciousCount: result.suspiciousSegments.length,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج
    console.log('\n' + '=' .repeat(60));
    console.log('📊 تحليل تأثير تقليل العتبات');
    console.log('=' .repeat(60));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgThreshold = validResults.reduce((sum, r) => sum + r.threshold, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`📈 النتائج بعد تقليل العتبات:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (كان 38.5%)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (كان 0%)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms (بدون AI)`);
        console.log(`   متوسط العتبة: ${(avgThreshold * 100).toFixed(1)}% (كان 52.5%)`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 38.5;
        const successImprovement = successRate - 0;
        
        console.log(`\n📊 التحسن المحقق:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تقييم التحسن
        if (avgAccuracy >= 80 && successRate >= 70) {
            console.log(`\n🎉 تحسن ممتاز! العتبات الجديدة فعالة جداً`);
        } else if (avgAccuracy >= 60 && successRate >= 50) {
            console.log(`\n✅ تحسن جيد! العتبات في الاتجاه الصحيح`);
        } else if (avgAccuracy > 38.5) {
            console.log(`\n📈 تحسن ملحوظ! يحتاج مزيد من التحسين`);
        } else {
            console.log(`\n⚠️ تحسن محدود! قد نحتاج عتبات أقل`);
        }
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج:`);
        validResults.forEach(result => {
            const improvement = result.accuracy > 70 ? '✅ تحسن' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${improvement}`);
        });
        
        // حفظ النتائج
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'threshold_fix_test',
            old_thresholds: { high: 80, medium: 65, low: 45, dynamic: '40-90%' },
            new_thresholds: { high: 35, medium: 25, low: 20, dynamic: '20-40%' },
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgThreshold: avgThreshold * 100,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results
        };
        
        fs.writeFileSync('threshold_fix_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير: threshold_fix_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            improvement: accuracyImprovement
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testThresholdFix();
        
        if (results) {
            console.log('\n🎯 الخلاصة:');
            console.log(`   الدقة الجديدة: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            
            if (results.avgAccuracy >= 80) {
                console.log('🎉 هدف الدقة 80%+ تحقق! ننتقل للخطوة التالية');
            } else if (results.improvement > 20) {
                console.log('📈 تحسن كبير! نحتاج مزيد من التحسينات');
            } else {
                console.log('🔧 تحسن محدود، نحتاج تحسينات إضافية');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testThresholdFix };
