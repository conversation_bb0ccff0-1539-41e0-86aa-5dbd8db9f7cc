const MultilingualAnalyzer = require('./src/modules/multilingualAnalyzer');
const fs = require('fs');
const path = require('path');

/**
 * اختبار شامل للمحلل متعدد اللغات
 * الهدف: التأكد من دقة 95%+ للعربية والإنجليزية
 */
async function testMultilingualAnalyzer() {
    console.log('🌐 اختبار شامل للمحلل متعدد اللغات');
    console.log('=' .repeat(80));
    console.log('🎯 الهدف: دقة 95%+ للعربية والإنجليزية');
    console.log('📚 قواعد البيانات: عربية (1012+ عبارة) + إنجليزية (1075+ عبارة)');
    console.log('=' .repeat(80));
    
    const analyzer = new MultilingualAnalyzer();
    
    // اختبار الملفات العربية
    console.log('\n🇸🇦 اختبار الملفات العربية:');
    const arabicTestFiles = [
        {
            file: 'real-research-tests/research_low_plagiarism_15percent.txt',
            expectedPercentage: 15,
            expectedLanguage: 'ar',
            description: 'بحث عربي منخفض الاستلال (15%)'
        },
        {
            file: 'real-research-tests/research_medium_plagiarism_50percent.txt',
            expectedPercentage: 50,
            expectedLanguage: 'ar',
            description: 'بحث عربي متوسط الاستلال (50%)'
        },
        {
            file: 'real-research-tests/research_high_plagiarism_85percent.txt',
            expectedPercentage: 85,
            expectedLanguage: 'ar',
            description: 'بحث عربي عالي الاستلال (85%)'
        }
    ];
    
    let arabicResults = [];
    
    for (const testCase of arabicTestFiles) {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`📄 ${testCase.description}`);
        
        try {
            const filePath = path.join(__dirname, testCase.file);
            
            if (!fs.existsSync(filePath)) {
                console.log(`❌ الملف غير موجود: ${filePath}`);
                continue;
            }
            
            const text = fs.readFileSync(filePath, 'utf8');
            const startTime = Date.now();
            
            // تحليل متعدد اللغات
            const result = await analyzer.analyzeText(text);
            
            const endTime = Date.now();
            const processingTime = (endTime - startTime) / 1000;
            
            // تقييم النتائج
            const accuracyError = Math.abs(result.plagiarismPercentage - testCase.expectedPercentage);
            const isAccurate = accuracyError <= 0.1; // دقة ±0.1%
            const languageCorrect = result.analysis.language === testCase.expectedLanguage;
            
            console.log(`✅ النتائج:`);
            console.log(`   📊 نسبة الاستلال: ${result.plagiarismPercentage}% (متوقع: ${testCase.expectedPercentage}%)`);
            console.log(`   🎯 دقة النتيجة: ${isAccurate ? '✅ ممتازة' : '❌ تحتاج تحسين'} (خطأ: ${accuracyError.toFixed(1)}%)`);
            console.log(`   🌐 اللغة المكتشفة: ${result.analysis.languageName} (${result.analysis.language})`);
            console.log(`   🔍 صحة اللغة: ${languageCorrect ? '✅ صحيحة' : '❌ خاطئة'}`);
            console.log(`   📚 قاعدة البيانات: ${result.analysis.databaseSize} عبارة`);
            console.log(`   🎭 نوع البحث: ${result.analysis.researchType.type} (ثقة: ${(result.analysis.researchType.confidence * 100).toFixed(1)}%)`);
            console.log(`   📈 مستوى الخطر: ${result.riskLevel.label} (${result.riskLevel.level}/5)`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            console.log(`   ⏱️ وقت المعالجة: ${processingTime.toFixed(3)} ثانية`);
            console.log(`   🔧 تحليل مستقل: ${result.analysis.multilingualAnalysis ? '✅ نعم' : '❌ لا'}`);
            console.log(`   🚫 تجاهل Gemini: ${result.analysis.geminiIgnored ? '✅ نعم' : '❌ لا'}`);
            
            arabicResults.push({
                file: testCase.file,
                expected: testCase.expectedPercentage,
                actual: result.plagiarismPercentage,
                error: accuracyError,
                accurate: isAccurate,
                language: result.analysis.language,
                languageCorrect: languageCorrect,
                processingTime: processingTime,
                suspiciousSegments: result.suspiciousSegments.length
            });
            
        } catch (error) {
            console.error(`❌ خطأ في تحليل ${testCase.file}:`, error.message);
        }
    }
    
    // اختبار نصوص إنجليزية تجريبية
    console.log('\n🇺🇸 اختبار النصوص الإنجليزية:');
    const englishTestTexts = [
        {
            text: `Title: The Impact of Digital Technology on University Learning Methods
            
            Abstract: This study aims to explore how digital technology affects learning methods in universities. With the rapid development of technology in recent decades, educational institutions have witnessed tremendous growth in the use of digital tools and platforms. The research methodology employed in this study is comprehensive, incorporating both quantitative and qualitative approaches to understand the various impacts of digital transformation on academic learning.
            
            The findings of this research contribute to the existing literature by providing empirical evidence for the effectiveness of digital learning tools. The study population consists of participants from diverse backgrounds, including students, faculty members, and administrative staff from multiple universities. The data collection process follows established protocols to ensure reliability and validity of results.
            
            The analysis reveals significant patterns in the data, demonstrating strong correlations between digital technology adoption and improved learning outcomes. The results show that students who actively engage with digital learning platforms demonstrate higher academic performance compared to those using traditional methods only.`,
            expectedPercentage: 15,
            expectedLanguage: 'en',
            description: 'English research - Low plagiarism (15%)'
        },
        {
            text: `Research Title: Comprehensive Analysis of Technology Integration in Higher Education
            
            The comprehensive analysis provides detailed insights into the subject matter of technology integration in educational settings. The systematic investigation reveals important patterns and relationships between digital tools and academic performance. The rigorous methodology ensures reliable and valid research outcomes that contribute to our understanding of modern educational practices.
            
            The extensive literature review establishes theoretical foundations for understanding how technology transforms learning environments. The empirical evidence supports the proposed research hypotheses regarding the positive impact of digital integration on student engagement and academic achievement. The statistical analysis demonstrates significant correlations between variables related to technology use and learning outcomes.
            
            The implementation framework provides systematic guidance for practitioners seeking to integrate technology effectively in their educational contexts. The quality assurance process ensures excellence in outcomes while maintaining high standards of academic integrity. The performance metrics demonstrate superior results when comparing technology-enhanced learning with traditional approaches.`,
            expectedPercentage: 50,
            expectedLanguage: 'en',
            description: 'English research - Medium plagiarism (50%)'
        },
        {
            text: `Advanced Research: Technology Implementation in Academic Institutions
            
            The comprehensive analysis provides detailed insights into the subject matter of educational technology implementation. The systematic investigation reveals important patterns and relationships between digital transformation and academic excellence. The rigorous methodology ensures reliable and valid research outcomes that support evidence-based decision making in higher education.
            
            The implementation framework provides systematic guidance for practitioners seeking to optimize their educational technology strategies. The quality assurance process ensures excellence in outcomes while maintaining the highest standards of academic and professional integrity. The performance metrics demonstrate superior results across multiple dimensions of educational effectiveness.
            
            The innovation demonstrates breakthrough technology advancement in educational settings. The development process incorporates cutting-edge methodologies that transform traditional academic practices. The creative solution addresses complex challenges effectively while enabling unprecedented capabilities in learning and teaching environments.
            
            The breakthrough algorithm improves performance significantly in educational data analysis. The novel approach generates unexpected insights into student learning patterns and academic achievement factors. The innovative model predicts outcomes accurately while supporting evidence-based educational decision making processes.`,
            expectedPercentage: 85,
            expectedLanguage: 'en',
            description: 'English research - High plagiarism (85%)'
        }
    ];
    
    let englishResults = [];
    
    for (const testCase of englishTestTexts) {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`📄 ${testCase.description}`);
        
        try {
            const startTime = Date.now();
            
            // تحليل متعدد اللغات
            const result = await analyzer.analyzeText(testCase.text);
            
            const endTime = Date.now();
            const processingTime = (endTime - startTime) / 1000;
            
            // تقييم النتائج
            const accuracyError = Math.abs(result.plagiarismPercentage - testCase.expectedPercentage);
            const isAccurate = accuracyError <= 0.1; // دقة ±0.1%
            const languageCorrect = result.analysis.language === testCase.expectedLanguage;
            
            console.log(`✅ النتائج:`);
            console.log(`   📊 نسبة الاستلال: ${result.plagiarismPercentage}% (متوقع: ${testCase.expectedPercentage}%)`);
            console.log(`   🎯 دقة النتيجة: ${isAccurate ? '✅ ممتازة' : '❌ تحتاج تحسين'} (خطأ: ${accuracyError.toFixed(1)}%)`);
            console.log(`   🌐 اللغة المكتشفة: ${result.analysis.languageName} (${result.analysis.language})`);
            console.log(`   🔍 صحة اللغة: ${languageCorrect ? '✅ صحيحة' : '❌ خاطئة'}`);
            console.log(`   📚 قاعدة البيانات: ${result.analysis.databaseSize} عبارة`);
            console.log(`   🎭 نوع البحث: ${result.analysis.researchType.type} (ثقة: ${(result.analysis.researchType.confidence * 100).toFixed(1)}%)`);
            console.log(`   📈 مستوى الخطر: ${result.riskLevel.label} (${result.riskLevel.level}/5)`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            console.log(`   ⏱️ وقت المعالجة: ${processingTime.toFixed(3)} ثانية`);
            console.log(`   🔧 تحليل مستقل: ${result.analysis.multilingualAnalysis ? '✅ نعم' : '❌ لا'}`);
            console.log(`   🚫 تجاهل Gemini: ${result.analysis.geminiIgnored ? '✅ نعم' : '❌ لا'}`);
            
            englishResults.push({
                description: testCase.description,
                expected: testCase.expectedPercentage,
                actual: result.plagiarismPercentage,
                error: accuracyError,
                accurate: isAccurate,
                language: result.analysis.language,
                languageCorrect: languageCorrect,
                processingTime: processingTime,
                suspiciousSegments: result.suspiciousSegments.length
            });
            
        } catch (error) {
            console.error(`❌ خطأ في تحليل النص الإنجليزي:`, error.message);
        }
    }
    
    // تقرير شامل
    console.log('\n' + '=' .repeat(80));
    console.log('📊 تقرير شامل للمحلل متعدد اللغات');
    console.log('=' .repeat(80));
    
    // إحصائيات العربية
    const arabicAccurate = arabicResults.filter(r => r.accurate).length;
    const arabicLanguageCorrect = arabicResults.filter(r => r.languageCorrect).length;
    const arabicAvgError = arabicResults.length > 0 ? arabicResults.reduce((sum, r) => sum + r.error, 0) / arabicResults.length : 0;
    const arabicAvgTime = arabicResults.length > 0 ? arabicResults.reduce((sum, r) => sum + r.processingTime, 0) / arabicResults.length : 0;
    
    console.log(`\n🇸🇦 نتائج العربية:`);
    console.log(`   📊 دقة النتائج: ${arabicAccurate}/${arabicResults.length} (${((arabicAccurate / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🌐 دقة اللغة: ${arabicLanguageCorrect}/${arabicResults.length} (${((arabicLanguageCorrect / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   📈 متوسط الخطأ: ${arabicAvgError.toFixed(2)}%`);
    console.log(`   ⏱️ متوسط الوقت: ${arabicAvgTime.toFixed(3)} ثانية`);
    
    // إحصائيات الإنجليزية
    const englishAccurate = englishResults.filter(r => r.accurate).length;
    const englishLanguageCorrect = englishResults.filter(r => r.languageCorrect).length;
    const englishAvgError = englishResults.length > 0 ? englishResults.reduce((sum, r) => sum + r.error, 0) / englishResults.length : 0;
    const englishAvgTime = englishResults.length > 0 ? englishResults.reduce((sum, r) => sum + r.processingTime, 0) / englishResults.length : 0;
    
    console.log(`\n🇺🇸 نتائج الإنجليزية:`);
    console.log(`   📊 دقة النتائج: ${englishAccurate}/${englishResults.length} (${((englishAccurate / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🌐 دقة اللغة: ${englishLanguageCorrect}/${englishResults.length} (${((englishLanguageCorrect / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   📈 متوسط الخطأ: ${englishAvgError.toFixed(2)}%`);
    console.log(`   ⏱️ متوسط الوقت: ${englishAvgTime.toFixed(3)} ثانية`);
    
    // النتيجة الإجمالية
    const totalTests = arabicResults.length + englishResults.length;
    const totalAccurate = arabicAccurate + englishAccurate;
    const totalLanguageCorrect = arabicLanguageCorrect + englishLanguageCorrect;
    const overallAccuracy = totalTests > 0 ? (totalAccurate / totalTests) * 100 : 0;
    const overallLanguageAccuracy = totalTests > 0 ? (totalLanguageCorrect / totalTests) * 100 : 0;
    
    console.log(`\n🌐 النتيجة الإجمالية:`);
    console.log(`   📊 دقة النتائج الإجمالية: ${totalAccurate}/${totalTests} (${overallAccuracy.toFixed(1)}%)`);
    console.log(`   🌐 دقة اللغة الإجمالية: ${totalLanguageCorrect}/${totalTests} (${overallLanguageAccuracy.toFixed(1)}%)`);
    console.log(`   🎯 تحقيق الهدف (95%+): ${overallAccuracy >= 95 ? '✅ نعم' : '❌ لا'}`);
    
    if (overallAccuracy >= 95) {
        console.log('\n🎉🎉🎉 مبروك! تم تحقيق الهدف بنجاح!');
        console.log('✅ المحلل متعدد اللغات يحقق دقة 95%+ للعربية والإنجليزية');
        console.log('🚀 جاهز للاستخدام في الإنتاج');
    } else {
        console.log('\n⚠️ لم يتم تحقيق الهدف بالكامل، قد نحتاج تحسينات إضافية');
    }
    
    console.log('\n' + '=' .repeat(80));
    console.log('✅ تم الانتهاء من اختبار المحلل متعدد اللغات');
}

// تشغيل الاختبار
async function main() {
    try {
        await testMultilingualAnalyzer();
    } catch (error) {
        console.error('❌ خطأ في اختبار المحلل متعدد اللغات:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testMultilingualAnalyzer };
