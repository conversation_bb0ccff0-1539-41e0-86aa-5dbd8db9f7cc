const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار شامل للمحلل فائق الدقة للوصول لدقة 95%+
 * الهدف: تحقيق دقة 95%+ على البحوث الثلاثة مهما كان الثمن
 */
async function testUltraPrecisionAnalyzer() {
    console.log('🚀 اختبار شامل للمحلل فائق الدقة للوصول لدقة 95%+ مهما كان الثمن');
    console.log('=' .repeat(80));
    console.log('🎯 الهدف: تحقيق دقة 95%+ على جميع البحوث الثلاثة');
    console.log('⚠️ النهج: مهما كان الثمن - تقنيات ثورية ومتطورة');
    console.log('=' .repeat(80));
    
    const checker = new PlagiarismChecker();
    
    // البحوث الحقيقية مع الأهداف المطلوبة
    const researchTests = [
        {
            file: 'research_low_plagiarism_15percent.txt',
            expected: 15,
            description: 'بحث أصلي منخفض الاستلال',
            targetAccuracy: 95,
            acceptableRange: [14, 16] // نطاق ضيق جداً للدقة الفائقة
        },
        {
            file: 'research_medium_plagiarism_50percent.txt',
            expected: 50,
            description: 'بحث متوسط الاستلال',
            targetAccuracy: 95,
            acceptableRange: [48, 52] // نطاق ضيق جداً للدقة الفائقة
        },
        {
            file: 'research_high_plagiarism_85percent.txt',
            expected: 85,
            description: 'بحث عالي الاستلال',
            targetAccuracy: 95,
            acceptableRange: [82, 88] // نطاق ضيق جداً للدقة الفائقة
        }
    ];
    
    let totalAccuracy = 0;
    let successfulTests = 0;
    let detailedResults = [];
    
    console.log('📊 بدء الاختبار الشامل للمحلل فائق الدقة:');
    
    for (const test of researchTests) {
        console.log(`\n${'='.repeat(60)}`);
        console.log(`📄 اختبار: ${test.file}`);
        console.log(`📋 ${test.description}`);
        console.log(`🎯 النتيجة المتوقعة: ${test.expected}%`);
        console.log(`📊 الدقة المطلوبة: ${test.targetAccuracy}%`);
        console.log(`📈 النطاق المقبول: ${test.acceptableRange[0]}% - ${test.acceptableRange[1]}%`);
        
        try {
            const filePath = path.join(__dirname, 'real-research-tests', test.file);
            
            if (!fs.existsSync(filePath)) {
                console.log(`❌ الملف غير موجود: ${filePath}`);
                continue;
            }
            
            console.log(`📁 مسار الملف: ${filePath}`);
            
            // قياس الوقت
            const startTime = Date.now();
            
            // تشغيل المحلل فائق الدقة
            const result = await checker.checkFile(filePath);
            
            const endTime = Date.now();
            const processingTime = (endTime - startTime) / 1000;
            
            const actualResult = result.plagiarismPercentage;
            const difference = Math.abs(actualResult - test.expected);
            const accuracy = Math.max(0, 100 - (difference / test.expected) * 100);
            
            // تحديد ما إذا كانت النتيجة في النطاق المقبول
            const inAcceptableRange = actualResult >= test.acceptableRange[0] && actualResult <= test.acceptableRange[1];
            const achievedTarget = accuracy >= test.targetAccuracy;
            
            console.log(`\n📈 النتائج:`);
            console.log(`   النتيجة الفعلية: ${actualResult}%`);
            console.log(`   النتيجة المتوقعة: ${test.expected}%`);
            console.log(`   الفرق: ${difference.toFixed(1)}%`);
            console.log(`   الدقة المحققة: ${accuracy.toFixed(1)}%`);
            console.log(`   في النطاق المقبول: ${inAcceptableRange ? '✅ نعم' : '❌ لا'}`);
            console.log(`   حقق الهدف (95%+): ${achievedTarget ? '✅ نعم' : '❌ لا'}`);
            console.log(`   وقت المعالجة: ${processingTime.toFixed(2)} ثانية`);
            
            // تحليل تفصيلي للنتائج
            if (result.analysis && result.analysis.ultraPrecision) {
                console.log(`\n🔍 تحليل فائق الدقة:`);
                const analysis = result.analysis;
                
                if (analysis.ultraPreciseType) {
                    console.log(`   نوع النص المحدد: ${analysis.ultraPreciseType.type}`);
                    console.log(`   ثقة التصنيف: ${(analysis.ultraPreciseType.confidence * 100).toFixed(1)}%`);
                    console.log(`   الهدف المحدد: ${analysis.ultraPreciseType.targetPercentage}%`);
                }
                
                if (analysis.advancedCharacteristics) {
                    const chars = analysis.advancedCharacteristics;
                    console.log(`   كثافة أكاديمية: ${(chars.academicDensity * 100).toFixed(1)}%`);
                    console.log(`   عدد الكلمات: ${chars.totalWords}`);
                    console.log(`   نسبة التكرار: ${(chars.repetitionRatio * 100).toFixed(1)}%`);
                    console.log(`   ثراء المفردات: ${(chars.vocabularyRichness * 100).toFixed(1)}%`);
                }
                
                if (analysis.techniquesApplied) {
                    console.log(`   التقنيات المطبقة: ${analysis.techniquesApplied.length} تقنية`);
                    analysis.techniquesApplied.forEach((technique, index) => {
                        console.log(`      ${index + 1}. ${technique}`);
                    });
                }
                
                if (analysis.ultraAnalysisResults) {
                    const ultraResults = analysis.ultraAnalysisResults;
                    console.log(`   نتائج التحليل المتقدم:`);
                    console.log(`      تطابق حرفي: ${(ultraResults.exactMatches * 100).toFixed(1)}%`);
                    console.log(`      تطابق دلالي: ${(ultraResults.semanticMatches * 100).toFixed(1)}%`);
                    console.log(`      كشف الأنماط: ${(ultraResults.patternMatches * 100).toFixed(1)}%`);
                    console.log(`      تحليل السياق: ${(ultraResults.contextMatches * 100).toFixed(1)}%`);
                    console.log(`      النتيجة الهجينة: ${(ultraResults.hybridScore * 100).toFixed(1)}%`);
                }
            }
            
            // حفظ النتائج التفصيلية
            const testResult = {
                file: test.file,
                description: test.description,
                expected: test.expected,
                actual: actualResult,
                difference: difference,
                accuracy: accuracy,
                inAcceptableRange: inAcceptableRange,
                achievedTarget: achievedTarget,
                processingTime: processingTime,
                targetAccuracy: test.targetAccuracy,
                acceptableRange: test.acceptableRange,
                analysis: result.analysis || {},
                suspiciousSegments: result.suspiciousSegments ? result.suspiciousSegments.length : 0
            };
            
            detailedResults.push(testResult);
            totalAccuracy += accuracy;
            
            if (achievedTarget) {
                successfulTests++;
                console.log(`\n🎉 نجح الاختبار! تم تحقيق دقة ${accuracy.toFixed(1)}% (الهدف: ${test.targetAccuracy}%+)`);
            } else {
                console.log(`\n⚠️ لم يحقق الهدف. دقة ${accuracy.toFixed(1)}% (الهدف: ${test.targetAccuracy}%+)`);
                console.log(`   الفجوة المطلوب سدها: ${(test.targetAccuracy - accuracy).toFixed(1)}%`);
            }
            
        } catch (error) {
            console.error(`❌ خطأ في اختبار ${test.file}:`, error.message);
            
            detailedResults.push({
                file: test.file,
                description: test.description,
                expected: test.expected,
                actual: null,
                difference: null,
                accuracy: 0,
                inAcceptableRange: false,
                achievedTarget: false,
                processingTime: 0,
                error: error.message
            });
        }
    }
    
    // حساب النتائج الإجمالية
    const avgAccuracy = totalAccuracy / researchTests.length;
    const successRate = (successfulTests / researchTests.length) * 100;
    const testsInRange = detailedResults.filter(r => r.inAcceptableRange).length;
    const rangeSuccessRate = (testsInRange / researchTests.length) * 100;
    
    console.log('\n' + '=' .repeat(80));
    console.log('📊 النتائج الإجمالية للمحلل فائق الدقة:');
    console.log('=' .repeat(80));
    
    console.log(`📈 متوسط الدقة: ${avgAccuracy.toFixed(1)}%`);
    console.log(`🎯 معدل تحقيق الهدف (95%+): ${successRate.toFixed(1)}% (${successfulTests}/${researchTests.length})`);
    console.log(`📊 معدل النطاق المقبول: ${rangeSuccessRate.toFixed(1)}% (${testsInRange}/${researchTests.length})`);
    
    // تقييم الأداء
    let performanceRating;
    let performanceDescription;
    
    if (successRate >= 100) {
        performanceRating = 'A+';
        performanceDescription = 'ممتاز - تم تحقيق الهدف بالكامل!';
    } else if (successRate >= 67) {
        performanceRating = 'A';
        performanceDescription = 'ممتاز - تم تحقيق الهدف على معظم البحوث';
    } else if (successRate >= 33) {
        performanceRating = 'B';
        performanceDescription = 'جيد - تم تحقيق الهدف على بعض البحوث';
    } else if (avgAccuracy >= 80) {
        performanceRating = 'C';
        performanceDescription = 'مقبول - دقة جيدة لكن لم يحقق الهدف';
    } else {
        performanceRating = 'D';
        performanceDescription = 'ضعيف - يحتاج تحسينات جذرية';
    }
    
    console.log(`🏆 تقييم الأداء: ${performanceRating} - ${performanceDescription}`);
    
    // تحليل مفصل لكل بحث
    console.log('\n📋 تحليل مفصل لكل بحث:');
    detailedResults.forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.file}:`);
        console.log(`   📋 ${result.description}`);
        if (result.actual !== null) {
            console.log(`   🎯 متوقع: ${result.expected}% | فعلي: ${result.actual}% | فرق: ${result.difference.toFixed(1)}%`);
            console.log(`   📊 دقة: ${result.accuracy.toFixed(1)}% | هدف: ${result.achievedTarget ? '✅' : '❌'} | نطاق: ${result.inAcceptableRange ? '✅' : '❌'}`);
            console.log(`   ⏱️ وقت: ${result.processingTime.toFixed(2)}s | أجزاء مشبوهة: ${result.suspiciousSegments}`);
        } else {
            console.log(`   ❌ خطأ: ${result.error}`);
        }
    });
    
    // توصيات للتحسين
    console.log('\n💡 توصيات للتحسين:');
    if (successRate < 100) {
        const failedTests = detailedResults.filter(r => !r.achievedTarget);
        console.log(`   🔧 البحوث التي تحتاج تحسين: ${failedTests.length}`);
        
        failedTests.forEach(test => {
            if (test.accuracy < 95) {
                const gapNeeded = 95 - test.accuracy;
                console.log(`   - ${test.file}: يحتاج تحسين ${gapNeeded.toFixed(1)}%`);
                
                if (test.actual < test.expected) {
                    console.log(`     المشكلة: النتيجة منخفضة جداً - يحتاج زيادة الحساسية`);
                } else {
                    console.log(`     المشكلة: النتيجة عالية جداً - يحتاج تقليل الحساسية`);
                }
            }
        });
        
        console.log('\n🚀 الخطوات التالية المقترحة:');
        console.log('   1. تطبيق معايرة رياضية أكثر دقة');
        console.log('   2. تحسين خوارزميات التصنيف');
        console.log('   3. توسيع قاعدة البيانات المرجعية');
        console.log('   4. تطبيق تقنيات AI/ML متطورة');
        console.log('   5. ضبط دقيق للمعاملات والعتبات');
    } else {
        console.log('   🎉 تم تحقيق الهدف بالكامل! المحلل فائق الدقة يعمل بشكل ممتاز');
    }
    
    // حفظ النتائج
    const reportData = {
        timestamp: new Date().toISOString(),
        testType: 'ultra_precision_analyzer_test',
        goal: 'تحقيق دقة 95%+ مهما كان الثمن',
        overallResults: {
            avgAccuracy: avgAccuracy,
            successRate: successRate,
            rangeSuccessRate: rangeSuccessRate,
            performanceRating: performanceRating,
            performanceDescription: performanceDescription
        },
        detailedResults: detailedResults,
        summary: {
            totalTests: researchTests.length,
            successfulTests: successfulTests,
            testsInRange: testsInRange,
            avgProcessingTime: detailedResults.reduce((sum, r) => sum + (r.processingTime || 0), 0) / detailedResults.length
        }
    };
    
    fs.writeFileSync('ultra_precision_analyzer_test_results.json', JSON.stringify(reportData, null, 2));
    console.log('\n💾 تم حفظ نتائج الاختبار: ultra_precision_analyzer_test_results.json');
    
    return reportData;
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testUltraPrecisionAnalyzer();
        
        console.log('\n🎯 خلاصة اختبار المحلل فائق الدقة:');
        console.log(`   متوسط الدقة: ${results.overallResults.avgAccuracy.toFixed(1)}%`);
        console.log(`   معدل تحقيق الهدف: ${results.overallResults.successRate.toFixed(1)}%`);
        console.log(`   التقييم: ${results.overallResults.performanceRating}`);
        console.log(`   الوصف: ${results.overallResults.performanceDescription}`);
        
        if (results.overallResults.successRate >= 100) {
            console.log('\n🎉 نجح المحلل فائق الدقة في تحقيق الهدف بالكامل!');
            console.log('✅ تم تحقيق دقة 95%+ على جميع البحوث');
        } else if (results.overallResults.successRate >= 67) {
            console.log('\n🎊 نجح المحلل فائق الدقة في تحقيق الهدف على معظم البحوث!');
            console.log('⚠️ يحتاج تحسينات طفيفة للوصول للكمال');
        } else {
            console.log('\n⚠️ المحلل فائق الدقة يحتاج تحسينات إضافية');
            console.log('🚀 الخطوة التالية: تطبيق تحسينات أكثر تطوراً');
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار المحلل فائق الدقة:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testUltraPrecisionAnalyzer };
