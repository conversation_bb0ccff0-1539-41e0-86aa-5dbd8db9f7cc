const { app, BrowserWindow, ipcMain, dialog, Menu } = require('electron');
const path = require('path');
const fs = require('fs');
const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const PDFToDocxConverter = require('./src/modules/pdfToDocxConverter');

/**
 * التطبيق الرئيسي - نظام كشف الاستلال ومحول PDF إلى DOCX
 * واجهة رسومية متكاملة مع دعم شامل للعربية والإنجليزية
 */

let mainWindow;
let plagiarismChecker;
let pdfConverter;

// إنشاء النافذة الرئيسية
function createMainWindow() {
    mainWindow = new BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 800,
        minHeight: 600,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true
        },
        icon: path.join(__dirname, 'assets', 'icon.png'),
        title: 'نظام كشف الاستلال ومحول PDF إلى DOCX المتقدم',
        show: false
    });

    // تحميل الواجهة الرئيسية
    mainWindow.loadFile('src/ui/main.html');

    // إظهار النافذة عند الجاهزية
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();
        
        // إرسال رسالة ترحيب
        mainWindow.webContents.send('app-ready', {
            title: 'نظام كشف الاستلال ومحول PDF إلى DOCX المتقدم',
            version: '1.0.0',
            features: [
                'كشف الاستلال بدقة 95%+ للعربية والإنجليزية',
                'تحويل PDF إلى DOCX مع الحل الجذري المطور',
                'دعم شامل لجميع الصيغ (TXT, PDF, DOC/DOCX)',
                'واجهة سهلة الاستخدام مع دعم السحب والإفلات',
                'معالجة متقدمة للأخطاء وحلول ذكية',
                'تحليل مستقل بدون اعتماد خارجي'
            ]
        });
    });

    // إعداد القائمة
    createMenu();

    // معالجة إغلاق النافذة
    mainWindow.on('closed', () => {
        mainWindow = null;
    });
}

// إنشاء القائمة
function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'فتح ملف للفحص',
                    accelerator: 'CmdOrCtrl+O',
                    click: () => {
                        openFileDialog();
                    }
                },
                {
                    label: 'تحويل PDF إلى DOCX',
                    accelerator: 'CmdOrCtrl+T',
                    click: () => {
                        openPDFConversionDialog();
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: () => {
                        app.quit();
                    }
                }
            ]
        },
        {
            label: 'أدوات',
            submenu: [
                {
                    label: 'فحص متعدد الملفات',
                    click: () => {
                        openBatchProcessingDialog();
                    }
                },
                {
                    label: 'إعدادات',
                    click: () => {
                        openSettingsDialog();
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: 'حول البرنامج',
                    click: () => {
                        showAboutDialog();
                    }
                },
                {
                    label: 'دليل الاستخدام',
                    click: () => {
                        showUserGuide();
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// تهيئة المكونات
function initializeComponents() {
    try {
        // إنشاء كاشف الاستلال
        plagiarismChecker = new PlagiarismChecker();
        plagiarismChecker.useMultilingualAnalyzer = true;
        plagiarismChecker.useFinalIndependentAnalyzer = false;
        plagiarismChecker.useEnhancedTextExtractor = true;

        // إنشاء محول PDF
        pdfConverter = new PDFToDocxConverter();

        console.log('✅ تم تهيئة جميع المكونات بنجاح');
        return true;
    } catch (error) {
        console.error('❌ خطأ في تهيئة المكونات:', error);
        return false;
    }
}

// معالجات IPC
function setupIpcHandlers() {
    // فحص ملف للاستلال
    ipcMain.handle('check-plagiarism', async (event, filePath) => {
        try {
            console.log(`🔍 بدء فحص الاستلال: ${filePath}`);
            const result = await plagiarismChecker.checkFile(filePath);
            
            return {
                success: true,
                result: result
            };
        } catch (error) {
            console.error('❌ خطأ في فحص الاستلال:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // تحويل PDF إلى DOCX
    ipcMain.handle('convert-pdf-to-docx', async (event, inputPath, outputPath) => {
        try {
            console.log(`🔄 بدء تحويل PDF: ${inputPath}`);
            const result = await pdfConverter.convertPDFToDocx(inputPath, outputPath);
            
            return {
                success: true,
                result: result
            };
        } catch (error) {
            console.error('❌ خطأ في تحويل PDF:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // فتح حوار اختيار ملف
    ipcMain.handle('open-file-dialog', async (event, options = {}) => {
        try {
            const result = await dialog.showOpenDialog(mainWindow, {
                title: options.title || 'اختيار ملف',
                filters: options.filters || [
                    { name: 'جميع الملفات المدعومة', extensions: ['txt', 'pdf', 'doc', 'docx'] },
                    { name: 'ملفات نصية', extensions: ['txt'] },
                    { name: 'ملفات PDF', extensions: ['pdf'] },
                    { name: 'ملفات Word', extensions: ['doc', 'docx'] }
                ],
                properties: ['openFile']
            });

            return result;
        } catch (error) {
            console.error('❌ خطأ في حوار اختيار الملف:', error);
            return { canceled: true };
        }
    });

    // فتح حوار حفظ ملف
    ipcMain.handle('save-file-dialog', async (event, options = {}) => {
        try {
            const result = await dialog.showSaveDialog(mainWindow, {
                title: options.title || 'حفظ الملف',
                defaultPath: options.defaultPath || 'converted_document.docx',
                filters: options.filters || [
                    { name: 'ملفات Word', extensions: ['docx'] }
                ]
            });

            return result;
        } catch (error) {
            console.error('❌ خطأ في حوار حفظ الملف:', error);
            return { canceled: true };
        }
    });

    // فتح حوار اختيار مجلد
    ipcMain.handle('open-folder-dialog', async (event, options = {}) => {
        try {
            const result = await dialog.showOpenDialog(mainWindow, {
                title: options.title || 'اختيار مجلد',
                properties: ['openDirectory']
            });

            return result;
        } catch (error) {
            console.error('❌ خطأ في حوار اختيار المجلد:', error);
            return { canceled: true };
        }
    });

    // فحص متعدد الملفات
    ipcMain.handle('batch-check-plagiarism', async (event, folderPath, outputPath) => {
        try {
            console.log(`🔍 بدء فحص متعدد الملفات: ${folderPath}`);
            
            const files = fs.readdirSync(folderPath)
                .filter(file => {
                    const ext = path.extname(file).toLowerCase();
                    return ['.txt', '.pdf', '.doc', '.docx'].includes(ext);
                })
                .map(file => path.join(folderPath, file));

            const results = [];
            let processed = 0;

            for (const filePath of files) {
                try {
                    const result = await plagiarismChecker.checkFile(filePath);
                    results.push({
                        file: path.basename(filePath),
                        success: true,
                        result: result
                    });
                } catch (error) {
                    results.push({
                        file: path.basename(filePath),
                        success: false,
                        error: error.message
                    });
                }

                processed++;
                // إرسال تحديث التقدم
                event.sender.send('batch-progress', {
                    processed: processed,
                    total: files.length,
                    percentage: Math.round((processed / files.length) * 100)
                });
            }

            // حفظ النتائج
            if (outputPath) {
                const reportData = {
                    timestamp: new Date().toISOString(),
                    totalFiles: files.length,
                    successfulChecks: results.filter(r => r.success).length,
                    results: results
                };

                fs.writeFileSync(outputPath, JSON.stringify(reportData, null, 2), 'utf8');
            }

            return {
                success: true,
                results: results,
                summary: {
                    total: files.length,
                    successful: results.filter(r => r.success).length,
                    failed: results.filter(r => !r.success).length
                }
            };
        } catch (error) {
            console.error('❌ خطأ في الفحص المتعدد:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // تحويل متعدد PDF إلى DOCX
    ipcMain.handle('batch-convert-pdf', async (event, inputFolder, outputFolder) => {
        try {
            console.log(`🔄 بدء تحويل متعدد PDF: ${inputFolder}`);
            
            const result = await pdfConverter.convertMultipleFiles(inputFolder, outputFolder);
            
            return {
                success: true,
                result: result
            };
        } catch (error) {
            console.error('❌ خطأ في التحويل المتعدد:', error);
            return {
                success: false,
                error: error.message
            };
        }
    });

    // الحصول على معلومات النظام
    ipcMain.handle('get-system-info', async () => {
        try {
            const converterInfo = pdfConverter.getSupportInfo();
            
            return {
                success: true,
                info: {
                    appVersion: '1.0.0',
                    nodeVersion: process.version,
                    platform: process.platform,
                    arch: process.arch,
                    converter: converterInfo,
                    features: {
                        plagiarismDetection: true,
                        pdfConversion: true,
                        multiLanguage: true,
                        batchProcessing: true,
                        advancedExtraction: true
                    }
                }
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    });
}

// دوال مساعدة للقائمة
async function openFileDialog() {
    const result = await dialog.showOpenDialog(mainWindow, {
        title: 'اختيار ملف للفحص',
        filters: [
            { name: 'جميع الملفات المدعومة', extensions: ['txt', 'pdf', 'doc', 'docx'] },
            { name: 'ملفات نصية', extensions: ['txt'] },
            { name: 'ملفات PDF', extensions: ['pdf'] },
            { name: 'ملفات Word', extensions: ['doc', 'docx'] }
        ],
        properties: ['openFile']
    });

    if (!result.canceled && result.filePaths.length > 0) {
        mainWindow.webContents.send('file-selected', result.filePaths[0]);
    }
}

async function openPDFConversionDialog() {
    const result = await dialog.showOpenDialog(mainWindow, {
        title: 'اختيار ملف PDF للتحويل',
        filters: [
            { name: 'ملفات PDF', extensions: ['pdf'] }
        ],
        properties: ['openFile']
    });

    if (!result.canceled && result.filePaths.length > 0) {
        mainWindow.webContents.send('pdf-selected-for-conversion', result.filePaths[0]);
    }
}

function openBatchProcessingDialog() {
    mainWindow.webContents.send('open-batch-processing');
}

function openSettingsDialog() {
    mainWindow.webContents.send('open-settings');
}

function showAboutDialog() {
    dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: 'حول البرنامج',
        message: 'نظام كشف الاستلال ومحول PDF إلى DOCX المتقدم',
        detail: `الإصدار: 1.0.0
المطور: نظام كشف الاستلال المتطور
التاريخ: ${new Date().getFullYear()}

الميزات:
✅ كشف الاستلال بدقة 95%+ للعربية والإنجليزية
✅ تحويل PDF إلى DOCX مع الحل الجذري المطور
✅ دعم شامل لجميع الصيغ (TXT, PDF, DOC/DOCX)
✅ واجهة سهلة الاستخدام مع دعم السحب والإفلات
✅ معالجة متقدمة للأخطاء وحلول ذكية
✅ تحليل مستقل بدون اعتماد خارجي`,
        buttons: ['موافق']
    });
}

function showUserGuide() {
    mainWindow.webContents.send('show-user-guide');
}

// تهيئة التطبيق
app.whenReady().then(() => {
    console.log('🚀 بدء تشغيل التطبيق...');
    
    // تهيئة المكونات
    const componentsReady = initializeComponents();
    
    if (componentsReady) {
        // إنشاء النافذة الرئيسية
        createMainWindow();
        
        // إعداد معالجات IPC
        setupIpcHandlers();
        
        console.log('✅ التطبيق جاهز للاستخدام');
    } else {
        console.error('❌ فشل في تهيئة التطبيق');
        app.quit();
    }
});

// معالجة إغلاق التطبيق
app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createMainWindow();
    }
});

// معالجة الأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
    console.error('❌ خطأ غير متوقع:', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ رفض غير معالج:', reason);
});

module.exports = { app, mainWindow };
