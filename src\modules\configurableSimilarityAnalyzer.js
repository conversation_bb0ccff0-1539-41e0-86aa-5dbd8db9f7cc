const fs = require('fs');
const path = require('path');

/**
 * محلل التشابه القابل للتخصيص - للوصول لدقة 95%+
 */
class ConfigurableSimilarityAnalyzer {
    constructor() {
        // إعدادات افتراضية قابلة للتعديل
        this.config = {
            thresholds: {
                high: 0.60,     // 60%
                medium: 0.45,   // 45%
                low: 0.30       // 30%
            },
            weights: {
                jaccard: 0.40,      // 40%
                cosine: 0.35,       // 35%
                levenshtein: 0.20,  // 20%
                semantic: 0.05      // 5%
            },
            dynamicThresholds: {
                enabled: true,
                minThreshold: 0.35,  // 35%
                maxThreshold: 0.65   // 65%
            }
        };
        
        this.loadReferenceData();
        this.createSearchIndex();
        
        console.log('🔧 تم تهيئة محلل التشابه القابل للتخصيص');
    }
    
    /**
     * تحديث الإعدادات
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        console.log('⚙️ تم تحديث إعدادات المحلل');
    }
    
    /**
     * تحميل البيانات المرجعية
     */
    loadReferenceData() {
        try {
            const dataPath = path.join(__dirname, '../data/reference_phrases.json');
            const data = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
            this.referenceTexts = data.academic_phrases || [];
            
            console.log(`✅ تم تحميل ${this.referenceTexts.length} عبارة مرجعية`);
        } catch (error) {
            console.error('❌ خطأ في تحميل البيانات:', error.message);
            this.referenceTexts = [];
        }
    }
    
    /**
     * إنشاء فهرس البحث
     */
    createSearchIndex() {
        this.searchIndex = new Map();
        
        this.referenceTexts.forEach((text, index) => {
            const words = this.extractWords(text);
            words.forEach(word => {
                if (!this.searchIndex.has(word)) {
                    this.searchIndex.set(word, []);
                }
                this.searchIndex.get(word).push(index);
            });
        });
        
        console.log(`🔍 تم إنشاء فهرس بحث لـ ${this.searchIndex.size} كلمة فريدة`);
    }
    
    /**
     * تحليل التشابه الرئيسي
     */
    async analyzeText(inputText) {
        console.log('🔬 بدء التحليل القابل للتخصيص...');
        
        const processedInput = this.preprocessText(inputText);
        const inputWords = this.extractWords(processedInput);
        
        // العثور على النصوص المرجعية ذات الصلة
        const relevantReferences = this.findRelevantReferences(inputWords);
        
        console.log(`🔍 فحص ${Math.min(relevantReferences.length, 30)} نص مرجعي من أصل ${this.referenceTexts.length}`);
        
        let maxSimilarity = 0;
        let totalSimilarity = 0;
        let suspiciousSegments = [];
        let similarities = { jaccard: 0, cosine: 0, levenshtein: 0, semantic: 0, exactMatch: 0 };
        
        // فحص النصوص المرجعية ذات الصلة
        const referencesToCheck = relevantReferences.slice(0, 30); // أفضل 30 نص
        
        for (const refIndex of referencesToCheck) {
            const referenceText = this.referenceTexts[refIndex];
            const refProcessed = this.preprocessText(referenceText);
            
            // حساب التشابه بالطرق المختلفة
            const jaccardSim = this.calculateJaccardSimilarity(processedInput, refProcessed);
            const cosineSim = this.calculateCosineSimilarity(processedInput, refProcessed);
            const levenshteinSim = this.calculateLevenshteinSimilarity(processedInput, refProcessed);
            const semanticSim = this.calculateSemanticSimilarity(processedInput, refProcessed);
            const exactMatchSim = this.calculateExactMatchSimilarity(processedInput, refProcessed);
            
            // حساب التشابه المركب باستخدام الأوزان القابلة للتخصيص
            const combinedSimilarity = (
                jaccardSim * this.config.weights.jaccard +
                cosineSim * this.config.weights.cosine +
                levenshteinSim * this.config.weights.levenshtein +
                semanticSim * this.config.weights.semantic
            ) + exactMatchSim * 0.1; // مكافأة للتطابق الحرفي
            
            totalSimilarity += combinedSimilarity;
            maxSimilarity = Math.max(maxSimilarity, combinedSimilarity);
            
            // تحديث متوسط التشابه
            similarities.jaccard = Math.max(similarities.jaccard, jaccardSim);
            similarities.cosine = Math.max(similarities.cosine, cosineSim);
            similarities.levenshtein = Math.max(similarities.levenshtein, levenshteinSim);
            similarities.semantic = Math.max(similarities.semantic, semanticSim);
            similarities.exactMatch = Math.max(similarities.exactMatch, exactMatchSim);
            
            // إذا كان التشابه عالي، ابحث عن الأجزاء المشبوهة
            if (combinedSimilarity > this.config.thresholds.medium) {
                const segments = this.findSuspiciousSegments(inputText, referenceText, combinedSimilarity);
                suspiciousSegments.push(...segments);
            }
        }
        
        // حساب متوسط التشابه
        const avgSimilarity = referencesToCheck.length > 0 ? totalSimilarity / referencesToCheck.length : 0;
        
        // حساب العتبة التكيفية
        const adaptiveThreshold = this.calculateAdaptiveThreshold(inputText, maxSimilarity);
        
        // حساب النسبة النهائية للاستلال
        const plagiarismPercentage = this.calculateFinalPlagiarismScore(
            maxSimilarity, 
            avgSimilarity, 
            suspiciousSegments.length,
            adaptiveThreshold
        );
        
        // تصنيف مستوى الخطر
        const riskLevel = this.classifyRiskLevel(plagiarismPercentage, adaptiveThreshold);
        
        console.log(`📈 نتائج التحليل: ${plagiarismPercentage}% استلال، ${suspiciousSegments.length} جزء مشبوه`);
        
        return {
            plagiarismPercentage,
            riskLevel,
            suspiciousSegments: suspiciousSegments.slice(0, 15),
            analysis: {
                maxSimilarity,
                avgSimilarity,
                adaptiveThreshold,
                similarities,
                weights: this.config.weights,
                referencesChecked: referencesToCheck.length
            }
        };
    }
    
    /**
     * العثور على النصوص المرجعية ذات الصلة
     */
    findRelevantReferences(inputWords) {
        const relevanceScores = new Map();
        
        inputWords.forEach(word => {
            if (this.searchIndex.has(word)) {
                const references = this.searchIndex.get(word);
                references.forEach(refIndex => {
                    const currentScore = relevanceScores.get(refIndex) || 0;
                    relevanceScores.set(refIndex, currentScore + 1);
                });
            }
        });
        
        // ترتيب حسب الصلة
        return Array.from(relevanceScores.entries())
            .sort((a, b) => b[1] - a[1])
            .map(entry => entry[0]);
    }
    
    /**
     * حساب تشابه Jaccard
     */
    calculateJaccardSimilarity(text1, text2) {
        const words1 = new Set(this.extractWords(text1));
        const words2 = new Set(this.extractWords(text2));
        
        const intersection = new Set([...words1].filter(x => words2.has(x)));
        const union = new Set([...words1, ...words2]);
        
        return union.size > 0 ? intersection.size / union.size : 0;
    }
    
    /**
     * حساب تشابه Cosine
     */
    calculateCosineSimilarity(text1, text2) {
        const words1 = this.extractWords(text1);
        const words2 = this.extractWords(text2);
        
        const allWords = [...new Set([...words1, ...words2])];
        const vector1 = allWords.map(word => words1.filter(w => w === word).length);
        const vector2 = allWords.map(word => words2.filter(w => w === word).length);
        
        const dotProduct = vector1.reduce((sum, val, i) => sum + val * vector2[i], 0);
        const magnitude1 = Math.sqrt(vector1.reduce((sum, val) => sum + val * val, 0));
        const magnitude2 = Math.sqrt(vector2.reduce((sum, val) => sum + val * val, 0));
        
        return (magnitude1 && magnitude2) ? dotProduct / (magnitude1 * magnitude2) : 0;
    }
    
    /**
     * حساب تشابه Levenshtein
     */
    calculateLevenshteinSimilarity(text1, text2) {
        const distance = this.levenshteinDistance(text1, text2);
        const maxLength = Math.max(text1.length, text2.length);
        return maxLength > 0 ? 1 - (distance / maxLength) : 0;
    }
    
    /**
     * حساب التشابه الدلالي
     */
    calculateSemanticSimilarity(text1, text2) {
        // تشابه بسيط بناءً على الكلمات المفتاحية
        const keywords1 = this.extractKeywords(text1);
        const keywords2 = this.extractKeywords(text2);
        
        const commonKeywords = keywords1.filter(k => keywords2.includes(k));
        const totalKeywords = Math.max(keywords1.length, keywords2.length);
        
        return totalKeywords > 0 ? commonKeywords.length / totalKeywords : 0;
    }
    
    /**
     * حساب التطابق الحرفي
     */
    calculateExactMatchSimilarity(text1, text2) {
        let matchScore = 0;
        const minLength = 15; // الحد الأدنى لطول العبارة
        
        for (let len = 50; len >= minLength; len--) {
            for (let i = 0; i <= text1.length - len; i++) {
                const substring = text1.substring(i, i + len);
                if (text2.includes(substring)) {
                    matchScore += len / text1.length;
                }
            }
        }
        
        return Math.min(1.0, matchScore);
    }
    
    /**
     * حساب العتبة التكيفية
     */
    calculateAdaptiveThreshold(inputText, maxSimilarity) {
        if (!this.config.dynamicThresholds.enabled) {
            return this.config.thresholds.medium;
        }
        
        const textLength = inputText.length;
        const textType = this.detectTextType(inputText);
        
        let baseThreshold = this.config.thresholds.medium;
        
        // تعديل حسب طول النص
        if (textLength < 500) {
            baseThreshold += 0.05; // نصوص قصيرة تحتاج عتبة أعلى
        } else if (textLength > 2000) {
            baseThreshold -= 0.05; // نصوص طويلة تحتاج عتبة أقل
        }
        
        // تعديل حسب نوع النص
        if (textType === 'academic') {
            baseThreshold -= 0.02; // النصوص الأكاديمية أكثر عرضة للاستلال
        }
        
        // ضمان البقاء ضمن الحدود
        return Math.max(
            this.config.dynamicThresholds.minThreshold,
            Math.min(this.config.dynamicThresholds.maxThreshold, baseThreshold)
        );
    }
    
    /**
     * حساب النتيجة النهائية للاستلال
     */
    calculateFinalPlagiarismScore(maxSimilarity, avgSimilarity, suspiciousCount, threshold) {
        // وزن أكبر للتشابه الأقصى
        let score = maxSimilarity * 0.7 + avgSimilarity * 0.3;
        
        // مكافأة للأجزاء المشبوهة
        const suspiciousBonus = Math.min(0.2, suspiciousCount * 0.02);
        score += suspiciousBonus;
        
        // تطبيق العتبة التكيفية
        if (score > threshold) {
            score = Math.min(1.0, score * 1.2); // تضخيم النتائج فوق العتبة
        } else {
            score = score * 0.8; // تقليل النتائج تحت العتبة
        }
        
        return Math.round(score * 100);
    }
    
    /**
     * تصنيف مستوى الخطر
     */
    classifyRiskLevel(percentage, threshold) {
        const normalizedThreshold = threshold * 100;
        
        if (percentage >= normalizedThreshold + 20) return { level: 5, label: 'عالي جداً' };
        if (percentage >= normalizedThreshold + 10) return { level: 4, label: 'عالي' };
        if (percentage >= normalizedThreshold) return { level: 3, label: 'متوسط' };
        if (percentage >= normalizedThreshold - 10) return { level: 2, label: 'منخفض' };
        return { level: 1, label: 'ضئيل' };
    }
    
    /**
     * العثور على الأجزاء المشبوهة
     */
    findSuspiciousSegments(inputText, referenceText, similarity) {
        const segments = [];
        const sentences = inputText.split(/[.!?؟]/).filter(s => s.trim().length > 20);
        
        sentences.forEach((sentence, index) => {
            const processedSentence = this.preprocessText(sentence);
            const processedReference = this.preprocessText(referenceText);
            
            if (processedReference.includes(processedSentence.substring(0, 30)) ||
                this.calculateJaccardSimilarity(processedSentence, processedReference) > 0.6) {
                
                segments.push({
                    text: sentence.trim(),
                    similarity: similarity,
                    startIndex: index,
                    type: 'suspicious'
                });
            }
        });
        
        return segments;
    }
    
    /**
     * معالجة النص
     */
    preprocessText(text) {
        return text
            .replace(/[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\w]/g, ' ')
            .replace(/\s+/g, ' ')
            .trim()
            .toLowerCase();
    }
    
    /**
     * استخراج الكلمات
     */
    extractWords(text) {
        return this.preprocessText(text)
            .split(/\s+/)
            .filter(word => word.length > 2);
    }
    
    /**
     * استخراج الكلمات المفتاحية
     */
    extractKeywords(text) {
        const stopWords = new Set(['في', 'من', 'إلى', 'على', 'عن', 'مع', 'هذا', 'هذه', 'التي', 'الذي', 'أن', 'كان', 'كانت']);
        return this.extractWords(text)
            .filter(word => word.length > 3 && !stopWords.has(word))
            .slice(0, 15);
    }
    
    /**
     * كشف نوع النص
     */
    detectTextType(text) {
        const academicKeywords = ['دراسة', 'بحث', 'تحليل', 'نتائج', 'استنتاج', 'فرضية'];
        const keywordCount = academicKeywords.filter(keyword => text.includes(keyword)).length;
        
        return keywordCount >= 2 ? 'academic' : 'general';
    }
    
    /**
     * حساب مسافة Levenshtein
     */
    levenshteinDistance(str1, str2) {
        const matrix = Array(str2.length + 1).fill().map(() => Array(str1.length + 1).fill(0));
        
        for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
        for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;
        
        for (let j = 1; j <= str2.length; j++) {
            for (let i = 1; i <= str1.length; i++) {
                const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
                matrix[j][i] = Math.min(
                    matrix[j - 1][i] + 1,
                    matrix[j][i - 1] + 1,
                    matrix[j - 1][i - 1] + cost
                );
            }
        }
        
        return matrix[str2.length][str1.length];
    }
}

module.exports = ConfigurableSimilarityAnalyzer;
