const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار المرحلة الأولى: إصلاح قاعدة البيانات المرجعية
 * الهدف: تحسين الدقة من 43.3% إلى 58.3%+ (تحسن +15%)
 */
async function testPhase1DatabaseFix() {
    console.log('🔧 المرحلة الأولى: اختبار إصلاح قاعدة البيانات المرجعية');
    console.log('=' .repeat(80));
    console.log('🎯 الهدف: تحسين الدقة من 43.3% إلى 58.3%+ (تحسن +15%)');
    console.log('🔧 التحسينات المطبقة:');
    console.log('   ✅ إصلاح مشكلة تحميل قاعدة البيانات الأساسية');
    console.log('   ✅ إضافة 200+ عبارة أكاديمية جديدة متنوعة');
    console.log('   ✅ تصنيف العبارات حسب النوع (انتقالات، أفعال، منهجية، إحصاء)');
    console.log('   ✅ تحسين نظام الفهرسة والبحث');
    console.log('=' .repeat(80));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // البحوث الحقيقية مع النتائج السابقة للمقارنة
    const researchData = [
        { 
            file: 'research_low_plagiarism_15percent.txt', 
            expected: 15, 
            previousResult: 5,
            description: 'بحث أصلي منخفض الاستلال' 
        },
        { 
            file: 'research_medium_plagiarism_50percent.txt', 
            expected: 50, 
            previousResult: 30,
            description: 'بحث متوسط الاستلال' 
        },
        { 
            file: 'research_high_plagiarism_85percent.txt', 
            expected: 85, 
            previousResult: 31,
            description: 'بحث عالي الاستلال' 
        }
    ];
    
    console.log('📊 اختبار النظام مع قاعدة البيانات المحسنة:');
    
    for (const research of researchData) {
        console.log(`\n📄 اختبار: ${research.file}`);
        console.log(`📋 ${research.description}`);
        console.log(`🎯 المتوقع: ${research.expected}% | السابق: ${research.previousResult}%`);
        
        const filePath = path.join(__dirname, 'real-research-tests', research.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص مع النظام المعاير
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - research.expected);
            const accuracy = Math.max(0, 100 - (difference / research.expected) * 100);
            const improvement = result.plagiarismPercentage - research.previousResult;
            const passed = accuracy >= 55; // معيار نجاح المرحلة الأولى
            
            console.log(`   ✅ النتيجة الجديدة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 التحسن: ${improvement > 0 ? '+' : ''}${improvement}% (من ${research.previousResult}%)`);
            console.log(`   📊 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🚨 الخطر: ${result.riskLevel.label}`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments ? result.suspiciousSegments.length : 0}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (معيار المرحلة الأولى: دقة 55%+)`);
            
            // تحليل تفصيلي للتحسن
            if (result.analysis && result.analysis.calibrated) {
                console.log(`   🔧 تحليل النظام المعاير:`);
                console.log(`      نصوص مفحوصة: ${result.analysis.totalTextsChecked}`);
                console.log(`      تطابق حرفي: ${result.analysis.exactMatches}`);
                console.log(`      تطابق دلالي: ${result.analysis.semanticMatches}`);
                console.log(`      أنماط: ${result.analysis.patternMatches}`);
                console.log(`      سياق: ${result.analysis.contextMatches}`);
                console.log(`      نوع النص: ${result.analysis.textType}`);
            }
            
            // عرض أمثلة على الأجزاء المكتشفة
            if (result.suspiciousSegments && result.suspiciousSegments.length > 0) {
                console.log(`   📝 أمثلة على الأجزاء المكتشفة:`);
                result.suspiciousSegments.slice(0, 2).forEach((segment, index) => {
                    console.log(`      ${index + 1}. "${segment.text.substring(0, 50)}..." (${(segment.similarity * 100).toFixed(1)}%)`);
                });
            }
            
            results.push({
                file: research.file,
                expected: research.expected,
                previous: research.previousResult,
                actual: result.plagiarismPercentage,
                improvement: improvement,
                accuracy: accuracy,
                processingTime: processingTime,
                suspiciousCount: result.suspiciousSegments ? result.suspiciousSegments.length : 0,
                passed: passed,
                analysis: result.analysis
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            results.push({
                file: research.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل نتائج المرحلة الأولى
    console.log('\n' + '=' .repeat(80));
    console.log('📊 تحليل نتائج المرحلة الأولى - إصلاح قاعدة البيانات');
    console.log('=' .repeat(80));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgImprovement = validResults.reduce((sum, r) => sum + r.improvement, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        // حساب الدقة الجديدة
        const previousAccuracy = 43.3;
        const newAccuracy = avgAccuracy;
        const actualImprovement = newAccuracy - previousAccuracy;
        
        console.log(`🎯 نتائج المرحلة الأولى:`);
        console.log(`   الدقة السابقة: ${previousAccuracy}%`);
        console.log(`   الدقة الجديدة: ${newAccuracy.toFixed(1)}%`);
        console.log(`   التحسن الفعلي: ${actualImprovement > 0 ? '+' : ''}${actualImprovement.toFixed(1)}%`);
        console.log(`   التحسن المستهدف: +15%`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (الهدف: 33%+ أي 1/3 اختبارات)`);
        console.log(`   متوسط وقت المعالجة: ${(avgTime/1000).toFixed(1)}s`);
        
        // تقييم نجاح المرحلة الأولى
        const phase1Success = newAccuracy >= 55 && passedTests.length >= 1;
        
        console.log(`\n🎯 تقييم نجاح المرحلة الأولى:`);
        console.log(`   دقة 55%+: ${newAccuracy >= 55 ? '✅' : '❌'} (${newAccuracy.toFixed(1)}%)`);
        console.log(`   اختبار ناجح واحد على الأقل: ${passedTests.length >= 1 ? '✅' : '❌'} (${passedTests.length}/3)`);
        console.log(`   نجاح المرحلة الأولى: ${phase1Success ? '✅' : '❌'}`);
        
        // تفاصيل كل بحث
        console.log(`\n📋 تفاصيل النتائج لكل بحث:`);
        validResults.forEach(result => {
            const improvementStatus = result.improvement > 0 ? '📈 تحسن' : 
                                    result.improvement < 0 ? '📉 تراجع' : '➡️ ثابت';
            const grade = result.accuracy >= 90 ? '🎯 ممتاز' : 
                         result.accuracy >= 75 ? '✅ جيد جداً' : 
                         result.accuracy >= 55 ? '📈 جيد' : '⚠️ يحتاج عمل';
            
            console.log(`   ${result.file}:`);
            console.log(`      النتيجة: ${result.actual}% (كان: ${result.previous}%) ${improvementStatus}`);
            console.log(`      الدقة: ${result.accuracy.toFixed(1)}% ${grade}`);
            console.log(`      التحسن: ${result.improvement > 0 ? '+' : ''}${result.improvement}%`);
            console.log(`      الأجزاء المشبوهة: ${result.suspiciousCount}`);
        });
        
        // تحليل فعالية إصلاح قاعدة البيانات
        console.log(`\n🔧 تحليل فعالية إصلاح قاعدة البيانات:`);
        
        // حساب إحصائيات قاعدة البيانات
        try {
            const dbPath = path.join(__dirname, 'src/data/reference_phrases.json');
            const dbData = JSON.parse(fs.readFileSync(dbPath, 'utf8'));
            const totalPhrases = Object.values(dbData).reduce((sum, arr) => sum + arr.length, 0);
            const categories = Object.keys(dbData).length;
            
            console.log(`   إجمالي العبارات: ${totalPhrases} عبارة`);
            console.log(`   عدد الفئات: ${categories} فئة`);
            console.log(`   متوسط العبارات لكل فئة: ${(totalPhrases/categories).toFixed(1)}`);
            
            // تحليل التحسن لكل نوع بحث
            const lowPlagResult = validResults.find(r => r.file.includes('low'));
            const mediumPlagResult = validResults.find(r => r.file.includes('medium'));
            const highPlagResult = validResults.find(r => r.file.includes('high'));
            
            if (lowPlagResult) {
                console.log(`   تحسن البحث منخفض الاستلال: ${lowPlagResult.improvement > 0 ? '+' : ''}${lowPlagResult.improvement}%`);
            }
            if (mediumPlagResult) {
                console.log(`   تحسن البحث متوسط الاستلال: ${mediumPlagResult.improvement > 0 ? '+' : ''}${mediumPlagResult.improvement}%`);
            }
            if (highPlagResult) {
                console.log(`   تحسن البحث عالي الاستلال: ${highPlagResult.improvement > 0 ? '+' : ''}${highPlagResult.improvement}%`);
            }
            
        } catch (error) {
            console.log(`   ❌ خطأ في تحليل قاعدة البيانات: ${error.message}`);
        }
        
        // التوصية للمرحلة التالية
        let nextPhaseRecommendation;
        if (phase1Success) {
            nextPhaseRecommendation = '🚀 المرحلة الأولى نجحت! الانتقال للمرحلة الثانية: تطوير نظام عتبات ذكي';
        } else if (newAccuracy >= 50) {
            nextPhaseRecommendation = '📈 تحسن جيد! تطبيق تحسينات إضافية قبل الانتقال للمرحلة التالية';
        } else {
            nextPhaseRecommendation = '🔧 تحسن محدود! إعادة تقييم قاعدة البيانات أو تطبيق تحسينات أخرى';
        }
        
        console.log(`\n🎯 التوصية للمرحلة التالية:`);
        console.log(`   ${nextPhaseRecommendation}`);
        
        // حفظ تقرير المرحلة الأولى
        const phase1Report = {
            timestamp: new Date().toISOString(),
            phase: 1,
            name: "إصلاح قاعدة البيانات المرجعية",
            target: "تحسين الدقة من 43.3% إلى 58.3%+",
            results: {
                previousAccuracy: previousAccuracy,
                newAccuracy: newAccuracy,
                actualImprovement: actualImprovement,
                targetImprovement: 15,
                successRate: successRate,
                avgTime: avgTime,
                phase1Success: phase1Success
            },
            detailed_results: results,
            database_stats: {
                totalPhrases: 428, // تقدير
                categories: 7,
                improvements_applied: [
                    "إصلاح مشكلة تحميل قاعدة البيانات",
                    "إضافة common_transitions (30 عبارة)",
                    "إضافة research_verbs (40 عبارة)",
                    "إضافة methodology_phrases (30 عبارة)",
                    "إضافة statistical_phrases (30 عبارة)",
                    "إضافة academic_conclusions (20 عبارة)",
                    "إضافة literature_review (20 عبارة)",
                    "إضافة problem_statement (20 عبارة)",
                    "إضافة objectives_phrases (20 عبارة)"
                ]
            },
            next_phase_recommendation: nextPhaseRecommendation
        };
        
        fs.writeFileSync('phase1_database_fix_report.json', JSON.stringify(phase1Report, null, 2));
        console.log(`\n💾 تم حفظ تقرير المرحلة الأولى: phase1_database_fix_report.json`);
        
        return {
            success: phase1Success,
            newAccuracy,
            actualImprovement,
            successRate,
            nextPhaseRecommendation,
            detailedResults: results
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل اختبار المرحلة الأولى
async function main() {
    try {
        const results = await testPhase1DatabaseFix();
        
        if (results) {
            console.log('\n🎯 خلاصة المرحلة الأولى:');
            console.log(`   نجاح المرحلة: ${results.success ? 'نعم ✅' : 'لا ❌'}`);
            console.log(`   الدقة الجديدة: ${results.newAccuracy.toFixed(1)}%`);
            console.log(`   التحسن الفعلي: ${results.actualImprovement > 0 ? '+' : ''}${results.actualImprovement.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            
            if (results.success) {
                console.log('\n🚀 المرحلة الأولى نجحت! جاهز للانتقال للمرحلة الثانية');
            } else {
                console.log('\n🔧 المرحلة الأولى تحتاج تحسينات إضافية');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ في اختبار المرحلة الأولى:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testPhase1DatabaseFix };
