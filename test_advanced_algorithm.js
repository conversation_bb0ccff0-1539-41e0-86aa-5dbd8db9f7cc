const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * اختبار الخوارزمية المتقدمة الجديدة
 */
async function testAdvancedAlgorithm() {
    console.log('🚀 اختبار الخوارزمية المتقدمة الجديدة');
    console.log('=' .repeat(70));
    console.log('🔧 التحسين المطبق:');
    console.log('   🧠 خوارزمية hybrid جديدة تماماً');
    console.log('   🎯 تركيز على الكشف الدقيق للعبارات المطابقة');
    console.log('   ⚖️ أوزان محسنة: Exact 35%, Fuzzy 25%, Semantic 20%, Structural 20%');
    console.log('   🔍 بحث متقدم عن العبارات الفرعية المطابقة');
    console.log('   📊 التحسن المتوقع: +30-50% في الدقة');
    console.log('=' .repeat(70));
    
    const checker = new PlagiarismChecker();
    const results = [];
    
    // ملفات الاختبار المعايرة
    const testFiles = [
        { file: 'test_calibrated_20_percent.txt', expected: 20, description: 'نص مع 20% عبارات مرجعية' },
        { file: 'test_calibrated_60_percent.txt', expected: 60, description: 'نص مع 60% عبارات مرجعية' },
        { file: 'test_calibrated_90_percent.txt', expected: 90, description: 'نص مع 90% عبارات مرجعية' }
    ];
    
    for (const testCase of testFiles) {
        console.log(`\n📄 اختبار: ${testCase.file}`);
        console.log(`📋 ${testCase.description} - متوقع: ${testCase.expected}%`);
        
        const filePath = path.join(__dirname, 'test-files', testCase.file);
        
        if (!fs.existsSync(filePath)) {
            console.log(`❌ الملف غير موجود`);
            continue;
        }
        
        try {
            const startTime = Date.now();
            
            // تشغيل الفحص بدون Gemini لتوفير الوقت
            checker.aiDetector.isEnabled = false;
            
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const processingTime = endTime - startTime;
            const difference = Math.abs(result.plagiarismPercentage - testCase.expected);
            const accuracy = Math.max(0, 100 - (difference / testCase.expected) * 100);
            const passed = accuracy >= 85; // معيار عالي للخوارزمية المتقدمة
            
            console.log(`   ✅ النتيجة: ${result.plagiarismPercentage}%`);
            console.log(`   📈 الدقة: ${accuracy.toFixed(1)}%`);
            console.log(`   ⏱️ الوقت: ${processingTime}ms`);
            console.log(`   🎯 الخطر: ${result.riskLevel.label}`);
            console.log(`   🔍 أجزاء مطابقة: ${result.suspiciousSegments ? result.suspiciousSegments.length : 0}`);
            console.log(`   ${passed ? '✅' : '❌'} ${passed ? 'نجح' : 'فشل'} (فرق: ${difference.toFixed(1)}%)`);
            
            // عرض تفاصيل التحليل المتقدم
            if (result.detailedAnalysis) {
                console.log(`   🔬 تحليل متقدم:`);
                console.log(`      إجمالي المراجع: ${result.detailedAnalysis.totalReferences}`);
                console.log(`      تشابه عالي: ${result.detailedAnalysis.highSimilarityCount}`);
                console.log(`      أقصى تشابه: ${(result.maxSimilarity * 100).toFixed(1)}%`);
                console.log(`      متوسط التشابه: ${(result.avgSimilarity * 100).toFixed(1)}%`);
            }
            
            // عرض أفضل المطابقات
            if (result.detailedAnalysis && result.detailedAnalysis.topMatches) {
                console.log(`   🏆 أفضل 3 مطابقات:`);
                result.detailedAnalysis.topMatches.slice(0, 3).forEach((match, index) => {
                    console.log(`      ${index + 1}. ${(match.combinedSimilarity * 100).toFixed(1)}% - "${match.referenceText.substring(0, 40)}..."`);
                });
            }
            
            // عرض أمثلة على الأجزاء المطابقة
            if (result.matchedSegments && result.matchedSegments.length > 0) {
                console.log(`   📝 أمثلة على الأجزاء المطابقة:`);
                result.matchedSegments.slice(0, 3).forEach((segment, index) => {
                    console.log(`      ${index + 1}. "${segment.text.substring(0, 50)}..." (${(segment.similarity * 100).toFixed(1)}%)`);
                });
            }
            
            results.push({
                file: testCase.file,
                expected: testCase.expected,
                actual: result.plagiarismPercentage,
                accuracy: accuracy,
                processingTime: processingTime,
                maxSimilarity: result.maxSimilarity,
                avgSimilarity: result.avgSimilarity,
                matchedCount: result.matchedSegments ? result.matchedSegments.length : 0,
                highSimilarityCount: result.detailedAnalysis ? result.detailedAnalysis.highSimilarityCount : 0,
                passed: passed
            });
            
        } catch (error) {
            console.log(`   ❌ خطأ: ${error.message}`);
            console.error(error.stack);
            results.push({
                file: testCase.file,
                error: error.message,
                passed: false
            });
        }
    }
    
    // تحليل النتائج
    console.log('\n' + '=' .repeat(70));
    console.log('📊 تحليل نتائج الخوارزمية المتقدمة');
    console.log('=' .repeat(70));
    
    const validResults = results.filter(r => !r.error);
    const passedTests = validResults.filter(r => r.passed);
    
    if (validResults.length > 0) {
        const avgAccuracy = validResults.reduce((sum, r) => sum + r.accuracy, 0) / validResults.length;
        const avgTime = validResults.reduce((sum, r) => sum + r.processingTime, 0) / validResults.length;
        const avgMaxSimilarity = validResults.reduce((sum, r) => sum + (r.maxSimilarity || 0), 0) / validResults.length;
        const avgMatchedCount = validResults.reduce((sum, r) => sum + r.matchedCount, 0) / validResults.length;
        const successRate = (passedTests.length / validResults.length) * 100;
        
        console.log(`📈 النتائج مع الخوارزمية المتقدمة:`);
        console.log(`   متوسط الدقة: ${avgAccuracy.toFixed(1)}% (كان 51.7%)`);
        console.log(`   معدل النجاح: ${successRate.toFixed(1)}% (كان 33.3%)`);
        console.log(`   متوسط وقت المعالجة: ${avgTime.toFixed(0)}ms`);
        console.log(`   متوسط أقصى تشابه: ${(avgMaxSimilarity * 100).toFixed(1)}%`);
        console.log(`   متوسط الأجزاء المطابقة: ${avgMatchedCount.toFixed(1)}`);
        
        // مقارنة مع النتائج السابقة
        const accuracyImprovement = avgAccuracy - 51.7;
        const successImprovement = successRate - 33.3;
        
        console.log(`\n📊 التحسن المحقق:`);
        console.log(`   تحسن الدقة: ${accuracyImprovement > 0 ? '+' : ''}${accuracyImprovement.toFixed(1)}%`);
        console.log(`   تحسن معدل النجاح: ${successImprovement > 0 ? '+' : ''}${successImprovement.toFixed(1)}%`);
        
        // تقييم التحسن
        if (avgAccuracy >= 95 && successRate >= 90) {
            console.log(`\n🎉 هدف محقق! الخوارزمية المتقدمة وصلت للهدف المطلوب!`);
        } else if (avgAccuracy >= 85 && successRate >= 70) {
            console.log(`\n🎯 تحسن ممتاز! الخوارزمية المتقدمة قريبة جداً من الهدف`);
        } else if (avgAccuracy >= 70 && successRate >= 50) {
            console.log(`\n✅ تحسن كبير! الخوارزمية المتقدمة في الاتجاه الصحيح`);
        } else if (avgAccuracy > 51.7) {
            console.log(`\n📈 تحسن ملحوظ! الخوارزمية المتقدمة تحسن الدقة`);
        } else {
            console.log(`\n⚠️ تحسن محدود! نحتاج مزيد من التطوير`);
        }
        
        // تفاصيل كل ملف
        console.log(`\n📋 تفاصيل النتائج:`);
        validResults.forEach(result => {
            const grade = result.accuracy >= 95 ? '🎯 ممتاز' : 
                         result.accuracy >= 85 ? '✅ جيد جداً' : 
                         result.accuracy >= 70 ? '📈 جيد' : '⚠️ يحتاج عمل';
            console.log(`   ${result.file}: ${result.actual}% (دقة: ${result.accuracy.toFixed(1)}%) ${grade}`);
        });
        
        // تقييم تحقيق الأهداف
        console.log(`\n🎯 تقييم تحقيق الأهداف:`);
        console.log(`   دقة 95%+: ${avgAccuracy >= 95 ? '✅' : '❌'} (${avgAccuracy.toFixed(1)}%)`);
        console.log(`   معدل نجاح 90%+: ${successRate >= 90 ? '✅' : '❌'} (${successRate.toFixed(1)}%)`);
        console.log(`   أداء < 1000ms: ${avgTime < 1000 ? '✅' : '❌'} (${avgTime.toFixed(0)}ms)`);
        
        const targetsAchieved = [
            avgAccuracy >= 95,
            successRate >= 90,
            avgTime < 1000
        ].filter(Boolean).length;
        
        console.log(`   الأهداف المحققة: ${targetsAchieved}/3`);
        
        // حفظ النتائج
        const reportData = {
            timestamp: new Date().toISOString(),
            test_type: 'advanced_algorithm_test',
            algorithm_features: {
                exact_substring_weight: 35,
                fuzzy_matching_weight: 25,
                semantic_similarity_weight: 20,
                structural_similarity_weight: 20,
                advanced_preprocessing: true,
                phrase_extraction: true,
                fingerprint_analysis: true
            },
            results: {
                avgAccuracy: avgAccuracy,
                successRate: successRate,
                avgTime: avgTime,
                avgMaxSimilarity: avgMaxSimilarity * 100,
                avgMatchedCount: avgMatchedCount,
                targetsAchieved: targetsAchieved,
                improvement: {
                    accuracy: accuracyImprovement,
                    successRate: successImprovement
                }
            },
            detailed_results: results
        };
        
        fs.writeFileSync('advanced_algorithm_report.json', JSON.stringify(reportData, null, 2));
        console.log(`\n💾 تم حفظ التقرير: advanced_algorithm_report.json`);
        
        return {
            avgAccuracy,
            successRate,
            avgTime,
            targetsAchieved,
            improvement: accuracyImprovement
        };
        
    } else {
        console.log('❌ لا توجد نتائج صحيحة للتحليل');
        return null;
    }
}

// تشغيل الاختبار
async function main() {
    try {
        const results = await testAdvancedAlgorithm();
        
        if (results) {
            console.log('\n🎯 الخلاصة:');
            console.log(`   الدقة الجديدة: ${results.avgAccuracy.toFixed(1)}%`);
            console.log(`   معدل النجاح: ${results.successRate.toFixed(1)}%`);
            console.log(`   التحسن: ${results.improvement > 0 ? '+' : ''}${results.improvement.toFixed(1)}%`);
            console.log(`   الأداء: ${results.avgTime.toFixed(0)}ms`);
            console.log(`   الأهداف المحققة: ${results.targetsAchieved}/3`);
            
            if (results.targetsAchieved === 3) {
                console.log('🎉 تم تحقيق جميع الأهداف! الخوارزمية المتقدمة ناجحة!');
            } else if (results.avgAccuracy >= 90) {
                console.log('🎯 دقة ممتازة! قريب جداً من الهدف النهائي');
            } else if (results.improvement > 30) {
                console.log('📈 تحسن كبير! الخوارزمية المتقدمة فعالة');
            } else {
                console.log('🔧 نحتاج مزيد من التحسينات');
            }
        }
        
    } catch (error) {
        console.error('❌ خطأ:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { testAdvancedAlgorithm };
