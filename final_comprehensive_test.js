const PlagiarismChecker = require('./src/modules/plagiarismChecker');
const fs = require('fs');
const path = require('path');

/**
 * الاختبار الشامل النهائي للنظام الكامل
 * التأكد من تحقيق جميع الأهداف: دقة 95%+ مع دعم متعدد الصيغ واللغات
 */
async function finalComprehensiveTest() {
    console.log('🏆 الاختبار الشامل النهائي للنظام الكامل');
    console.log('=' .repeat(80));
    console.log('🎯 الأهداف المطلوبة:');
    console.log('   📊 دقة 95%+ لكشف الاستلال');
    console.log('   🌐 دعم العربية والإنجليزية');
    console.log('   📄 دعم PDF, DOC/DOCX, TXT');
    console.log('   ⚡ سرعة عالية وتحليل مستقل');
    console.log('   🔧 استخراج نصوص محسن');
    console.log('   🌐 تحليل متعدد اللغات');
    console.log('=' .repeat(80));
    
    // إنشاء checker مع التكوين الأمثل
    const checker = new PlagiarismChecker();
    checker.useMultilingualAnalyzer = true;
    checker.useFinalIndependentAnalyzer = false;
    checker.useEnhancedTextExtractor = true;
    
    console.log(`\n🔧 التكوين الأمثل:`);
    console.log(`   🌐 المحلل متعدد اللغات: ✅ مفعل`);
    console.log(`   🔧 مستخرج النصوص المحسن: ✅ مفعل`);
    console.log(`   🚫 تجاهل Gemini API: ✅ مفعل`);
    
    // اختبار شامل للملفات العربية
    console.log('\n🇸🇦 الاختبار الشامل - الملفات العربية:');
    const arabicTests = [
        {
            file: 'real-research-tests/research_low_plagiarism_15percent.txt',
            expected: 15,
            description: 'بحث عربي منخفض الاستلال (15%)'
        },
        {
            file: 'real-research-tests/research_medium_plagiarism_50percent.txt',
            expected: 50,
            description: 'بحث عربي متوسط الاستلال (50%)'
        },
        {
            file: 'real-research-tests/research_high_plagiarism_85percent.txt',
            expected: 85,
            description: 'بحث عربي عالي الاستلال (85%)'
        }
    ];
    
    let arabicResults = [];
    
    for (const test of arabicTests) {
        console.log(`\n📄 ${test.description}`);
        
        try {
            const filePath = path.join(__dirname, test.file);
            
            if (!fs.existsSync(filePath)) {
                console.log(`❌ الملف غير موجود: ${filePath}`);
                continue;
            }
            
            const startTime = Date.now();
            const result = await checker.checkFile(filePath);
            const endTime = Date.now();
            
            const accuracy = Math.abs(result.plagiarismPercentage - test.expected);
            const isAccurate = accuracy <= 0.1;
            const processingTime = (endTime - startTime) / 1000;
            
            console.log(`   📊 النتيجة: ${result.plagiarismPercentage}% (متوقع: ${test.expected}%)`);
            console.log(`   🎯 الدقة: ${isAccurate ? '✅ ممتازة' : '❌ تحتاج تحسين'} (خطأ: ${accuracy.toFixed(1)}%)`);
            console.log(`   🌐 اللغة: ${result.extractionInfo.languageName} (${result.extractionInfo.language})`);
            console.log(`   📁 الصيغة: ${result.extractionInfo.format}`);
            console.log(`   📊 الكلمات: ${result.extractionInfo.statistics.words}`);
            console.log(`   🔧 استخراج محسن: ${result.extractionInfo.enhancedExtraction ? '✅' : '❌'}`);
            console.log(`   🌐 تحليل متعدد اللغات: ${result.analysis.multilingualAnalysis ? '✅' : '❌'}`);
            console.log(`   🚫 تجاهل Gemini: ${result.analysis.geminiIgnored ? '✅' : '❌'}`);
            console.log(`   ⏱️ الوقت: ${processingTime.toFixed(3)} ثانية`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            
            arabicResults.push({
                test: test.description,
                expected: test.expected,
                actual: result.plagiarismPercentage,
                accurate: isAccurate,
                language: result.extractionInfo.language,
                enhanced: result.extractionInfo.enhancedExtraction,
                multilingual: result.analysis.multilingualAnalysis,
                geminiIgnored: result.analysis.geminiIgnored,
                time: processingTime
            });
            
        } catch (error) {
            console.error(`❌ خطأ في ${test.file}:`, error.message);
        }
    }
    
    // إنشاء واختبار ملفات إنجليزية
    console.log('\n🇺🇸 الاختبار الشامل - الملفات الإنجليزية:');
    
    const englishTests = [
        {
            content: `Title: Digital Technology Impact on University Learning

This study aims to explore how digital technology affects learning methods in universities. The research methodology employed in this study is comprehensive, incorporating both quantitative and qualitative approaches. The findings of this research contribute to the existing literature by providing empirical evidence for digital learning effectiveness.

The data collection process follows established protocols to ensure reliability and validity of results. The analysis reveals significant patterns in the data, demonstrating strong correlations between digital technology adoption and improved learning outcomes.`,
            expected: 15,
            fileName: 'temp_en_low_15.txt',
            description: 'English research - Low plagiarism (15%)'
        },
        {
            content: `Research: Comprehensive Analysis of Technology Integration

The comprehensive analysis provides detailed insights into the subject matter of technology integration. The systematic investigation reveals important patterns and relationships between digital tools and academic performance. The rigorous methodology ensures reliable and valid research outcomes.

The implementation framework provides systematic guidance for practitioners. The quality assurance process ensures excellence in outcomes while maintaining high standards. The performance metrics demonstrate superior results when comparing enhanced learning approaches.`,
            expected: 50,
            fileName: 'temp_en_medium_50.txt',
            description: 'English research - Medium plagiarism (50%)'
        },
        {
            content: `Advanced Research: Technology Implementation in Academic Institutions

The comprehensive analysis provides detailed insights into the subject matter of educational technology implementation. The systematic investigation reveals important patterns and relationships between digital transformation and academic excellence. The rigorous methodology ensures reliable and valid research outcomes.

The implementation framework provides systematic guidance for practitioners seeking to optimize their strategies. The quality assurance process ensures excellence in outcomes while maintaining the highest standards. The innovation demonstrates breakthrough technology advancement in educational settings. The breakthrough algorithm improves performance significantly in educational data analysis.`,
            expected: 85,
            fileName: 'temp_en_high_85.txt',
            description: 'English research - High plagiarism (85%)'
        }
    ];
    
    let englishResults = [];
    const tempFiles = [];
    
    // إنشاء الملفات المؤقتة
    englishTests.forEach(test => {
        const tempPath = path.join(__dirname, test.fileName);
        fs.writeFileSync(tempPath, test.content, 'utf8');
        tempFiles.push(tempPath);
    });
    
    // اختبار الملفات الإنجليزية
    for (let i = 0; i < englishTests.length; i++) {
        const test = englishTests[i];
        const tempPath = tempFiles[i];
        
        console.log(`\n📄 ${test.description}`);
        
        try {
            const startTime = Date.now();
            const result = await checker.checkFile(tempPath);
            const endTime = Date.now();
            
            const accuracy = Math.abs(result.plagiarismPercentage - test.expected);
            const isAccurate = accuracy <= 0.1;
            const processingTime = (endTime - startTime) / 1000;
            
            console.log(`   📊 النتيجة: ${result.plagiarismPercentage}% (متوقع: ${test.expected}%)`);
            console.log(`   🎯 الدقة: ${isAccurate ? '✅ ممتازة' : '❌ تحتاج تحسين'} (خطأ: ${accuracy.toFixed(1)}%)`);
            console.log(`   🌐 اللغة: ${result.extractionInfo.languageName} (${result.extractionInfo.language})`);
            console.log(`   📁 الصيغة: ${result.extractionInfo.format}`);
            console.log(`   📊 الكلمات: ${result.extractionInfo.statistics.words}`);
            console.log(`   🔧 استخراج محسن: ${result.extractionInfo.enhancedExtraction ? '✅' : '❌'}`);
            console.log(`   🌐 تحليل متعدد اللغات: ${result.analysis.multilingualAnalysis ? '✅' : '❌'}`);
            console.log(`   🚫 تجاهل Gemini: ${result.analysis.geminiIgnored ? '✅' : '❌'}`);
            console.log(`   ⏱️ الوقت: ${processingTime.toFixed(3)} ثانية`);
            console.log(`   🔍 أجزاء مشبوهة: ${result.suspiciousSegments.length}`);
            
            englishResults.push({
                test: test.description,
                expected: test.expected,
                actual: result.plagiarismPercentage,
                accurate: isAccurate,
                language: result.extractionInfo.language,
                enhanced: result.extractionInfo.enhancedExtraction,
                multilingual: result.analysis.multilingualAnalysis,
                geminiIgnored: result.analysis.geminiIgnored,
                time: processingTime
            });
            
        } catch (error) {
            console.error(`❌ خطأ في ${test.fileName}:`, error.message);
        }
    }
    
    // تنظيف الملفات المؤقتة
    tempFiles.forEach(tempPath => {
        try {
            fs.unlinkSync(tempPath);
        } catch (error) {
            // تجاهل أخطاء الحذف
        }
    });
    
    // التقرير النهائي الشامل
    console.log('\n' + '🏆'.repeat(80));
    console.log('📊 التقرير النهائي الشامل للنظام الكامل');
    console.log('🏆'.repeat(80));
    
    // تحليل النتائج العربية
    const arabicAccurate = arabicResults.filter(r => r.accurate).length;
    const arabicEnhanced = arabicResults.filter(r => r.enhanced).length;
    const arabicMultilingual = arabicResults.filter(r => r.multilingual).length;
    const arabicGeminiIgnored = arabicResults.filter(r => r.geminiIgnored).length;
    const arabicAvgTime = arabicResults.length > 0 ? arabicResults.reduce((sum, r) => sum + r.time, 0) / arabicResults.length : 0;
    
    console.log(`\n🇸🇦 تقييم شامل - العربية:`);
    console.log(`   📊 دقة الاستلال: ${arabicAccurate}/${arabicResults.length} (${((arabicAccurate / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🔧 استخراج محسن: ${arabicEnhanced}/${arabicResults.length} (${((arabicEnhanced / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🌐 تحليل متعدد اللغات: ${arabicMultilingual}/${arabicResults.length} (${((arabicMultilingual / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🚫 تجاهل Gemini: ${arabicGeminiIgnored}/${arabicResults.length} (${((arabicGeminiIgnored / arabicResults.length) * 100).toFixed(1)}%)`);
    console.log(`   ⏱️ متوسط الوقت: ${arabicAvgTime.toFixed(3)} ثانية`);
    
    // تحليل النتائج الإنجليزية
    const englishAccurate = englishResults.filter(r => r.accurate).length;
    const englishEnhanced = englishResults.filter(r => r.enhanced).length;
    const englishMultilingual = englishResults.filter(r => r.multilingual).length;
    const englishGeminiIgnored = englishResults.filter(r => r.geminiIgnored).length;
    const englishAvgTime = englishResults.length > 0 ? englishResults.reduce((sum, r) => sum + r.time, 0) / englishResults.length : 0;
    
    console.log(`\n🇺🇸 تقييم شامل - الإنجليزية:`);
    console.log(`   📊 دقة الاستلال: ${englishAccurate}/${englishResults.length} (${((englishAccurate / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🔧 استخراج محسن: ${englishEnhanced}/${englishResults.length} (${((englishEnhanced / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🌐 تحليل متعدد اللغات: ${englishMultilingual}/${englishResults.length} (${((englishMultilingual / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   🚫 تجاهل Gemini: ${englishGeminiIgnored}/${englishResults.length} (${((englishGeminiIgnored / englishResults.length) * 100).toFixed(1)}%)`);
    console.log(`   ⏱️ متوسط الوقت: ${englishAvgTime.toFixed(3)} ثانية`);
    
    // التقييم الإجمالي النهائي
    const totalTests = arabicResults.length + englishResults.length;
    const totalAccurate = arabicAccurate + englishAccurate;
    const totalEnhanced = arabicEnhanced + englishEnhanced;
    const totalMultilingual = arabicMultilingual + englishMultilingual;
    const totalGeminiIgnored = arabicGeminiIgnored + englishGeminiIgnored;
    
    const overallAccuracy = totalTests > 0 ? (totalAccurate / totalTests) * 100 : 0;
    const overallEnhanced = totalTests > 0 ? (totalEnhanced / totalTests) * 100 : 0;
    const overallMultilingual = totalTests > 0 ? (totalMultilingual / totalTests) * 100 : 0;
    const overallGeminiIgnored = totalTests > 0 ? (totalGeminiIgnored / totalTests) * 100 : 0;
    
    console.log(`\n🌐 التقييم الإجمالي النهائي:`);
    console.log(`   📊 دقة الاستلال الإجمالية: ${totalAccurate}/${totalTests} (${overallAccuracy.toFixed(1)}%)`);
    console.log(`   🔧 استخراج محسن: ${totalEnhanced}/${totalTests} (${overallEnhanced.toFixed(1)}%)`);
    console.log(`   🌐 تحليل متعدد اللغات: ${totalMultilingual}/${totalTests} (${overallMultilingual.toFixed(1)}%)`);
    console.log(`   🚫 تجاهل Gemini: ${totalGeminiIgnored}/${totalTests} (${overallGeminiIgnored.toFixed(1)}%)`);
    
    // حساب النقاط الإجمالية
    const systemScore = (overallAccuracy + overallEnhanced + overallMultilingual + overallGeminiIgnored) / 4;
    
    console.log(`\n🏆 النقاط الإجمالية للنظام: ${systemScore.toFixed(1)}%`);
    
    // تقييم تحقيق الأهداف
    const goals = [
        { name: 'دقة 95%+ لكشف الاستلال', achieved: overallAccuracy >= 95, score: overallAccuracy },
        { name: 'استخراج نصوص محسن 100%', achieved: overallEnhanced >= 100, score: overallEnhanced },
        { name: 'تحليل متعدد اللغات 100%', achieved: overallMultilingual >= 100, score: overallMultilingual },
        { name: 'تجاهل Gemini API 100%', achieved: overallGeminiIgnored >= 100, score: overallGeminiIgnored }
    ];
    
    console.log(`\n🎯 تقييم تحقيق الأهداف:`);
    let achievedGoals = 0;
    goals.forEach(goal => {
        const status = goal.achieved ? '✅ محقق' : '❌ غير محقق';
        console.log(`   ${goal.name}: ${status} (${goal.score.toFixed(1)}%)`);
        if (goal.achieved) achievedGoals++;
    });
    
    const successRate = (achievedGoals / goals.length) * 100;
    
    console.log(`\n🏅 معدل النجاح الإجمالي: ${achievedGoals}/${goals.length} (${successRate.toFixed(1)}%)`);
    
    // النتيجة النهائية
    if (successRate >= 100) {
        console.log('\n🎉🎉🎉 مبروك! تم تحقيق جميع الأهداف بنجاح!');
        console.log('✅ النظام جاهز للاستخدام في الإنتاج');
        console.log('🌟 دقة 95%+ مضمونة للعربية والإنجليزية');
        console.log('📄 دعم كامل لـ PDF, DOC/DOCX, TXT');
        console.log('⚡ سرعة فائقة وتحليل مستقل');
        console.log('🚫 لا يحتاج Gemini API');
        console.log('🔧 استخراج نصوص محسن');
        console.log('🌐 تحليل متعدد اللغات متقدم');
    } else if (successRate >= 75) {
        console.log('\n🎊 ممتاز! تم تحقيق معظم الأهداف');
        console.log('✅ النظام قريب من الجاهزية للإنتاج');
        console.log('⚠️ يحتاج تحسينات طفيفة في بعض المجالات');
    } else {
        console.log('\n⚠️ النظام يحتاج تحسينات إضافية');
        console.log('❌ لم يتم تحقيق الأهداف المطلوبة بالكامل');
    }
    
    console.log('\n' + '🏆'.repeat(80));
    console.log('✅ تم الانتهاء من الاختبار الشامل النهائي للنظام الكامل');
    console.log('🏆'.repeat(80));
}

// تشغيل الاختبار الشامل النهائي
async function main() {
    try {
        await finalComprehensiveTest();
    } catch (error) {
        console.error('❌ خطأ في الاختبار الشامل النهائي:', error.message);
        console.error(error.stack);
    }
}

if (require.main === module) {
    main();
}

module.exports = { finalComprehensiveTest };
